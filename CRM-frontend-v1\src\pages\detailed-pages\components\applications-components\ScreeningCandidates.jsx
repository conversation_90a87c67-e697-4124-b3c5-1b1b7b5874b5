import CandidateCarouselCards from 'components/CandidateCarouselCards';
import React, { useCallback, useEffect, useState } from 'react';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import Whatsapp from '../chats/Whatsapp';
import SMS from '../chats/SMS';
import UpdateResponseDialog from '../chats/UpdateResponseDialog';
import UpdateEmailResponse from '../chats/UpdateEmailResponse';
import VoiceAppTwillio from '../chats/VoiceCallApp';
import { message } from 'antd';
import { API_URLS } from 'constants/apiUrls';
import { Get, Put } from 'actions/API/apiActions';
import { json, useParams } from 'react-router';
import { set } from 'lodash';

const responsive = {
  superLargeDesktop: {
    breakpoint: { max: 4000, min: 1200 },
    items: 4
  },
  desktop: {
    breakpoint: { max: 1200, min: 1024 },
    items: 3
  },
  tablet: {
    breakpoint: { max: 1024, min: 768 },
    items: 2
  },
  mobile: {
    breakpoint: { max: 768, min: 0 },
    items: 1
  }
};

const data = [
  {
    id: 1,
    title: 'New Candidates',
    color: '#FF9500',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'New'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'New'
      },
      {
        id: 3,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'New'
      },
      {
        id: 4,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'New'
      },
      {
        id: 5,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'New'
      },
      {
        id: 6,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'New'
      }
    ]
  },
  {
    id: 2,
    title: 'Email',
    color: '#00C7BE',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Email Sent'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Email Sent'
      },
      {
        id: 3,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Email Sent'
      }
    ]
  },
  {
    id: 3,
    title: 'Email Response',
    color: '#5856D6',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested',
        response_count: '1'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Not Interested',
        response_count: '1'
      }
    ]
  },
  {
    id: 4,
    title: 'No Call Replies X1',
    color: '#32ADE6',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'No Response'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'No Response'
      },
      {
        id: 3,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'No Response'
      }
    ]
  },
  {
    id: 5,
    title: 'WhatsApp',
    color: '#34C759',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Msg Sent'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Msg Sent'
      }
    ]
  },
  {
    id: 6,
    title: 'Mobile Msg',
    color: '#AF52DE',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Msg Sent'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Msg Sent'
      }
    ]
  },
  {
    id: 7,
    title: 'WhatsApp & Mobile Reply',
    color: '#A2845E',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested',
        response_count: '1'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Not Interested'
      },
      {
        id: 3,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested',
        response_count: '5'
      }
    ]
  },
  {
    id: 8,
    title: 'No Call Replies X2',
    color: '#E70F38',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'No Response'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'No Response'
      }
    ]
  },
  {
    id: 9,
    title: 'LI  Connection',
    color: '#EE8C3C',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Conn Sent'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Conn not sent'
      }
    ]
  },
  {
    id: 10,
    title: 'LI Connection Response',
    color: '#FFCC00',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Not Interested'
      },
      {
        id: 3,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested'
      }
    ]
  },
  {
    id: 11,
    title: 'No Call Replies X3',
    color: '#EE8C3C',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'No Response'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'No Response'
      }
    ]
  },
  {
    id: 12,
    title: 'LI InMail',
    color: '#34C759',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'InMail Sent'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'InMail Sent'
      }
    ]
  },
  {
    id: 13,
    title: 'InMail Response',
    color: '#F688F2',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'Interested'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'Not Interested'
      }
    ]
  },
  {
    id: 14,
    title: 'No Call Replies X4',
    color: '#34C759',
    candidates: [
      {
        id: 1,
        name: 'Jone Doe',
        replyTime: '2 days ago',
        status: 'No Response'
      },
      {
        id: 2,
        name: 'Janie johne',
        replyTime: '2 days ago',
        status: 'No Response'
      }
    ]
  }
];

function ScreeningCandidates() {
  const [openWhatsappDialog, setOpenWhatsappDialog] = useState(false);
  const [openVoiceAppDialog, setOpenVoiceAppDialog] = useState(false);
  const [openSMSDialog, setOpenSMSDialog] = useState(false);
  const [openUpdateLogResponseDialog, setOpenUpdateLogResponseDialog] = useState(false);
  const [openEmailStatusDialog, setOpenEmailStatusDialog] = useState(false);
  const [candidateNumber, setCandidateNumber] = useState(null);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [chennel, setChannel] = useState(null);

  const { roleId } = useParams();
  const [formattedCandidates, setFormattedCandidates] = useState([]);
  const getRoleApplicants = useCallback(() => {
    try {
      Get(
        {},
        API_URLS.get360AndPreQualificationCandidates.replace(':roleId', roleId),
        (resp) => {
          const temp = resp || [];
          const candidates = temp.map((item) => {
            return {
              role_candidate_id: item.id,
              id: item.candidate.id,
              name: item.candidate.first_name + ' ' + item.candidate.last_name || 'N/A',
              replyTime: item.updated_at.split('T')[0] || 'N/A',
              stage: item.stage || 'N/A',
              screening_stage: item.screening_stage || 'N/A',
              phones: item.candidate.phones || [],
              candidate: item.candidate || {},
              emails: item.candidate.emails || []
            };
          });
  console.log("jhsdfgdsfdsf", candidates)
          const formattedData = [
            {
              id: 1,
              title: 'New Candidates',
              color: '#FF9500',
              candidates: candidates
            },
            {
              id: 2,
              title: 'Email',
              color: '#00C7BE',
              candidates: candidates.filter((candidate) => candidate.stage === 'Email Sent')
            },
            {
              id: 3,
              title: 'Email Response',
              color: '#5856D6',
              candidates: candidates.filter((candidate) => candidate.stage === 'Interested' || candidate.stage === 'Not Interested')
            },
            {
              id: 4,
              title: 'No Call Replies X1',
              color: '#32ADE6',
              candidates: candidates.filter((candidate) => candidate.screening_stage === 'NCR')
            },
            {
              id: 5,
              title: 'WhatsApp',
              color: '#34C759',
              candidates: candidates.filter((candidate) => candidate.stage === 'Msg Sent')
            },
            {
              id: 6,
              title: 'Mobile Msg',
              color: '#AF52DE',
              candidates: candidates.filter((candidate) => candidate.stage === 'Msg Sent')
            },
            {
              id: 7,
              title: 'WhatsApp & Mobile Reply',
              color: '#A2845E',
              candidates: candidates.filter((candidate) => candidate.stage === 'Interested' || candidate.stage === 'Not Interested')
            },
            {
              id: 8,
              title: 'No Call Replies X2',
              color: '#E70F38',
              candidates: candidates.filter((candidate) => candidate.stage === 'No Response')
            },
            {
              id: 9,
              title: 'LI Connection',
              color: '#EE8C3C',
              candidates: candidates.filter((candidate) => candidate.stage === 'Conn Sent' || candidate.stage === 'Conn not sent')
            },
            {
              id: 10,
              title: 'LI Connection Response',
              color: '#FFCC00',
              candidates: candidates.filter((candidate) => candidate.stage === 'Interested' || candidate.stage === 'Not Interested')
            },
            {
              id: 11,
              title: 'No Call Replies X3',
              color: '#EE8C3C',
              candidates: candidates.filter((candidate) => candidate.stage === 'No Response')
            },
            {
              id: 12,
              title: 'LI InMail',
              color: '#34C759',
              candidates: candidates.filter((candidate) => candidate.stage === 'InMail Sent')
            },
            {
              id: 13,
              title: 'InMail Response',
              color: '#F688F2',
              candidates: candidates.filter((candidate) => candidate.stage === 'Interested' || candidate.stage === 'Not Interested')
            },
            {
              id: 14,
              title: 'No Call Replies X4',
              color: '#34C759',
              candidates: candidates.filter((candidate) => candidate.stage === 'No Response')
            }
          ];
          setFormattedCandidates(formattedData);
        },
        (error) => {
          message.error(error?.response?.message || 'Failed to get role applications. Try refreshing the page!');
        }
      );
    } catch (error) {
      message.error('Failed to get role applications. Try refreshing the page!');
    }
  }, [roleId]);
  useEffect(() => {
    getRoleApplicants();
  }, []);

  const handleUpdateResponse = (response) => {
    const payload = {};
    switch (response?.group) {
      case 'screening_stage':
        payload.screening_stage = response?.response;
        break;
      case 'partially_interested_stage':
        payload.partially_interested_stage = response?.response;
        break;
      case 'submission_stage':
        payload.submission_stage = response?.response;
        break;
    }

    payload.chennel = chennel;
    payload.role_candidate_id = selectedCandidate?.role_candidate_id;
    console.log('Response:', payload);
    try {
      Put(
        payload,
        API_URLS.updateRoleCandidateStage,
        (resp) => {
          message.success('Response updated successfully!');
          getRoleApplicants();
        },
        (error) => {
          message.error(error?.response?.message || 'Failed to update response. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to update response. Try again!');
    }
  };

  return (
    <div style={{ width: '100%', marginTop: '20px', height: '100vh' }}>
      <Carousel
        responsive={responsive}
        arrows={false}
        autoPlay={false}
        keyBoardControl
        containerClass="carousel-container"
        removeArrowOnDeviceType={['tablet', 'mobile']}
      >
        {formattedCandidates?.map((candidate) => (
          <div key={candidate.id} style={{ height: '100%', borderRight: '2px solid #CED0DA', cursor: 'pointer', userSelect: 'none' }}>
            <CandidateCarouselCards
              title={candidate.title}
              candidates={candidate.candidates}
              color={candidate.color}
              showActions={true}
              onWhatsappClick={(c) => {
                setSelectedCandidate(c);
                console.log('Candidate:', c);
                if (c.phones?.length === 0) {
                  alert('No phone number available for this candidate.');
                  message.error('No phone number available for this candidate.');
                  return;
                } else if (c?.phones?.length > 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenWhatsappDialog(true);
                } else if (c?.phones?.length === 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenWhatsappDialog(true);
                }
              }}
              onVoiceAppClick={(c) => {
                setSelectedCandidate(c);
                console.log('Candidsdsdsdate:', c);
                if (c.phones?.length === 0) {
                  alert('No phone number available for this candidate.');
                  message.error('No phone number available for this candidate.');
                  return;
                } else if (c?.phones?.length > 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenVoiceAppDialog(true);
                } else if (c?.phones?.length === 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenVoiceAppDialog(true);
                }
              }}
              onSMSClick={(c) => {
                setSelectedCandidate(c);
                console.log('Candidate:', c);
                if (c.phones?.length === 0) {
                  alert('No phone number available for this candidate.');
                  message.error('No phone number available for this candidate.');
                  return;
                } else if (c?.phones?.length > 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenSMSDialog(true);
                } else if (c?.phones?.length === 1) {
                  setCandidateNumber(c?.phones[0]?.phone_number);
                  setOpenSMSDialog(true);
                }
              }}
              onEmailClick={() => setOpenEmailStatusDialog(true)}
              showSubmissionActions={false}
            />
          </div>
        ))}
      </Carousel>
      <br />
      {formattedCandidates?.length === 0 && (
        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <h3>No Candidates Found</h3>
          <p>Please check back later or add candidates to the role.</p>
        </div>
      )}
      {/* Dialogs for different actions */}
      {openWhatsappDialog && (
        <Whatsapp
          onClose={() => {
            setChannel('whatsapp');
            setOpenWhatsappDialog(false);
            setOpenUpdateLogResponseDialog(true);
          }}
          candidateNumber={candidateNumber}
          candidate={selectedCandidate?.candidate}
        />
      )}
      {openSMSDialog && (
        <SMS
          onClose={() => {
            setChannel('sms');
            setOpenSMSDialog(false);
            setOpenUpdateLogResponseDialog(true);
          }}
          candidateNumber={candidateNumber}
          candidate={selectedCandidate?.candidate}
          open={openSMSDialog}
        />
      )}
      {openVoiceAppDialog && (
        <VoiceAppTwillio
          open={openVoiceAppDialog}
          onClose={() => {
            setChannel('Call');
            setOpenVoiceAppDialog(false);
            setOpenUpdateLogResponseDialog(true);
          }}
          toNumber={candidateNumber}
          status={'connected'}
          candidate={selectedCandidate?.candidate}
        />
      )}
      {openUpdateLogResponseDialog && (
        <UpdateResponseDialog
          open={() => setOpenUpdateLogResponseDialog(true)}
          onClose={() => setOpenUpdateLogResponseDialog(false)}
          onSubmit={(response) => {
            handleUpdateResponse(response);
          }}
        />
      )}
      {openEmailStatusDialog && (
        <UpdateEmailResponse
          open={() => setOpenEmailStatusDialog(true)}
          onClose={() => {
            setOpenEmailStatusDialog(false);
          }}
          onSubmit={(response) => {
            handleUpdateResponse(response);
          }}
        />
      )}
    </div>
  );
}

export default ScreeningCandidates;
