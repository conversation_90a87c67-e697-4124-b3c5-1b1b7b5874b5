"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const users_module_1 = require("./users/users.module");
const logger_middleware_1 = require("./logger.middleware");
const email_module_1 = require("./email/email.module");
const cloudinary_module_1 = require("./cloudinary/cloudinary.module");
const stripe_module_1 = require("./stripe/stripe.module");
const service_module_1 = require("./service/service.module");
const s3bucket_module_1 = require("./s3bucket/s3bucket.module");
const jobs_module_1 = require("./jobs/jobs.module");
const country_module_1 = require("./country/country.module");
const sector_module_1 = require("./sector/sector.module");
const people_module_1 = require("./people/people.module");
const company_module_1 = require("./company/company.module");
const preferences_module_1 = require("./preferences/preferences.module");
const job_alerts_module_1 = require("./job-alerts/job-alerts.module");
const resume_templates_module_1 = require("./resume-templates/resume-templates.module");
const resume_module_1 = require("./resume/resume.module");
const emails_module_1 = require("./emails/emails.module");
const phone_module_1 = require("./phone/phone.module");
const skills_module_1 = require("./skills/skills.module");
const experience_module_1 = require("./experience/experience.module");
const qualifications_module_1 = require("./qualifications/qualifications.module");
const mail_box_module_1 = require("./mail-box/mail-box.module");
const roles_module_1 = require("./roles/roles.module");
const invoices_module_1 = require("./invoices/invoices.module");
const calendar_module_1 = require("./calendar/calendar.module");
const email_templates_module_1 = require("./email-templates/email-templates.module");
const focus_point_module_1 = require("./focus-point/focus-point.module");
const role_logs_module_1 = require("./role_logs/role_logs.module");
const sequence_module_1 = require("./sequence/sequence.module");
const sequence_steps_module_1 = require("./sequence-steps/sequence-steps.module");
const job_applications_module_1 = require("./job_applications/job_applications.module");
const languages_module_1 = require("./languages/languages.module");
const contact_us_module_1 = require("./contact-us/contact-us.module");
const role_candidates_module_1 = require("./role_candidates/role_candidates.module");
const voice_module_1 = require("./voice/voice.module");
const twillio_module_1 = require("./twillio/twillio.module");
const volunteer_module_1 = require("./volunteer/volunteer.module");
const projects_module_1 = require("./projects/projects.module");
const people_assignments_module_1 = require("./people-assignments/people-assignments.module");
const leads_module_1 = require("./leads/leads.module");
const scrapper_module_1 = require("./scrapper/scrapper.module");
const company_scrapper_control_module_1 = require("./company_scrapper_control/company_scrapper_control.module");
const messages_module_1 = require("./messages/messages.module");
const candidate_sequence_status_module_1 = require("./candidate-sequence-status/candidate-sequence-status.module");
const queue_module_1 = require("./queue/queue.module");
const webhooks_module_1 = require("./webhooks/webhooks.module");
const file_manager_module_1 = require("./file-manager/file-manager.module");
const website_manual_requests_module_1 = require("./website_manual_requests/website_manual_requests.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(logger_middleware_1.LoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({ isGlobal: true }),
            typeorm_1.TypeOrmModule.forRoot({
                type: process.env.DB_TYPE,
                host: process.env.DB_HOST,
                port: parseInt(process.env.DB_PORT, 10) || 5432,
                username: process.env.DB_USERNAME,
                password: process.env.DB_PASSWORD,
                database: process.env.DB_NAME,
                entities: [__dirname + '/**/*.entity{.ts,.js}'],
                synchronize: true,
                autoLoadEntities: true,
            }),
            users_module_1.UsersModule,
            cloudinary_module_1.CloudinaryModule,
            email_module_1.EmailModule,
            stripe_module_1.StripeModule,
            service_module_1.ServiceModule,
            s3bucket_module_1.S3bucketModule,
            jobs_module_1.JobsModule,
            country_module_1.CountryModule,
            sector_module_1.SectorModule,
            people_module_1.PeopleModule,
            company_module_1.CompanyModule,
            preferences_module_1.PreferencesModule,
            job_alerts_module_1.JobAlertsModule,
            resume_templates_module_1.ResumeTemplatesModule,
            resume_module_1.ResumeModule,
            emails_module_1.EmailsModule,
            phone_module_1.PhoneModule,
            skills_module_1.SkillsModule,
            experience_module_1.ExperienceModule,
            qualifications_module_1.QualificationsModule,
            mail_box_module_1.MailBoxModule,
            roles_module_1.RolesModule,
            invoices_module_1.InvoicesModule,
            calendar_module_1.CalendarModule,
            email_templates_module_1.EmailTemplatesModule,
            focus_point_module_1.FocusPointModule,
            role_logs_module_1.RoleLogsModule,
            sequence_module_1.SequenceModule,
            sequence_steps_module_1.SequenceStepsModule,
            job_applications_module_1.JobApplicationsModule,
            languages_module_1.LanguagesModule,
            contact_us_module_1.ContactUsModule,
            role_candidates_module_1.RoleCandidatesModule,
            voice_module_1.VoiceModule,
            twillio_module_1.TwillioModule,
            volunteer_module_1.VolunteerModule,
            projects_module_1.ProjectsModule,
            people_assignments_module_1.PeopleAssignmentsModule,
            leads_module_1.LeadsModule,
            scrapper_module_1.ScrapperModule,
            company_scrapper_control_module_1.CompanyScrapperControlModule,
            messages_module_1.MessagesModule,
            candidate_sequence_status_module_1.CandidateSequenceStatusModule,
            queue_module_1.QueueModule,
            webhooks_module_1.WebhooksModule,
            file_manager_module_1.FileManagerModule,
            website_manual_requests_module_1.WebsiteManualRequestsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService,],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map