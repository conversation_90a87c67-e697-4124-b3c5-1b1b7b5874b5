import { CalendarService } from './calendar.service';
import { CalendarDTO } from './dto/calendar.dto';
import { UpdateCalendarDto } from './dto/updateCalendar.dto';
export declare class CalendarController {
    private readonly calendarService;
    constructor(calendarService: CalendarService);
    createCalendar(calendar: CalendarDTO): Promise<import("./calendar.entity").Calendar>;
    updateCalendar(id: number, calendar: UpdateCalendarDto): Promise<import("./calendar.entity").Calendar>;
    getAllCalendars(page?: number, limit?: number): Promise<import("./calendar.entity").Calendar[]>;
    getCalendarsByEventType(eventType: string, userId: string, pageSize?: string): Promise<import("./calendar.entity").Calendar[]>;
    deleteCalendar(id: number): Promise<void>;
    deleteAllCalendars(): Promise<void>;
    getCalendarById(id: number): Promise<import("./calendar.entity").Calendar>;
}
