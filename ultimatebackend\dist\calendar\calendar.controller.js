"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarController = void 0;
const common_1 = require("@nestjs/common");
const calendar_service_1 = require("./calendar.service");
const swagger_1 = require("@nestjs/swagger");
const calendar_dto_1 = require("./dto/calendar.dto");
const updateCalendar_dto_1 = require("./dto/updateCalendar.dto");
let CalendarController = class CalendarController {
    constructor(calendarService) {
        this.calendarService = calendarService;
    }
    async createCalendar(calendar) {
        return this.calendarService.createCalendar(calendar);
    }
    async updateCalendar(id, calendar) {
        return this.calendarService.updateCalendar(id, calendar);
    }
    async getAllCalendars(page = 0, limit = 10) {
        return this.calendarService.getAllCalendars(page, limit);
    }
    async getCalendarsByEventType(eventType, userId, pageSize) {
        return this.calendarService.getCalendarByEventType(eventType, userId, pageSize);
    }
    async deleteCalendar(id) {
        return this.calendarService.deleteCalendar(id);
    }
    async deleteAllCalendars() {
        return this.calendarService.deleteAllCalendars();
    }
    async getCalendarById(id) {
        return this.calendarService.getCalendarById(id);
    }
};
exports.CalendarController = CalendarController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new calendar' }),
    (0, swagger_1.ApiBody)({ type: calendar_dto_1.CalendarDTO }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [calendar_dto_1.CalendarDTO]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "createCalendar", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an existing calendar' }),
    (0, swagger_1.ApiBody)({ type: updateCalendar_dto_1.UpdateCalendarDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateCalendar_dto_1.UpdateCalendarDto]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "updateCalendar", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all calendars' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "getAllCalendars", null);
__decorate([
    (0, common_1.Get)('getByEventType/:eventType/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get calendars by event type' }),
    __param(0, (0, common_1.Param)('eventType')),
    __param(1, (0, common_1.Param)('userId')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "getCalendarsByEventType", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a calendar by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "deleteCalendar", null);
__decorate([
    (0, common_1.Delete)('deleteAll'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete all calendars' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "deleteAllCalendars", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a calendar by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CalendarController.prototype, "getCalendarById", null);
exports.CalendarController = CalendarController = __decorate([
    (0, swagger_1.ApiTags)('Calendar'),
    (0, common_1.Controller)('calendar'),
    __metadata("design:paramtypes", [calendar_service_1.CalendarService])
], CalendarController);
//# sourceMappingURL=calendar.controller.js.map