import { Calendar } from './calendar.entity';
import { Repository } from 'typeorm';
import { CalendarDTO } from './dto/calendar.dto';
import { UpdateCalendarDto } from './dto/updateCalendar.dto';
export declare class CalendarService {
    private calendarRepository;
    constructor(calendarRepository: Repository<Calendar>);
    createCalendar(calendar: CalendarDTO): Promise<Calendar>;
    updateCalendar(id: number, calendar: UpdateCalendarDto): Promise<Calendar>;
    getAllCalendars(page?: number, pageSize?: number, searchString?: string): Promise<Calendar[]>;
    getCalendarById(id: number): Promise<Calendar>;
    deleteCalendar(id: number): Promise<void>;
    deleteAllCalendars(): Promise<void>;
    getCalendarByEventType(eventType: string, userId: string, pageSize?: string): Promise<Calendar[]>;
}
