"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const calendar_entity_1 = require("./calendar.entity");
const typeorm_2 = require("typeorm");
let CalendarService = class CalendarService {
    constructor(calendarRepository) {
        this.calendarRepository = calendarRepository;
    }
    async createCalendar(calendar) {
        try {
            const newCalendar = this.calendarRepository.create(calendar);
            const savedCalendar = await this.calendarRepository.insert(newCalendar);
            return savedCalendar.raw[0];
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to create calendar',
                error: error.message,
            });
        }
    }
    async updateCalendar(id, calendar) {
        try {
            const existingCalendar = await this.calendarRepository.findOne({
                where: { id },
            });
            if (!existingCalendar) {
                throw new common_1.NotFoundException('Calendar not found');
            }
            const updatedCalendar = this.calendarRepository.merge(existingCalendar, calendar);
            const savedCalendar = await this.calendarRepository.save(updatedCalendar);
            return savedCalendar;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to update calendar',
                error: error.message,
            });
        }
    }
    async getAllCalendars(page = 0, pageSize = 10, searchString = null) {
        try {
            const query = this.calendarRepository.createQueryBuilder('calendar');
            if (searchString) {
                query.where('calendar.event_name LIKE :searchString', {
                    searchString: `%${searchString}%`,
                });
            }
            query.skip(page * pageSize).take(pageSize);
            const calendars = await query.getMany();
            return calendars;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to fetch calendars',
                error: error.message,
            });
        }
    }
    async getCalendarById(id) {
        try {
            const calendar = await this.calendarRepository.findOne({
                where: { id },
            });
            if (!calendar) {
                throw new common_1.NotFoundException('Calendar not found');
            }
            return calendar;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to fetch calendar',
                error: error.message,
            });
        }
    }
    async deleteCalendar(id) {
        try {
            const result = await this.calendarRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.NotFoundException('Calendar not found');
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to delete calendar',
                error: error.message,
            });
        }
    }
    async deleteAllCalendars() {
        try {
            await this.calendarRepository.clear();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to delete all calendars',
                error: error.message,
            });
        }
    }
    async getCalendarByEventType(eventType, userId, pageSize) {
        try {
            if (pageSize) {
                const limit = parseInt(pageSize);
                const calendars = await this.calendarRepository.find({
                    where: { event_type: eventType, userId: userId },
                    relations: ['user', 'person'],
                    take: limit,
                });
                return calendars;
            }
            else {
                const calendars = await this.calendarRepository.find({
                    where: { event_type: eventType, userId: userId },
                    relations: ['user', 'person'],
                });
                return calendars;
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to fetch calendars by event type',
                error: error.message,
            });
        }
    }
};
exports.CalendarService = CalendarService;
exports.CalendarService = CalendarService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(calendar_entity_1.Calendar)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CalendarService);
//# sourceMappingURL=calendar.service.js.map