"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CalendarDTO {
}
exports.CalendarDTO = CalendarDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of calendar',
        enum: ['PERSONAL', 'WORK', 'OTHER'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['PERSONAL', 'WORK', 'OTHER']),
    __metadata("design:type", String)
], CalendarDTO.prototype, "calendar_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of event',
        enum: ['MEETING', 'INTERVIEW', 'OTHER'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['MEETING', 'INTERVIEW', 'OTHER']),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of interview',
        enum: ['ONLINE', 'ONSITE', 'PHONE', 'OTHER'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['ONLINE', 'ONSITE', 'PHONE', 'OTHER']),
    __metadata("design:type", String)
], CalendarDTO.prototype, "interview_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date of the event',
        type: 'string',
        format: 'date',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CalendarDTO.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the candidate',
        type: String,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CalendarDTO.prototype, "candidateId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the role',
        type: String,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CalendarDTO.prototype, "roleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the event',
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of client',
        enum: ['CLIENT', 'PROSPECT', 'CANDIDATE', 'OTHER'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['CLIENT', 'PROSPECT', 'CANDIDATE', 'OTHER']),
    __metadata("design:type", String)
], CalendarDTO.prototype, "client_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Source of the event',
        enum: ['ZOOM', 'GOOGLE MEETS', 'TEAMS', 'PHYSICAL', 'OTHER'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['ZOOM', 'GOOGLE MEETS', 'TEAMS', 'PHYSICAL', 'OTHER']),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_source", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Link to the event',
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_link", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location of the event',
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(0, 255),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the event',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CalendarDTO.prototype, "event_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date of the event',
        type: 'string',
        format: 'date',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CalendarDTO.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reminder date for the event',
        type: 'string',
        format: 'date',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CalendarDTO.prototype, "reminder_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the User entry',
        type: String,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CalendarDTO.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Person id ',
        example: '1',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CalendarDTO.prototype, "personId", void 0);
//# sourceMappingURL=calendar.dto.js.map