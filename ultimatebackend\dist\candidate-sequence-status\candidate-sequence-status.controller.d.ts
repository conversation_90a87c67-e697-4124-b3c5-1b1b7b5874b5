import { CandidateSequenceStatusService } from './candidate-sequence-status.service';
import { CandidateSequenceStatus } from './candidate-sequence-status.entity';
export declare class CandidateSequenceStatusController {
    private readonly candidateSequenceStatusService;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService);
    getCandidateSequenceStatus(candidateId: number, sequenceId: number): Promise<CandidateSequenceStatus[]>;
    getStepsByStatus(status: string, limit?: number): Promise<CandidateSequenceStatus[]>;
}
