"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateSequenceStatusController = void 0;
const common_1 = require("@nestjs/common");
const candidate_sequence_status_service_1 = require("./candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("./candidate-sequence-status.entity");
let CandidateSequenceStatusController = class CandidateSequenceStatusController {
    constructor(candidateSequenceStatusService) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
    }
    async getCandidateSequenceStatus(candidateId, sequenceId) {
        return await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, sequenceId);
    }
    async getStepsByStatus(status, limit = 100) {
        const statusUpper = status.toUpperCase();
        if (!Object.values(candidate_sequence_status_entity_1.SequenceStepStatus).includes(statusUpper)) {
            throw new common_1.BadRequestException(`Invalid status: ${status}. Valid statuses are: ${Object.values(candidate_sequence_status_entity_1.SequenceStepStatus).join(', ')}`);
        }
        const statusEnum = statusUpper;
        return await this.candidateSequenceStatusService.getStepsByStatus(statusEnum, limit);
    }
};
exports.CandidateSequenceStatusController = CandidateSequenceStatusController;
__decorate([
    (0, common_1.Get)('candidate/:candidateId/sequence/:sequenceId'),
    __param(0, (0, common_1.Param)('candidateId')),
    __param(1, (0, common_1.Param)('sequenceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CandidateSequenceStatusController.prototype, "getCandidateSequenceStatus", null);
__decorate([
    (0, common_1.Get)('status/:status'),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], CandidateSequenceStatusController.prototype, "getStepsByStatus", null);
exports.CandidateSequenceStatusController = CandidateSequenceStatusController = __decorate([
    (0, common_1.Controller)('candidate-sequence-status'),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService])
], CandidateSequenceStatusController);
//# sourceMappingURL=candidate-sequence-status.controller.js.map