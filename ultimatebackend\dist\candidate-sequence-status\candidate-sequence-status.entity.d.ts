import { RoleSequence } from 'src/sequence/sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
export declare enum SequenceStepStatus {
    PENDING = "PENDING",
    QUEUED = "QUEUED",
    SENT = "SENT",
    DELIVERED = "DELIVERED",
    OPENED = "OPENED",
    CLICKED = "CLICKED",
    REPLIED = "REPLIED",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    SKIPPED = "SKIPPED"
}
export declare enum StepType {
    SEQUENTIAL = "SEQUENTIAL",
    PARALLEL = "PARALLEL"
}
export declare class CandidateSequenceStatus {
    id: number;
    candidate: RoleCandidate;
    candidateId: number;
    sequence: RoleSequence;
    sequenceId: number;
    step: SequenceSteps;
    stepId: number;
    status: SequenceStepStatus;
    stepType: StepType;
    stepOrder: number;
    scheduledAt: Date;
    sentAt: Date;
    deliveredAt: Date;
    openedAt: Date;
    clickedAt: Date;
    repliedAt: Date;
    completedAt: Date;
    failedAt: Date;
    errorMessage: string;
    attemptCount: number;
    metadata: Record<string, any>;
    responseData: string;
    createdAt: Date;
    updatedAt: Date;
}
