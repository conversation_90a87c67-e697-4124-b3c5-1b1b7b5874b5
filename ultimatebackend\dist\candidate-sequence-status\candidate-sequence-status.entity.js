"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateSequenceStatus = exports.StepType = exports.SequenceStepStatus = void 0;
const sequence_entity_1 = require("../sequence/sequence.entity");
const sequence_steps_entity_1 = require("../sequence-steps/sequence_steps.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
const typeorm_1 = require("typeorm");
var SequenceStepStatus;
(function (SequenceStepStatus) {
    SequenceStepStatus["PENDING"] = "PENDING";
    SequenceStepStatus["QUEUED"] = "QUEUED";
    SequenceStepStatus["SENT"] = "SENT";
    SequenceStepStatus["DELIVERED"] = "DELIVERED";
    SequenceStepStatus["OPENED"] = "OPENED";
    SequenceStepStatus["CLICKED"] = "CLICKED";
    SequenceStepStatus["REPLIED"] = "REPLIED";
    SequenceStepStatus["COMPLETED"] = "COMPLETED";
    SequenceStepStatus["FAILED"] = "FAILED";
    SequenceStepStatus["SKIPPED"] = "SKIPPED";
})(SequenceStepStatus || (exports.SequenceStepStatus = SequenceStepStatus = {}));
var StepType;
(function (StepType) {
    StepType["SEQUENTIAL"] = "SEQUENTIAL";
    StepType["PARALLEL"] = "PARALLEL";
})(StepType || (exports.StepType = StepType = {}));
let CandidateSequenceStatus = class CandidateSequenceStatus {
};
exports.CandidateSequenceStatus = CandidateSequenceStatus;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => role_candidates_entity_1.RoleCandidate, { nullable: false }),
    __metadata("design:type", role_candidates_entity_1.RoleCandidate)
], CandidateSequenceStatus.prototype, "candidate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "candidateId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sequence_entity_1.RoleSequence, { nullable: false }),
    __metadata("design:type", sequence_entity_1.RoleSequence)
], CandidateSequenceStatus.prototype, "sequence", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "sequenceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sequence_steps_entity_1.SequenceSteps, { nullable: false }),
    __metadata("design:type", sequence_steps_entity_1.SequenceSteps)
], CandidateSequenceStatus.prototype, "step", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "stepId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SequenceStepStatus,
        default: SequenceStepStatus.PENDING,
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], CandidateSequenceStatus.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: StepType,
        default: StepType.SEQUENTIAL,
    }),
    __metadata("design:type", String)
], CandidateSequenceStatus.prototype, "stepType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "stepOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "scheduledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "sentAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "deliveredAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "openedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "clickedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "repliedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "failedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CandidateSequenceStatus.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], CandidateSequenceStatus.prototype, "attemptCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], CandidateSequenceStatus.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], CandidateSequenceStatus.prototype, "responseData", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], CandidateSequenceStatus.prototype, "updatedAt", void 0);
exports.CandidateSequenceStatus = CandidateSequenceStatus = __decorate([
    (0, typeorm_1.Entity)('candidate_sequence_status'),
    (0, typeorm_1.Index)(['candidateId', 'sequenceId', 'stepId'], { unique: true })
], CandidateSequenceStatus);
//# sourceMappingURL=candidate-sequence-status.entity.js.map