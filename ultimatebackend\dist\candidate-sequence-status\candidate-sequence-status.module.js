"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateSequenceStatusModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const candidate_sequence_status_entity_1 = require("./candidate-sequence-status.entity");
const candidate_sequence_status_service_1 = require("./candidate-sequence-status.service");
const candidate_sequence_status_controller_1 = require("./candidate-sequence-status.controller");
const sequence_entity_1 = require("../sequence/sequence.entity");
const sequence_steps_entity_1 = require("../sequence-steps/sequence_steps.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
let CandidateSequenceStatusModule = class CandidateSequenceStatusModule {
};
exports.CandidateSequenceStatusModule = CandidateSequenceStatusModule;
exports.CandidateSequenceStatusModule = CandidateSequenceStatusModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                candidate_sequence_status_entity_1.CandidateSequenceStatus,
                sequence_entity_1.RoleSequence,
                sequence_steps_entity_1.SequenceSteps,
                role_candidates_entity_1.RoleCandidate,
            ]),
        ],
        controllers: [candidate_sequence_status_controller_1.CandidateSequenceStatusController],
        providers: [candidate_sequence_status_service_1.CandidateSequenceStatusService],
        exports: [candidate_sequence_status_service_1.CandidateSequenceStatusService],
    })
], CandidateSequenceStatusModule);
//# sourceMappingURL=candidate-sequence-status.module.js.map