import { Repository } from 'typeorm';
import { CandidateSequenceStatus, SequenceStepStatus, StepType } from './candidate-sequence-status.entity';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
export declare class CandidateSequenceStatusService {
    private readonly candidateSequenceStatusRepository;
    private readonly sequenceRepository;
    private readonly stepRepository;
    private readonly candidateRepository;
    constructor(candidateSequenceStatusRepository: Repository<CandidateSequenceStatus>, sequenceRepository: Repository<RoleSequence>, stepRepository: Repository<SequenceSteps>, candidateRepository: Repository<RoleCandidate>);
    createCandidateSequenceStatus(candidateId: number, sequenceId: number, stepId: number, stepType?: StepType, stepOrder?: number): Promise<CandidateSequenceStatus>;
    private validateForeignKeyReferences;
    findById(id: number): Promise<CandidateSequenceStatus | null>;
    updateStatus(id: number, status: SequenceStepStatus, metadata?: Record<string, any>): Promise<CandidateSequenceStatus>;
    getCandidateSequenceStatus(candidateId: number, sequenceId: number): Promise<CandidateSequenceStatus[]>;
    getNextSteps(candidateId: number, sequenceId: number, currentOrder: number): Promise<CandidateSequenceStatus[]>;
    getPendingSteps(candidateId: number, sequenceId: number, stepOrder: number): Promise<CandidateSequenceStatus[]>;
    markStepCompleted(candidateId: number, stepId: number, responseData?: string): Promise<CandidateSequenceStatus>;
    incrementAttemptCount(id: number): Promise<void>;
    getStepsByStatus(status: SequenceStepStatus, limit?: number): Promise<CandidateSequenceStatus[]>;
    getCandidatesInSequence(sequenceId: number): Promise<CandidateSequenceStatus[]>;
    initializeCandidateSequence(candidateIds: number[], sequenceId: number): Promise<CandidateSequenceStatus[]>;
}
