"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateSequenceStatusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const candidate_sequence_status_entity_1 = require("./candidate-sequence-status.entity");
const sequence_entity_1 = require("../sequence/sequence.entity");
const sequence_steps_entity_1 = require("../sequence-steps/sequence_steps.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
let CandidateSequenceStatusService = class CandidateSequenceStatusService {
    constructor(candidateSequenceStatusRepository, sequenceRepository, stepRepository, candidateRepository) {
        this.candidateSequenceStatusRepository = candidateSequenceStatusRepository;
        this.sequenceRepository = sequenceRepository;
        this.stepRepository = stepRepository;
        this.candidateRepository = candidateRepository;
    }
    async createCandidateSequenceStatus(candidateId, sequenceId, stepId, stepType = candidate_sequence_status_entity_1.StepType.SEQUENTIAL, stepOrder = 0) {
        await this.validateForeignKeyReferences(candidateId, sequenceId, stepId);
        const status = this.candidateSequenceStatusRepository.create({
            candidateId,
            sequenceId,
            stepId,
            stepType,
            stepOrder,
            status: candidate_sequence_status_entity_1.SequenceStepStatus.PENDING,
        });
        try {
            return await this.candidateSequenceStatusRepository.save(status);
        }
        catch (error) {
            console.error(`Failed to create candidate sequence status:`, {
                candidateId,
                sequenceId,
                stepId,
                error: error.message
            });
            throw new Error(`Failed to create candidate sequence status: ${error.message}`);
        }
    }
    async validateForeignKeyReferences(candidateId, sequenceId, stepId) {
        const candidate = await this.candidateRepository.findOne({
            where: { id: candidateId },
        });
        if (!candidate) {
            throw new Error(`Candidate with ID ${candidateId} not found`);
        }
        const sequence = await this.sequenceRepository.findOne({
            where: { id: sequenceId },
        });
        if (!sequence) {
            throw new Error(`Sequence with ID ${sequenceId} not found`);
        }
        const step = await this.stepRepository.findOne({
            where: { id: stepId },
        });
        if (!step) {
            throw new Error(`Step with ID ${stepId} not found`);
        }
        if (step.roleSequenceId !== sequenceId) {
            throw new Error(`Step ${stepId} does not belong to sequence ${sequenceId}. Step belongs to sequence ${step.roleSequenceId}`);
        }
    }
    async findById(id) {
        return await this.candidateSequenceStatusRepository.findOne({
            where: { id },
            relations: ['candidate', 'sequence', 'step'],
        });
    }
    async updateStatus(id, status, metadata) {
        const candidateStatus = await this.candidateSequenceStatusRepository.findOne({
            where: { id },
        });
        if (!candidateStatus) {
            console.error(`❌ CANDIDATE-SEQUENCE-STATUS: Record with ID ${id} not found in database`);
            const allRecords = await this.candidateSequenceStatusRepository.find({
                take: 10,
                order: { id: 'DESC' }
            });
            console.error(`❌ CANDIDATE-SEQUENCE-STATUS: Recent records in database:`, allRecords.map(r => ({ id: r.id, candidateId: r.candidateId, stepId: r.stepId, status: r.status })));
            throw new common_1.NotFoundException(`Candidate sequence status not found for ID ${id}`);
        }
        candidateStatus.status = status;
        if (metadata) {
            candidateStatus.metadata = { ...candidateStatus.metadata, ...metadata };
        }
        const now = new Date();
        switch (status) {
            case candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED:
                candidateStatus.scheduledAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.SENT:
                candidateStatus.sentAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED:
                candidateStatus.deliveredAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.OPENED:
                candidateStatus.openedAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.CLICKED:
                candidateStatus.clickedAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.REPLIED:
                candidateStatus.repliedAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.COMPLETED:
                candidateStatus.completedAt = now;
                break;
            case candidate_sequence_status_entity_1.SequenceStepStatus.FAILED:
                candidateStatus.failedAt = now;
                break;
        }
        return await this.candidateSequenceStatusRepository.save(candidateStatus);
    }
    async getCandidateSequenceStatus(candidateId, sequenceId) {
        return await this.candidateSequenceStatusRepository.find({
            where: { candidateId, sequenceId },
            relations: ['candidate', 'sequence', 'step'],
            order: { stepOrder: 'ASC' },
        });
    }
    async getNextSteps(candidateId, sequenceId, currentOrder) {
        return await this.candidateSequenceStatusRepository.find({
            where: {
                candidateId,
                sequenceId,
                stepOrder: currentOrder + 1,
                status: candidate_sequence_status_entity_1.SequenceStepStatus.PENDING,
            },
            relations: ['step'],
        });
    }
    async getPendingSteps(candidateId, sequenceId, stepOrder) {
        return await this.candidateSequenceStatusRepository.find({
            where: {
                candidateId,
                sequenceId,
                stepOrder,
                status: candidate_sequence_status_entity_1.SequenceStepStatus.PENDING,
            },
            relations: ['step'],
        });
    }
    async markStepCompleted(candidateId, stepId, responseData) {
        const status = await this.candidateSequenceStatusRepository.findOne({
            where: { candidateId, stepId },
        });
        if (!status) {
            throw new common_1.NotFoundException('Candidate sequence status not found');
        }
        status.status = candidate_sequence_status_entity_1.SequenceStepStatus.COMPLETED;
        status.completedAt = new Date();
        if (responseData) {
            status.responseData = responseData;
        }
        return await this.candidateSequenceStatusRepository.save(status);
    }
    async incrementAttemptCount(id) {
        await this.candidateSequenceStatusRepository.increment({ id }, 'attemptCount', 1);
    }
    async getStepsByStatus(status, limit = 100) {
        return await this.candidateSequenceStatusRepository.find({
            where: { status },
            relations: ['candidate', 'sequence', 'step'],
            take: limit,
            order: { createdAt: 'ASC' },
        });
    }
    async getCandidatesInSequence(sequenceId) {
        return await this.candidateSequenceStatusRepository.find({
            where: { sequenceId },
            select: ['candidateId', 'sequenceId'],
            order: { candidateId: 'ASC' },
        });
    }
    async initializeCandidateSequence(candidateIds, sequenceId) {
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Initializing candidate sequence for ${candidateIds.length} candidates and sequence ${sequenceId}`);
        if (!candidateIds || candidateIds.length === 0) {
            console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ No candidate IDs provided`);
            throw new Error('No candidate IDs provided');
        }
        if (!sequenceId) {
            console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ No sequence ID provided`);
            throw new Error('No sequence ID provided');
        }
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Fetching sequence ${sequenceId} with steps...`);
        const sequence = await this.sequenceRepository.findOne({
            where: { id: sequenceId },
            relations: ['sequenceSteps'],
        });
        if (!sequence) {
            console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Sequence with ID ${sequenceId} not found`);
            throw new common_1.NotFoundException(`Sequence with ID ${sequenceId} not found`);
        }
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Found sequence "${sequence.name}" (ID: ${sequence.id})`);
        if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
            console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Sequence ${sequenceId} has no steps defined`);
            throw new Error(`Sequence ${sequenceId} has no steps defined`);
        }
        const sortedSteps = sequence.sequenceSteps.sort((a, b) => a.order - b.order);
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Found ${sortedSteps.length} steps for sequence ${sequenceId}`);
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Steps details:`, sortedSteps.map(s => ({
            id: s.id,
            name: s.name,
            order: s.order,
            medium: s.medium,
            type: s.type,
            templateId: s.templateId
        })));
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Validating ${candidateIds.length} candidates exist...`);
        for (const candidateId of candidateIds) {
            const candidate = await this.candidateRepository.findOne({
                where: { id: candidateId },
            });
            if (!candidate) {
                console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Candidate with ID ${candidateId} not found`);
                throw new Error(`Candidate with ID ${candidateId} not found`);
            }
            console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Validated candidate ${candidateId}`);
        }
        const statusEntries = [];
        for (const candidateId of candidateIds) {
            console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Processing candidate ${candidateId}`);
            for (const step of sortedSteps) {
                try {
                    const stepsAtSameOrder = sortedSteps.filter(s => s.order === step.order);
                    const stepType = stepsAtSameOrder.length > 1 ? candidate_sequence_status_entity_1.StepType.PARALLEL : candidate_sequence_status_entity_1.StepType.SEQUENTIAL;
                    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Creating status for candidate ${candidateId}, step ${step.id} (${step.name}), order ${step.order}, medium ${step.medium}, type ${stepType}`);
                    const status = await this.createCandidateSequenceStatus(candidateId, sequenceId, step.id, stepType, step.order);
                    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Created status record ID ${status.id} for candidate ${candidateId}, step ${step.id}`);
                    statusEntries.push(status);
                }
                catch (error) {
                    console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Failed to create status for candidate ${candidateId}, step ${step.id}:`, error.message);
                    throw error;
                }
            }
        }
        console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Successfully created ${statusEntries.length} candidate sequence status entries`);
        return statusEntries;
    }
};
exports.CandidateSequenceStatusService = CandidateSequenceStatusService;
exports.CandidateSequenceStatusService = CandidateSequenceStatusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(candidate_sequence_status_entity_1.CandidateSequenceStatus)),
    __param(1, (0, typeorm_1.InjectRepository)(sequence_entity_1.RoleSequence)),
    __param(2, (0, typeorm_1.InjectRepository)(sequence_steps_entity_1.SequenceSteps)),
    __param(3, (0, typeorm_1.InjectRepository)(role_candidates_entity_1.RoleCandidate)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CandidateSequenceStatusService);
//# sourceMappingURL=candidate-sequence-status.service.js.map