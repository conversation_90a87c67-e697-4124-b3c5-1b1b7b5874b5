import { CompanyService } from './company.service';
import { AddCompanyWithLinksDto, CreateCompanyDto } from './dto/createCompnay.entity';
import { UpdateCompanyDTO } from './dto/updateCompany.dto';
import { GetAllCompaniesDto, GetCompaniesDto } from 'src/jobs/dto/createJob.dto';
export declare class CompanyController {
    private readonly companyService;
    constructor(companyService: CompanyService);
    createCompany(company: CreateCompanyDto): Promise<import("./company.entity").Company>;
    addCompanyWithLinks(linksData: AddCompanyWithLinksDto): Promise<{
        message: string;
        newCompaniesAdded: number;
        totalLinks: number;
        existingLinks: number;
        newCompaniesCount: number;
    } | {
        message: string;
        totalLinks: number;
        existingLinks: number;
        newCompaniesCount: number;
        newCompaniesAdded?: undefined;
    }>;
    updateCompany(id: number, company: UpdateCompanyDTO): Promise<import("./company.entity").Company>;
    deleteCompany(id: number): Promise<void>;
    findAll(): Promise<import("./company.entity").Company[]>;
    getAllCompanies(queryParams: GetAllCompaniesDto): Promise<{
        companies: {
            peopleCount: any;
            jobPostsCount: number;
            id: number;
            public_id: string;
            company_id: string;
            name: string;
            profile_url: string;
            profile_url_encoded: string;
            logo: string;
            cover_photo: string;
            website: string;
            tagline: string;
            address: string;
            company_country: string;
            staff_count: number;
            staff_count_range_start: number;
            staff_count_range_end: number;
            followers_count: number;
            description: string;
            founded: string;
            employee_benefits: string[];
            industry: string;
            specialities: string[];
            company_email: string;
            company_phone: string;
            region: string;
            scrapper_level: number;
            is_scrapped_fully: boolean;
            headquarter_country: string;
            headquarter_city: string;
            headquarter_geographic_area: string;
            headquarter_line1: string;
            headquarter_line2: string;
            headquarter_postal_code: string;
            company_source: string;
            user: import("../users/users.entity").Users;
            userId: string;
            sector: import("../sector/sector.entity").Sector;
            sectorId: number;
            country: import("../country/country.entity").Country;
            scrapper_profile_name: string;
            countryId: number;
            people: import("../people/people.entity").People[];
            userAssigned: import("../people-assignments/entities/people-assignment.entity").PeopleAssignment[];
            jobs: import("../jobs/jobs.entity").Jobs[];
            created_at: Date;
            updated_at: Date;
        }[];
        jobPostsCount: number;
        peopleCount: number;
        totalCount: number;
        totalPages: number;
        currentPage: number;
    }>;
    getCompanies(queryParams: GetCompaniesDto): Promise<{
        companies: import("./company.entity").Company[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
        totalPages: number;
        currentPage: number;
    }>;
    findOne(id: number): Promise<import("./company.entity").Company>;
    findByName(name: string): Promise<import("./company.entity").Company>;
    findByPublicId(public_id: string): Promise<import("./company.entity").Company>;
    findByProfileUrl(profile_url: string): Promise<import("./company.entity").Company>;
    getUniqueIndustriesFromCompanies(): Promise<{
        industries: any[];
        industryStats: {
            industry: any;
            count: number;
        }[];
    }>;
    searchCompanies(searchTerm: string): Promise<import("./company.entity").Company[]>;
}
