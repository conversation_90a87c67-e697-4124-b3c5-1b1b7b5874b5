"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const company_service_1 = require("./company.service");
const createCompnay_entity_1 = require("./dto/createCompnay.entity");
const updateCompany_dto_1 = require("./dto/updateCompany.dto");
const createJob_dto_1 = require("../jobs/dto/createJob.dto");
let CompanyController = class CompanyController {
    constructor(companyService) {
        this.companyService = companyService;
    }
    async createCompany(company) {
        return this.companyService.createCompany(company);
    }
    async addCompanyWithLinks(linksData) {
        return this.companyService.addCompanyWithLinks(linksData);
    }
    async updateCompany(id, company) {
        return this.companyService.updateCompany(id, company);
    }
    async deleteCompany(id) {
        return this.companyService.deleteCompany(id);
    }
    async findAll() {
        return this.companyService.findAll();
    }
    async getAllCompanies(queryParams) {
        return this.companyService.getAllCompanies(queryParams);
    }
    async getCompanies(queryParams) {
        return this.companyService.getCompanies(queryParams);
    }
    async findOne(id) {
        return this.companyService.findOne(id);
    }
    async findByName(name) {
        return this.companyService.findByName(name);
    }
    async findByPublicId(public_id) {
        return this.companyService.findByPublicId(public_id);
    }
    async findByProfileUrl(profile_url) {
        return this.companyService.findByProfileUrl(profile_url);
    }
    async getUniqueIndustriesFromCompanies() {
        return this.companyService.getUniqueIndustriesFromCompanies();
    }
    async searchCompanies(searchTerm) {
        return this.companyService.searchCompanies(searchTerm);
    }
};
exports.CompanyController = CompanyController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new company' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createCompnay_entity_1.CreateCompanyDto]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "createCompany", null);
__decorate([
    (0, common_1.Post)('addCompanyWithLinks'),
    (0, swagger_1.ApiOperation)({ summary: 'Add companies with links' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createCompnay_entity_1.AddCompanyWithLinksDto]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "addCompanyWithLinks", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a company' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateCompany_dto_1.UpdateCompanyDTO]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "updateCompany", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a company' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "deleteCompany", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all companies' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('getAllCompanies'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all companies with query' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createJob_dto_1.GetAllCompaniesDto]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "getAllCompanies", null);
__decorate([
    (0, common_1.Get)('getCompanies'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all companies with query' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createJob_dto_1.GetCompaniesDto]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "getCompanies", null);
__decorate([
    (0, common_1.Get)('find/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a company by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('find/name'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a company by name' }),
    __param(0, (0, common_1.Query)('name')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "findByName", null);
__decorate([
    (0, common_1.Get)('find/public_id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a company by public_id' }),
    __param(0, (0, common_1.Query)('public_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "findByPublicId", null);
__decorate([
    (0, common_1.Get)('find/profile_url'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a company by profile_url' }),
    __param(0, (0, common_1.Query)('profile_url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "findByProfileUrl", null);
__decorate([
    (0, common_1.Get)('getUniqueIndustriesFromCompanies'),
    (0, swagger_1.ApiOperation)({ summary: 'Get unique industries from companies' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "getUniqueIndustriesFromCompanies", null);
__decorate([
    (0, common_1.Get)('searchCompanies'),
    (0, swagger_1.ApiOperation)({ summary: 'Search companies' }),
    __param(0, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CompanyController.prototype, "searchCompanies", null);
exports.CompanyController = CompanyController = __decorate([
    (0, common_1.Controller)('company'),
    (0, swagger_1.ApiTags)('company'),
    __metadata("design:paramtypes", [company_service_1.CompanyService])
], CompanyController);
//# sourceMappingURL=company.controller.js.map