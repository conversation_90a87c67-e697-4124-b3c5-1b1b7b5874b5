import { Company } from './company.entity';
import { Repository } from 'typeorm';
import { AddCompanyWithLinksDto, CreateCompanyDto } from './dto/createCompnay.entity';
import { UpdateCompanyDTO } from './dto/updateCompany.dto';
import { GetAllCompaniesDto, GetCompaniesDto } from 'src/jobs/dto/createJob.dto';
import { People } from 'src/people/people.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
export declare class CompanyService {
    private companyRepository;
    private peopleRepository;
    private jobsRepository;
    private peopleAssignmentRepository;
    constructor(companyRepository: Repository<Company>, peopleRepository: Repository<People>, jobsRepository: Repository<Jobs>, peopleAssignmentRepository: Repository<PeopleAssignment>);
    createCompany(company: CreateCompanyDto): Promise<Company>;
    updateCompany(id: number, company: UpdateCompanyDTO): Promise<Company>;
    deleteCompany(id: number): Promise<void>;
    findAll(): Promise<Company[]>;
    findOne(id: number): Promise<Company>;
    findByName(name: string): Promise<Company>;
    findByPublicId(public_id: string): Promise<Company>;
    findByCompanyId(company_id: string): Promise<Company>;
    findByProfileUrl(profile_url: string): Promise<Company>;
    getAllCompanies(queryParams: GetAllCompaniesDto): Promise<{
        companies: {
            peopleCount: any;
            jobPostsCount: number;
            id: number;
            public_id: string;
            company_id: string;
            name: string;
            profile_url: string;
            profile_url_encoded: string;
            logo: string;
            cover_photo: string;
            website: string;
            tagline: string;
            address: string;
            company_country: string;
            staff_count: number;
            staff_count_range_start: number;
            staff_count_range_end: number;
            followers_count: number;
            description: string;
            founded: string;
            employee_benefits: string[];
            industry: string;
            specialities: string[];
            company_email: string;
            company_phone: string;
            region: string;
            scrapper_level: number;
            is_scrapped_fully: boolean;
            headquarter_country: string;
            headquarter_city: string;
            headquarter_geographic_area: string;
            headquarter_line1: string;
            headquarter_line2: string;
            headquarter_postal_code: string;
            company_source: string;
            user: import("../users/users.entity").Users;
            userId: string;
            sector: import("../sector/sector.entity").Sector;
            sectorId: number;
            country: import("../country/country.entity").Country;
            scrapper_profile_name: string;
            countryId: number;
            people: People[];
            userAssigned: PeopleAssignment[];
            jobs: Jobs[];
            created_at: Date;
            updated_at: Date;
        }[];
        jobPostsCount: number;
        peopleCount: number;
        totalCount: number;
        totalPages: number;
        currentPage: number;
    }>;
    searchCompanies(searchTerm: string): Promise<Company[]>;
    getUniqueIndustriesFromCompanies(): Promise<{
        industries: any[];
        industryStats: {
            industry: any;
            count: number;
        }[];
    }>;
    addCompanyWithLinks(linksData: AddCompanyWithLinksDto): Promise<{
        message: string;
        newCompaniesAdded: number;
        totalLinks: number;
        existingLinks: number;
        newCompaniesCount: number;
    } | {
        message: string;
        totalLinks: number;
        existingLinks: number;
        newCompaniesCount: number;
        newCompaniesAdded?: undefined;
    }>;
    getCompanies(queryParams: GetCompaniesDto): Promise<{
        companies: Company[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
        totalPages: number;
        currentPage: number;
    }>;
}
