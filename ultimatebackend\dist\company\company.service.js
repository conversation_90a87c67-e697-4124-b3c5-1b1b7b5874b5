"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const company_entity_1 = require("./company.entity");
const typeorm_2 = require("typeorm");
const people_entity_1 = require("../people/people.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
let CompanyService = class CompanyService {
    constructor(companyRepository, peopleRepository, jobsRepository, peopleAssignmentRepository) {
        this.companyRepository = companyRepository;
        this.peopleRepository = peopleRepository;
        this.jobsRepository = jobsRepository;
        this.peopleAssignmentRepository = peopleAssignmentRepository;
    }
    async createCompany(company) {
        return await this.companyRepository.save(company);
    }
    async updateCompany(id, company) {
        const companyToUpdate = await this.companyRepository.findOne({
            where: { id },
        });
        await this.companyRepository.update({ id }, company);
        return companyToUpdate;
    }
    async deleteCompany(id) {
        await this.companyRepository.delete({ id });
    }
    async findAll() {
        return this.companyRepository.find();
    }
    async findOne(id) {
        return this.companyRepository.findOne({ where: { id } });
    }
    async findByName(name) {
        return this.companyRepository.findOne({ where: { name } });
    }
    async findByPublicId(public_id) {
        return this.companyRepository.findOne({ where: { public_id } });
    }
    async findByCompanyId(company_id) {
        return this.companyRepository.findOne({ where: { company_id } });
    }
    async findByProfileUrl(profile_url) {
        return this.companyRepository.findOne({ where: { profile_url } });
    }
    async getAllCompanies(queryParams) {
        try {
            const { page, size, country_id, sector_id, findPeople, startDate, endDate, withContactInfo, } = queryParams;
            const pageNumber = parseInt(page) || 1;
            const pageSize = parseInt(size) || 10;
            const skip = (pageNumber - 1) * pageSize;
            const qb = this.companyRepository
                .createQueryBuilder('company')
                .leftJoinAndSelect('company.people', 'people')
                .leftJoinAndSelect('company.country', 'country')
                .leftJoinAndSelect('company.sector', 'sector')
                .leftJoinAndSelect('company.jobs', 'jobs');
            const safeStartDate = typeof startDate === 'string' ? startDate.trim() : undefined;
            const safeEndDate = typeof endDate === 'string' ? endDate.trim() : undefined;
            const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
            if (safeStartDate && safeEndDate && safeStartDate === safeEndDate) {
                qb.andWhere('DATE(company.updated_at) = :date', {
                    date: safeStartDate,
                });
            }
            else if (safeStartDate && safeEndDate) {
                qb.andWhere('DATE(company.updated_at) BETWEEN :start AND :end', {
                    start: safeStartDate,
                    end: safeEndDate,
                });
            }
            else if (safeStartDate) {
                qb.andWhere('DATE(company.updated_at) >= :start', {
                    start: safeStartDate,
                });
            }
            else if (safeEndDate) {
                qb.andWhere('DATE(company.updated_at) <= :end', { end: safeEndDate });
            }
            if (country_id) {
                qb.andWhere('company.countryId = :countryId', {
                    countryId: parseInt(country_id),
                });
            }
            if (sector_id) {
                qb.andWhere('company.sectorId = :sectorId', {
                    sectorId: parseInt(sector_id),
                });
            }
            if (findPeople === 'true') {
                qb.andWhere('company.scrapper_level = :scrapperLevel', {
                    scrapperLevel: 3,
                });
                const peopleWithCompanies = await this.peopleRepository
                    .createQueryBuilder('people')
                    .select('people.companyId')
                    .where('people.companyId IS NOT NULL')
                    .getMany();
                if (peopleWithCompanies.length > 0) {
                    const companyIds = [
                        ...new Set(peopleWithCompanies.map((p) => p.companyId)),
                    ];
                    qb.andWhere('company.id IN (:...companyIds)', { companyIds });
                }
                else {
                    return {
                        companies: [],
                        peopleCount: 0,
                        jobPostsCount: 0,
                        totalCount: 0,
                        totalPages: 1,
                        currentPage: pageNumber,
                    };
                }
            }
            else if (findPeople === 'false') {
                qb.andWhere('company.scrapper_level = :scrapperLevel', {
                    scrapperLevel: 2,
                });
                const peopleWithCompanies = await this.peopleRepository
                    .createQueryBuilder('people')
                    .select('people.companyId')
                    .where('people.companyId IS NOT NULL')
                    .getMany();
                if (peopleWithCompanies.length > 0) {
                    const companyIds = [
                        ...new Set(peopleWithCompanies.map((p) => p.companyId)),
                    ];
                    qb.andWhere('company.id NOT IN (:...companyIds)', { companyIds });
                }
            }
            else if (findPeople === null) {
                qb.andWhere('company.scrapper_level IN (:...scrapperLevels)', {
                    scrapperLevels: [2, 3],
                });
            }
            qb.orderBy('company.id', 'DESC').take(pageSize).skip(skip);
            const [companies, totalCount] = await qb.getManyAndCount();
            if (companies.length === 0) {
                return {
                    companies: [],
                    peopleCount: 0,
                    jobPostsCount: 0,
                    totalCount: 0,
                    totalPages: 1,
                    currentPage: pageNumber,
                };
            }
            const companyIds = companies.map((c) => c.id);
            const [jobPostsCount, peopleCount] = await Promise.all([
                this.jobsRepository
                    .createQueryBuilder('jobs')
                    .where('jobs.companyId IN (:...companyIds)', { companyIds })
                    .getCount(),
                withContactInfo === 'true'
                    ? (async () => {
                        const assignments = await this.peopleAssignmentRepository
                            .createQueryBuilder('assignments')
                            .select('assignments.personId')
                            .getMany();
                        if (assignments.length > 0) {
                            const personIds = [
                                ...new Set(assignments.map((a) => a.personId)),
                            ];
                            return this.peopleRepository
                                .createQueryBuilder('people')
                                .where('people.companyId IN (:...companyIds)', { companyIds })
                                .andWhere('people.id IN (:...personIds)', { personIds })
                                .getCount();
                        }
                        else {
                            return 0;
                        }
                    })()
                    : this.peopleRepository
                        .createQueryBuilder('people')
                        .where('people.companyId IN (:...companyIds)', { companyIds })
                        .getCount(),
            ]);
            const peoplePerCompany = await this.peopleRepository
                .createQueryBuilder('people')
                .select('people.companyId', 'companyId')
                .addSelect('COUNT(people.id)', 'count')
                .where('people.companyId IN (:...companyIds)', { companyIds })
                .groupBy('people.companyId')
                .getRawMany();
            const peopleCountMap = Object.fromEntries(peoplePerCompany.map((p) => [p.companyId, parseInt(p.count, 10)]));
            return {
                companies: companies.map((company) => ({
                    ...company,
                    peopleCount: peopleCountMap[company.id] || 0,
                    jobPostsCount: company.jobs.length || 0,
                })),
                jobPostsCount,
                peopleCount,
                totalCount,
                totalPages: Math.ceil(totalCount / pageSize),
                currentPage: pageNumber,
            };
        }
        catch (error) {
            console.error(error);
            throw new common_1.InternalServerErrorException(`Failed to retrieve all companies: ${error.message}`);
        }
    }
    async searchCompanies(searchTerm) {
        try {
            const companies = await this.companyRepository.find({
                where: [
                    { name: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                    { website: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                    { profile_url: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                    { industry: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                    { company_phone: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                    { company_email: (0, typeorm_2.ILike)(`%${searchTerm}%`) },
                ],
                relations: {
                    people: true,
                    jobs: true,
                },
            });
            return companies;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(`Failed to retrieve all companies: ${error.message}`);
        }
    }
    async getUniqueIndustriesFromCompanies() {
        try {
            const industries = await this.companyRepository
                .createQueryBuilder('company')
                .select('company.industry', 'industry')
                .addSelect('COUNT(company.industry)', 'count')
                .where('company.industry IS NOT NULL')
                .groupBy('company.industry')
                .orderBy('COUNT(company.industry)', 'DESC')
                .getRawMany();
            const industryCounts = industries.map((industry) => ({
                industry: industry.industry,
                count: parseInt(industry.count),
            }));
            const temp = [];
            industryCounts.forEach((industry) => {
                temp.push(industry.industry + ' (' + industry.count + ')');
            });
            return {
                industries: temp,
                industryStats: industryCounts,
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(`Failed to retrieve job applicants: ${error.message}`);
        }
    }
    async addCompanyWithLinks(linksData) {
        try {
            let links;
            if (typeof linksData === 'string') {
                try {
                    const parsedBody = JSON.parse(linksData);
                    links = parsedBody.links;
                }
                catch (parseError) {
                    throw new common_1.BadRequestException('Invalid JSON format: ' + parseError.message);
                }
            }
            else {
                links = linksData.links;
            }
            if (!links || !Array.isArray(links)) {
                throw new common_1.BadRequestException('No links provided or invalid format. Please provide an array of LinkedIn company URLs.');
            }
            const cleanedLinks = links
                .map((link) => {
                if (typeof link !== 'string')
                    return null;
                return link.trim().replace(/^[\\'",\s]+|[\\'",\s]+$/g, '');
            })
                .filter((link) => link && link.includes('linkedin.com/company/'));
            if (cleanedLinks.length === 0) {
                throw new common_1.BadRequestException('No valid LinkedIn company URLs found in the provided links.');
            }
            const existingCompanies = await this.companyRepository.find({
                where: [
                    {
                        profile_url: (0, typeorm_2.In)(cleanedLinks),
                    },
                    {
                        profile_url_encoded: (0, typeorm_2.In)(cleanedLinks),
                    },
                ],
                select: ['profile_url', 'profile_url_encoded'],
            });
            const existingLinks = new Set();
            existingCompanies.forEach((company) => {
                if (company.profile_url)
                    existingLinks.add(company.profile_url);
                if (company.profile_url_encoded)
                    existingLinks.add(company.profile_url_encoded);
            });
            const newLinks = cleanedLinks.filter((link) => !existingLinks.has(link));
            if (newLinks.length === 0) {
                return {
                    message: 'All provided links already exist in the database',
                    newCompaniesAdded: 0,
                    totalLinks: cleanedLinks.length,
                    existingLinks: existingCompanies.length,
                    newCompaniesCount: 0,
                };
            }
            const companiesToCreate = newLinks
                .map((link) => {
                const urlParts = link.split('/company/');
                if (urlParts.length < 2) {
                    return null;
                }
                let identifier = urlParts[1].trim();
                if (identifier.endsWith('/')) {
                    identifier = identifier.slice(0, -1);
                }
                const isNumeric = /^\d+$/.test(identifier);
                const companyData = {
                    name: ' ',
                    scrapper_level: 2,
                    company_source: 'MANUAL',
                };
                if (isNumeric) {
                    companyData.profile_url_encoded = link;
                    companyData.public_id = identifier;
                }
                else {
                    companyData.profile_url = link;
                    companyData.public_id = identifier;
                }
                return companyData;
            })
                .filter(Boolean);
            const createdCompanies = await this.companyRepository.save(companiesToCreate);
            return {
                message: `${createdCompanies.length} new companies added successfully from ${cleanedLinks.length} links provided. ${existingLinks.size} links already exist.`,
                totalLinks: cleanedLinks.length,
                existingLinks: existingCompanies.length,
                newCompaniesCount: createdCompanies.length,
            };
        }
        catch (error) {
            console.error('Error in addCompanyWithLink:', error);
            throw new common_1.InternalServerErrorException('Error adding companies by link: ' + error.message);
        }
    }
    async getCompanies(queryParams) {
        const { page, pageSize, search, country_id, sector_id, level, startDate, endDate, companySize, industries, premium_level, } = queryParams;
        const pageNumber = parseInt(page) || 1;
        const pageSizeInt = parseInt(pageSize) || 10;
        const limit = pageSizeInt;
        const skip = (pageNumber - 1) * pageSizeInt;
        const safeStartDate = typeof startDate === 'string' ? startDate.trim() : undefined;
        const safeEndDate = typeof endDate === 'string' ? endDate.trim() : undefined;
        const qb = this.companyRepository.createQueryBuilder('company');
        qb.leftJoinAndSelect('company.people', 'people');
        qb.leftJoinAndSelect('company.jobs', 'jobs');
        if (country_id) {
            if (country_id == '0') {
                console.log('country_id is 0');
                qb.andWhere('company.scrapper_level = :level', { level: Number(3) });
                qb.andWhere('company."countryId" IS NULL');
            }
            else {
                qb.andWhere('company.countryId = :country_id', {
                    country_id: parseInt(country_id),
                });
            }
        }
        if (sector_id) {
            qb.andWhere('company.sectorId = :sector_id', {
                sector_id: parseInt(sector_id),
            });
        }
        if (level) {
            qb.andWhere('company.scrapper_level = :level', {
                level: parseInt(level),
            });
        }
        if (companySize) {
            qb.andWhere('company.staff_count = :companySize', { companySize });
        }
        if (premium_level === 'true') {
            qb.andWhere('company.region IS NULL');
        }
        else if (premium_level === 'false') {
            qb.andWhere('company.region IS NOT NULL');
        }
        let industriess = [];
        if (industries && Array.isArray(industries)) {
            industriess = industries.map((industry) => industry.replace(/\s\(\d+\)$/, ''));
        }
        else if (typeof industries === 'string') {
            industriess = industries
                .split(',')
                .map((industry) => industry.trim().replace(/\s\(\d+\)$/, ''));
        }
        if (industriess.length > 0) {
            qb.andWhere('company.industry ILIKE ANY(:industries)', {
                industries: industriess.map((i) => `%${i}%`),
            });
        }
        if (search && search.trim()) {
            const term = `%${search}%`;
            let searchCondition = `(company.name ILIKE :term
      OR company.website ILIKE :term
      OR company.profile_url ILIKE :term
      OR company.industry ILIKE :term
      OR company.address ILIKE :term
      OR company.description ILIKE :term
      OR company.headquarter_country ILIKE :term`;
            const params = { term };
            if (!isNaN(Number(search))) {
                searchCondition +=
                    ' OR company.staff_count = :staffCount OR company.founded = :founded';
                params.staffCount = Number(search);
                params.founded = Number(search);
            }
            searchCondition += ')';
            qb.andWhere(searchCondition, params);
        }
        if (safeStartDate && safeEndDate && safeStartDate === safeEndDate) {
            qb.andWhere('DATE(company.updated_at) = :date', { date: safeStartDate });
        }
        else if (safeStartDate && safeEndDate) {
            qb.andWhere('DATE(company.updated_at) BETWEEN :start AND :end', {
                start: safeStartDate,
                end: safeEndDate,
            });
        }
        else if (safeStartDate) {
            qb.andWhere('DATE(company.updated_at) >= :start', {
                start: safeStartDate,
            });
        }
        else if (safeEndDate) {
            qb.andWhere('DATE(company.updated_at) <= :end', { end: safeEndDate });
        }
        qb.orderBy('company.updated_at', 'DESC');
        qb.skip(skip).take(limit);
        const [companies, totalCount] = await qb.getManyAndCount();
        const baseQb = qb
            .clone()
            .skip(undefined)
            .take(undefined)
            .orderBy(undefined);
        const sr_count = await baseQb
            .clone()
            .andWhere('company.sectorId = 2')
            .getCount();
        const direct_count = await baseQb
            .clone()
            .andWhere('company.sectorId = 1')
            .getCount();
        const unknown_count = await baseQb
            .clone()
            .andWhere('company.sectorId IS NULL')
            .getCount();
        return {
            companies,
            totalCount,
            sr_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? sr_count
                : sector_id === '2'
                    ? sr_count
                    : 0,
            direct_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? direct_count
                : sector_id === '1'
                    ? direct_count
                    : 0,
            unknown_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? unknown_count
                : sector_id === 'null'
                    ? unknown_count
                    : 0,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: limit > 0 ? Math.ceil((skip + 1) / limit) : 1,
        };
    }
};
exports.CompanyService = CompanyService;
exports.CompanyService = CompanyService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(1, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(2, (0, typeorm_1.InjectRepository)(jobs_entity_1.Jobs)),
    __param(3, (0, typeorm_1.InjectRepository)(people_assignment_entity_1.PeopleAssignment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CompanyService);
//# sourceMappingURL=company.service.js.map