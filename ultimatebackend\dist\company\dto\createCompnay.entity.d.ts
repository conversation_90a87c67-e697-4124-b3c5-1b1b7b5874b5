export declare class CreateCompanyDto {
    public_id: string;
    company_id: string;
    name: string;
    profile_url: string;
    profile_url_encoded: string;
    logo?: string;
    cover_photo?: string;
    website?: string;
    tagline?: string;
    address?: string;
    staff_count?: number;
    staff_count_range_start?: number;
    staff_count_range_end?: number;
    followers_count?: number;
    description?: string;
    founded?: string;
    industry?: string;
    headquarter_country?: string;
    headquarter_city?: string;
    headquarter_geographic_area?: string;
    headquarter_line1?: string;
    headquarter_line2?: string;
    headquarter_postal_code?: string;
    specialities?: string[];
    employee_benefits?: string[];
    company_email?: string;
    company_phone?: string;
    region?: string;
    scrapper_level?: number;
    is_scrapped_fully?: boolean;
    userId?: string;
    sectorId?: number;
    countryId?: number;
    peopleId?: number;
    jobsId?: number;
}
export declare class AddCompanyWithLinksDto {
    links: string[];
}
