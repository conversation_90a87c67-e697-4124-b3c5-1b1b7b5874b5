"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddCompanyWithLinksDto = exports.CreateCompanyDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateCompanyDto {
}
exports.CreateCompanyDto = CreateCompanyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The public id of a company',
        example: 'ultimate-outsourcing-services',
        type: String,
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "public_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The company ID',
        type: String,
        example: '3218264',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "company_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the company',
        type: String,
        example: 'Ultimate Outsourcing LTD',
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The profile URL of the company',
        type: String,
        example: 'https://www.linkedin.com/company/ultimate-outsourcing-services/',
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "profile_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The encoded profile URL of the company',
        type: String,
        required: false,
        example: 'https://www.linkedin.com/company/3218264/',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "profile_url_encoded", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The company logo URL',
        type: String,
        required: false,
        example: 'https://media.licdn.com/dms/image/v2/D560BAQF-7bKrR_0aFA/company-logo_200_200/company-logo_200_200/0/1729525068868/ultimate_outsourcing_services_logo?e=1749686400&v=beta&t=9Z97_vGML89cFZ2nQaWjJ-mbIVApyivVgoMl-BjRWBY',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "logo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The cover photo URL of the company',
        type: String,
        required: false,
        example: 'https://media.licdn.com/dms/image/v2/D560BAQF-7bKrR_0aFA/company-logo_200_200/company-logo_200_200/0/1729525068868/ultimate_outsourcing_services_logo?e=1749686400&v=beta&t=9Z97_vGML89cFZ2nQaWjJ-mbIVApyivVgoMl-BjRWBY',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "cover_photo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The company website URL',
        type: String,
        required: false,
        example: 'https://ultimateoutsourcing.co.uk/',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The company tagline',
        type: String,
        required: false,
        example: 'All Your Recruitment Worries Outsourced',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "tagline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The company address',
        type: String,
        required: false,
        example: 'London',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of staff in the company',
        type: Number,
        required: false,
        example: '11',
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "staff_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Staff count range start',
        type: Number,
        required: false,
        example: 11,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "staff_count_range_start", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Staff count range end',
        type: Number,
        required: false,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "staff_count_range_end", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of followers the company has',
        type: Number,
        required: false,
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "followers_count", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detailed description of the company',
        type: String,
        required: false,
        example: 'Company about info',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Year the company was founded',
        type: String,
        required: false,
        example: '2014',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "founded", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The industry the company belongs to',
        type: String,
        required: false,
        example: 'Staffing and Recruiting',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "industry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The country of company headquarter',
        type: String,
        required: false,
        example: 'UK',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The city of company headquarter',
        type: String,
        required: false,
        example: 'London',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The geographic area of company headquarter',
        type: String,
        required: false,
        example: 'Street 1',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_geographic_area", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The line 1 address of company headquarter',
        type: String,
        required: false,
        example: 'Here will be line 1 address',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_line1", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The line 2 address of company headquarter',
        type: String,
        required: false,
        example: 'Here will be line 2 address',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_line2", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The postal code of company headquarter',
        type: String,
        required: false,
        example: 'SW1A 1AA',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "headquarter_postal_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Specialities of the company (comma-separated values)',
        type: [String],
        required: false,
        example: [
            'Recruitment, Candidate Sourcing',
            'CV Sourcing',
            'Pre-Qualification',
            '360 Recruitment',
            'Dedicated Resourcer',
            'head hunter',
        ],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCompanyDto.prototype, "specialities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Employee benefits (comma-separated values)',
        type: [String],
        required: false,
        example: ['Health Insurance', 'Paid Time Off', 'Flexible Working Hours'],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCompanyDto.prototype, "employee_benefits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Company official email',
        type: String,
        required: false,
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "company_email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Company phone number',
        type: String,
        required: false,
        example: '+44 125 12446412',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "company_phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Region where the company is located',
        type: String,
        required: false,
        example: 'United Kingdom',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Scraper level for data collection priority',
        type: Number,
        required: false,
        example: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "scrapper_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The status of scrapper',
        type: Boolean,
        required: false,
        example: 'true',
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateCompanyDto.prototype, "is_scrapped_fully", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The user who owns the company',
        type: String,
        required: false,
        example: '',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCompanyDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The sector the company belongs to',
        type: Number,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "sectorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The country the company is based in',
        type: Number,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "countryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The primary people associated with the company',
        type: Number,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "peopleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The jobs associated with the company',
        type: Number,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCompanyDto.prototype, "jobsId", void 0);
class AddCompanyWithLinksDto {
}
exports.AddCompanyWithLinksDto = AddCompanyWithLinksDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Links associated with the company',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AddCompanyWithLinksDto.prototype, "links", void 0);
//# sourceMappingURL=createCompnay.entity.js.map