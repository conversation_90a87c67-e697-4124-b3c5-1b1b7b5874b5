import { CompanyScrapperControlService } from './company_scrapper_control.service';
import { CreateCompanyScrapperControlDto } from './dto/create-company_scrapper_control.dto';
import { UpdateCompanyScrapperControlDto } from './dto/update-company_scrapper_control.dto';
export declare class CompanyScrapperControlController {
    private readonly companyScrapperControlService;
    constructor(companyScrapperControlService: CompanyScrapperControlService);
    create(createCompanyScrapperControlDto: CreateCompanyScrapperControlDto): string;
    createAndUpdate(data: CreateCompanyScrapperControlDto): Promise<{
        message: string;
        result: import("typeorm").UpdateResult;
    } | {
        message: string;
        result: import("./entities/company_scrapper_control.entity").CompanyScrapperControl;
    }>;
    findAll(): Promise<import("./entities/company_scrapper_control.entity").CompanyScrapperControl>;
    findOne(id: string): string;
    update(id: string, updateCompanyScrapperControlDto: UpdateCompanyScrapperControlDto): string;
    remove(id: string): string;
}
