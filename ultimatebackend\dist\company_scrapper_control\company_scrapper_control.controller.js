"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyScrapperControlController = void 0;
const common_1 = require("@nestjs/common");
const company_scrapper_control_service_1 = require("./company_scrapper_control.service");
const create_company_scrapper_control_dto_1 = require("./dto/create-company_scrapper_control.dto");
const update_company_scrapper_control_dto_1 = require("./dto/update-company_scrapper_control.dto");
let CompanyScrapperControlController = class CompanyScrapperControlController {
    constructor(companyScrapperControlService) {
        this.companyScrapperControlService = companyScrapperControlService;
    }
    create(createCompanyScrapperControlDto) {
        return this.companyScrapperControlService.create(createCompanyScrapperControlDto);
    }
    createAndUpdate(data) {
        return this.companyScrapperControlService.createAndUpdate(data);
    }
    findAll() {
        return this.companyScrapperControlService.findAll();
    }
    findOne(id) {
        return this.companyScrapperControlService.findOne(+id);
    }
    update(id, updateCompanyScrapperControlDto) {
        return this.companyScrapperControlService.update(+id, updateCompanyScrapperControlDto);
    }
    remove(id) {
        return this.companyScrapperControlService.remove(+id);
    }
};
exports.CompanyScrapperControlController = CompanyScrapperControlController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_company_scrapper_control_dto_1.CreateCompanyScrapperControlDto]),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('create-update'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_company_scrapper_control_dto_1.CreateCompanyScrapperControlDto]),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "createAndUpdate", null);
__decorate([
    (0, common_1.Get)('get-company-controller'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_company_scrapper_control_dto_1.UpdateCompanyScrapperControlDto]),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CompanyScrapperControlController.prototype, "remove", null);
exports.CompanyScrapperControlController = CompanyScrapperControlController = __decorate([
    (0, common_1.Controller)('company-scrapper-control'),
    __metadata("design:paramtypes", [company_scrapper_control_service_1.CompanyScrapperControlService])
], CompanyScrapperControlController);
//# sourceMappingURL=company_scrapper_control.controller.js.map