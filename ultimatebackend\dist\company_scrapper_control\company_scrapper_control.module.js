"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyScrapperControlModule = void 0;
const common_1 = require("@nestjs/common");
const company_scrapper_control_service_1 = require("./company_scrapper_control.service");
const company_scrapper_control_controller_1 = require("./company_scrapper_control.controller");
const typeorm_1 = require("@nestjs/typeorm");
const company_scrapper_control_entity_1 = require("./entities/company_scrapper_control.entity");
let CompanyScrapperControlModule = class CompanyScrapperControlModule {
};
exports.CompanyScrapperControlModule = CompanyScrapperControlModule;
exports.CompanyScrapperControlModule = CompanyScrapperControlModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([company_scrapper_control_entity_1.CompanyScrapperControl])],
        controllers: [company_scrapper_control_controller_1.CompanyScrapperControlController],
        providers: [company_scrapper_control_service_1.CompanyScrapperControlService],
        exports: [company_scrapper_control_service_1.CompanyScrapperControlService],
    })
], CompanyScrapperControlModule);
//# sourceMappingURL=company_scrapper_control.module.js.map