import { CreateCompanyScrapperControlDto } from './dto/create-company_scrapper_control.dto';
import { UpdateCompanyScrapperControlDto } from './dto/update-company_scrapper_control.dto';
import { CompanyScrapperControl } from './entities/company_scrapper_control.entity';
import { Repository } from 'typeorm';
export declare class CompanyScrapperControlService {
    private readonly companyScrapperControlRepository;
    constructor(companyScrapperControlRepository: Repository<CompanyScrapperControl>);
    create(createCompanyScrapperControlDto: CreateCompanyScrapperControlDto): string;
    createAndUpdate(data: CreateCompanyScrapperControlDto): Promise<{
        message: string;
        result: import("typeorm").UpdateResult;
    } | {
        message: string;
        result: CompanyScrapperControl;
    }>;
    findAll(): Promise<CompanyScrapperControl>;
    findOne(id: number): string;
    update(id: number, updateCompanyScrapperControlDto: UpdateCompanyScrapperControlDto): string;
    remove(id: number): string;
}
