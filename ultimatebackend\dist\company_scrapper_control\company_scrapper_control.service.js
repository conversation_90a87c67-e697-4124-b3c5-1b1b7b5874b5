"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyScrapperControlService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const company_scrapper_control_entity_1 = require("./entities/company_scrapper_control.entity");
const typeorm_2 = require("typeorm");
let CompanyScrapperControlService = class CompanyScrapperControlService {
    constructor(companyScrapperControlRepository) {
        this.companyScrapperControlRepository = companyScrapperControlRepository;
    }
    create(createCompanyScrapperControlDto) {
        return 'This action adds a new companyScrapperControl';
    }
    async createAndUpdate(data) {
        const existedData = await this.companyScrapperControlRepository.find();
        if (existedData.length > 0) {
            const result = await this.companyScrapperControlRepository.update({ id: existedData[0].id }, {
                countryId: data.countryId,
                sectorId: data.sectorId,
                company_source: data.company_source,
                is_default: data.is_default,
            });
            return {
                message: 'Company Scrapper settings updated successfully',
                result,
            };
        }
        else {
            const result = this.companyScrapperControlRepository.create({
                countryId: data.countryId,
                sectorId: data.sectorId,
                company_source: data.company_source,
                is_default: data.is_default,
            });
            await this.companyScrapperControlRepository.save(result);
            return {
                message: 'Company Scrapper settings updated successfully',
                result,
            };
        }
    }
    async findAll() {
        const result = await this.companyScrapperControlRepository.find();
        const data = result[0];
        return data;
    }
    findOne(id) {
        return `This action returns a #${id} companyScrapperControl`;
    }
    update(id, updateCompanyScrapperControlDto) {
        return `This action updates a #${id} companyScrapperControl`;
    }
    remove(id) {
        return `This action removes a #${id} companyScrapperControl`;
    }
};
exports.CompanyScrapperControlService = CompanyScrapperControlService;
exports.CompanyScrapperControlService = CompanyScrapperControlService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(company_scrapper_control_entity_1.CompanyScrapperControl)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CompanyScrapperControlService);
//# sourceMappingURL=company_scrapper_control.service.js.map