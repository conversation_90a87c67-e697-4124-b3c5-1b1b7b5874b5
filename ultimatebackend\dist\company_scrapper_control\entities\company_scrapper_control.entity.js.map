{"version": 3, "file": "company_scrapper_control.entity.js", "sourceRoot": "", "sources": ["../../../src/company_scrapper_control/entities/company_scrapper_control.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAiE;AAG1D,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;CAiBlC,CAAA;AAjBY,wDAAsB;AAEjC;IADC,IAAA,gCAAsB,GAAE;;kDACd;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDACT;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDACV;AAKjB;IAHC,IAAA,gBAAM,EAAC;QACN,QAAQ,EAAE,IAAI;KACf,CAAC;;8DACqB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DACR;iCAhBR,sBAAsB;IADlC,IAAA,gBAAM,EAAC,0BAA0B,CAAC;GACtB,sBAAsB,CAiBlC"}