"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactUs = void 0;
const typeorm_1 = require("typeorm");
let ContactUs = class ContactUs {
};
exports.ContactUs = ContactUs;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ContactUs.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'first_name', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "firstName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_name', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'job_title', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "jobTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_name', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "companyName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'business_email', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "businessEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'phone_number', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_post_code', type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "companyPostCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'industry', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "industry", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'interested_services', type: 'text', nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "interestedServices", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_website', type: 'text', nullable: true }),
    __metadata("design:type", String)
], ContactUs.prototype, "companyWebsite", void 0);
exports.ContactUs = ContactUs = __decorate([
    (0, typeorm_1.Entity)('contact_us')
], ContactUs);
//# sourceMappingURL=contact-us.entity.js.map