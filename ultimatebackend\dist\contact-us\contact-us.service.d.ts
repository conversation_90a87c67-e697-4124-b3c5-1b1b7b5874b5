import { Repository } from 'typeorm';
import { ContactUs } from './contact-us.entity';
import { EmailService } from 'src/email/email.service';
import { ContactUsDto } from './dto/contact_us.dto';
export declare class ContactUsService {
    private contactUsRepository;
    private readonly emailService;
    constructor(contactUsRepository: Repository<ContactUs>, emailService: EmailService);
    createContactUs(contactUsData: ContactUsDto): Promise<ContactUs>;
}
