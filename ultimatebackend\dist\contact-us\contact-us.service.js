"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactUsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contact_us_entity_1 = require("./contact-us.entity");
const email_service_1 = require("../email/email.service");
let ContactUsService = class ContactUsService {
    constructor(contactUsRepository, emailService) {
        this.contactUsRepository = contactUsRepository;
        this.emailService = emailService;
    }
    async createContactUs(contactUsData) {
        const fullName = `${contactUsData.firstName} ${contactUsData.lastName}`;
        const newContactUs = this.contactUsRepository.create(contactUsData);
        const savedContactUs = await this.contactUsRepository.save(newContactUs);
        const userEmail = contactUsData.businessEmail;
        const adminEmail = '<EMAIL>';
        try {
            await this.emailService.sendEmail({
                to: [userEmail],
                subject: 'Query Received',
                body: `<p>Dear ${fullName},</p>
               <p>Thank you for reaching out to us regarding ${contactUsData.interestedServices}. 
               We have received your query and will respond shortly.</p>
               <p>Best regards,<br>Ultimate Outsourcing Team</p>`,
                from: process.env.DEFAULT_FROM_EMAIL,
            });
            await this.emailService.sendEmail({
                to: [adminEmail],
                subject: 'New Contact Us Query',
                body: `<p>A new contact form submission has been received:</p>
               <ul>
                 <li><strong>Name:</strong> ${fullName}</li>
                 <li><strong>Email:</strong> ${userEmail}</li>
                 <li><strong>Phone:</strong> ${contactUsData.phoneNumber}</li>
                 <li><strong>Job Title:</strong> ${contactUsData.jobTitle}</li>
                 <li><strong>Company:</strong> ${contactUsData.companyName}</li>
                 <li><strong>Post Code:</strong> ${contactUsData.companyPostCode}</li>
                 <li><strong>Industry:</strong> ${contactUsData.industry}</li>
                 <li><strong>Interested Services:</strong> ${contactUsData.interestedServices}</li>
               </ul>`,
                from: process.env.DEFAULT_FROM_EMAIL,
            });
        }
        catch (error) {
            console.error('Error sending email:', error);
        }
        return savedContactUs;
    }
};
exports.ContactUsService = ContactUsService;
exports.ContactUsService = ContactUsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contact_us_entity_1.ContactUs)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        email_service_1.EmailService])
], ContactUsService);
//# sourceMappingURL=contact-us.service.js.map