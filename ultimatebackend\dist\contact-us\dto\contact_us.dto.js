"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactUsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ContactUsDto {
}
exports.ContactUsDto = ContactUsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'First name of the contact person',
        example: 'John',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last name of the contact person',
        example: 'Doe',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Job title of the contact person',
        example: 'Software Engineer',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "jobTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Company name of the contact person',
        example: 'Tech Corp',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Business email of the contact person',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "businessEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Phone number of the contact person',
        example: '+1234567890',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Post code of the company',
        example: '12345',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "companyPostCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Industry of the company',
        example: 'Information Technology',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "industry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Services the contact person is interested in',
        example: 'Cloud Computing, DevOps',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "interestedServices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Company website of the contact person',
        example: 'https://www.techcorp.com',
    }),
    __metadata("design:type", String)
], ContactUsDto.prototype, "companyWebsite", void 0);
//# sourceMappingURL=contact_us.dto.js.map