"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountryService = void 0;
const common_1 = require("@nestjs/common");
const country_entity_1 = require("./country.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const sector_entity_1 = require("../sector/sector.entity");
let CountryService = class CountryService {
    constructor(countryRepository, sectorRepository) {
        this.countryRepository = countryRepository;
        this.sectorRepository = sectorRepository;
    }
    async createCountry(name, code, timezone, flag, region) {
        await this.countryRepository.insert({
            name,
            code,
            timezone,
            flag,
            region,
        });
    }
    async updateCountry(id, name, code, timezone, flag, region) {
        const country = await this.countryRepository.findOne({ where: { id } });
        await this.countryRepository.update({ id }, {
            name,
            code,
            timezone,
            flag,
            region,
        });
        return country;
    }
    async deleteCountry(id) {
        await this.countryRepository.delete({ id });
    }
    async findAll() {
        return this.countryRepository.find();
    }
    async findOne(id) {
        return this.countryRepository.findOne({ where: { id } });
    }
    async findByName(name) {
        return this.countryRepository.findOne({ where: { name } });
    }
    async getAllRegions() {
        const regions = await this.countryRepository
            .createQueryBuilder('country')
            .select('country.region', 'region')
            .distinct(true)
            .getRawMany();
        return regions.map((region) => region.region);
    }
    async getCountriesAndSector() {
        const countries = await this.countryRepository.find();
        const sectors = await this.sectorRepository.find();
        return { countries, sectors };
    }
    async getAllCountries(region) {
        if (region) {
            const countries = await this.countryRepository.find({
                where: { region },
            });
            return countries;
        }
        else {
            const countries = await this.countryRepository.find();
            return countries;
        }
    }
};
exports.CountryService = CountryService;
exports.CountryService = CountryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(country_entity_1.Country)),
    __param(1, (0, typeorm_2.InjectRepository)(sector_entity_1.Sector)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository])
], CountryService);
//# sourceMappingURL=country.service.js.map