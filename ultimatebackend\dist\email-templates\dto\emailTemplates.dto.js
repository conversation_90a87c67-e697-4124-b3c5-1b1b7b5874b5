"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplatesDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class EmailTemplatesDto {
}
exports.EmailTemplatesDto = EmailTemplatesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique identifier for the email template',
        example: 'd290f1ee-6c54-4b01-90e6-d701748f0851',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Name of the email template',
        example: 'Welcome Email',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Subject of the email template',
        example: 'Welcome to our platform!',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Body content of the email template',
        example: '<p>Thank you for joining us!</p>',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "body", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of the email template',
        example: 'WELCOME',
        enum: [
            'CLIENT_WELCOME',
            'JOB_ALERTS',
            'RESUME_TEMPLATES',
            'SKILLS',
            'EXPERIENCE',
            'QUALIFICATIONS',
            'CLIENT_INTRODUCTION_TO_ACM',
            'SEND_CANDIDATE_TO_CLIENT',
            'MARKETING_EMAIL',
            'INVOICE',
            'OTHER',
        ],
    }),
    (0, class_validator_1.IsEnum)([
        'WELCOME',
        'JOB_ALERTS',
        'RESUME_TEMPLATES',
        'SKILLS',
        'EXPERIENCE',
        'QUALIFICATIONS',
        'MAIL_BOX',
        'ROLES',
        'INVOICES',
        'CALENDAR',
        'EMAIL_TEMPLATES',
        'OTHER',
    ]),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sub type of the email template',
        example: 'CLIENT',
        enum: ['CLIENT', 'CANDIDATE'],
    }),
    (0, class_validator_1.IsEnum)(['CLIENT', 'CANDIDATE']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "sub_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique identifier of the user associated with the email template',
        example: 'a123f1ee-6c54-4b01-90e6-d701748f0851',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], EmailTemplatesDto.prototype, "userId", void 0);
//# sourceMappingURL=emailTemplates.dto.js.map