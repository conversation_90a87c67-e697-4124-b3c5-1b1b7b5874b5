"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEmailTemplateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const emailTemplates_dto_1 = require("./emailTemplates.dto");
const class_validator_1 = require("class-validator");
class UpdateEmailTemplateDto extends (0, swagger_1.PartialType)(emailTemplates_dto_1.EmailTemplatesDto) {
}
exports.UpdateEmailTemplateDto = UpdateEmailTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the email template',
        type: String,
        format: 'uuid',
        example: '123e4567-e89b-12d3-a456-************',
        required: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateEmailTemplateDto.prototype, "id", void 0);
//# sourceMappingURL=updateEmailTemplate.dto.js.map