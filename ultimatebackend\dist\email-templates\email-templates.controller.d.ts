import { EmailTemplatesService } from './email-templates.service';
import { EmailTemplatesDto } from './dto/emailTemplates.dto';
import { UpdateEmailTemplateDto } from './dto/updateEmailTemplate.dto';
export declare class EmailTemplatesController {
    private readonly emailTemplatesService;
    constructor(emailTemplatesService: EmailTemplatesService);
    createEmailTemplate(emailTemplate: EmailTemplatesDto): Promise<import("./emailTemplates.entity").EmailTemplates>;
    getEmailTemplateByType(type: string): Promise<import("./emailTemplates.entity").EmailTemplates[]>;
    getEmailTemplateById(id: string): Promise<import("./emailTemplates.entity").EmailTemplates>;
    updateEmailTemplate(id: string, emailTemplate: UpdateEmailTemplateDto): Promise<UpdateEmailTemplateDto>;
    deleteEmailTemplate(id: string): Promise<void>;
    getAllEmailTemplates(): Promise<import("./emailTemplates.entity").EmailTemplates[]>;
}
