"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplatesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const email_templates_service_1 = require("./email-templates.service");
const emailTemplates_dto_1 = require("./dto/emailTemplates.dto");
const updateEmailTemplate_dto_1 = require("./dto/updateEmailTemplate.dto");
let EmailTemplatesController = class EmailTemplatesController {
    constructor(emailTemplatesService) {
        this.emailTemplatesService = emailTemplatesService;
    }
    async createEmailTemplate(emailTemplate) {
        return this.emailTemplatesService.createEmailTemplate(emailTemplate);
    }
    async getEmailTemplateByType(type) {
        return this.emailTemplatesService.getEmailTemplateByType(type);
    }
    async getEmailTemplateById(id) {
        return this.emailTemplatesService.getEmailTemplateById(id);
    }
    async updateEmailTemplate(id, emailTemplate) {
        return this.emailTemplatesService.updateEmailTemplate(id, emailTemplate);
    }
    async deleteEmailTemplate(id) {
        return this.emailTemplatesService.deleteEmailTemplate(id);
    }
    async getAllEmailTemplates() {
        return this.emailTemplatesService.getAllEmailTemplates();
    }
};
exports.EmailTemplatesController = EmailTemplatesController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new email template' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [emailTemplates_dto_1.EmailTemplatesDto]),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "createEmailTemplate", null);
__decorate([
    (0, common_1.Get)(':type'),
    (0, swagger_1.ApiOperation)({ summary: 'Get email template by type' }),
    (0, swagger_1.ApiParam)({ name: 'type', description: 'Email template type' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "getEmailTemplateByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get email template by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Email template ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "getEmailTemplateById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update email template by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Email template ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, updateEmailTemplate_dto_1.UpdateEmailTemplateDto]),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "updateEmailTemplate", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete email template by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Email template ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "deleteEmailTemplate", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all email templates' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailTemplatesController.prototype, "getAllEmailTemplates", null);
exports.EmailTemplatesController = EmailTemplatesController = __decorate([
    (0, swagger_1.ApiTags)('email-templates'),
    (0, common_1.Controller)('email-templates'),
    __metadata("design:paramtypes", [email_templates_service_1.EmailTemplatesService])
], EmailTemplatesController);
//# sourceMappingURL=email-templates.controller.js.map