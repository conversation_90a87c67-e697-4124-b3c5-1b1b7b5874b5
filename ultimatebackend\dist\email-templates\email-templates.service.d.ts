import { EmailTemplates } from './emailTemplates.entity';
import { Repository } from 'typeorm';
import { EmailTemplatesDto } from './dto/emailTemplates.dto';
import { UpdateEmailTemplateDto } from './dto/updateEmailTemplate.dto';
export declare class EmailTemplatesService {
    private readonly emailTemplatesRepository;
    constructor(emailTemplatesRepository: Repository<EmailTemplates>);
    createEmailTemplate(emailTemplate: Partial<EmailTemplatesDto>): Promise<EmailTemplates>;
    getEmailTemplateByType(type: string): Promise<EmailTemplates[]>;
    getEmailTemplateById(id: string): Promise<EmailTemplates>;
    updateEmailTemplate(id: string, emailTemplate: Partial<EmailTemplatesDto>): Promise<UpdateEmailTemplateDto>;
    deleteEmailTemplate(id: string): Promise<void>;
    getAllEmailTemplates(page?: number, pageSize?: number, searchString?: string): Promise<EmailTemplates[]>;
}
