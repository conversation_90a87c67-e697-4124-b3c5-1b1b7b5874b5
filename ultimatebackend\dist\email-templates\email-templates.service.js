"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplatesService = void 0;
const common_1 = require("@nestjs/common");
const emailTemplates_entity_1 = require("./emailTemplates.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let EmailTemplatesService = class EmailTemplatesService {
    constructor(emailTemplatesRepository) {
        this.emailTemplatesRepository = emailTemplatesRepository;
    }
    async createEmailTemplate(emailTemplate) {
        try {
            const newEmailTemplate = this.emailTemplatesRepository.create(emailTemplate);
            return await this.emailTemplatesRepository.save(newEmailTemplate);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error creating email template', error.message);
        }
    }
    async getEmailTemplateByType(type) {
        try {
            const emailTemplates = await this.emailTemplatesRepository.find({
                where: { type },
            });
            if (!emailTemplates || emailTemplates.length === 0) {
                throw new common_1.NotFoundException('Email template not found');
            }
            ;
            return emailTemplates;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving email template', error.message);
        }
    }
    async getEmailTemplateById(id) {
        try {
            const emailTemplate = await this.emailTemplatesRepository.findOne({
                where: { id },
            });
            if (!emailTemplate) {
                throw new common_1.NotFoundException('Email template not found');
            }
            return emailTemplate;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving email template', error.message);
        }
    }
    async updateEmailTemplate(id, emailTemplate) {
        try {
            const existingTemplate = await this.emailTemplatesRepository.findOne({
                where: { id },
            });
            if (!existingTemplate) {
                throw new common_1.NotFoundException('Email template not found');
            }
            const updatedTemplate = Object.assign(existingTemplate, emailTemplate);
            return await this.emailTemplatesRepository.save(updatedTemplate);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error updating email template', error.message);
        }
    }
    async deleteEmailTemplate(id) {
        try {
            const result = await this.emailTemplatesRepository.delete({ id });
            if (result.affected === 0) {
                throw new common_1.NotFoundException('Email template not found');
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error deleting email template', error.message);
        }
    }
    async getAllEmailTemplates(page = 0, pageSize = 10, searchString = null) {
        try {
            const query = this.emailTemplatesRepository
                .createQueryBuilder('email_template')
                .skip(page * pageSize)
                .take(pageSize);
            if (searchString) {
                query.where('email_template.name LIKE :searchString', {
                    searchString: `%${searchString}%`,
                });
            }
            const emailTemplates = await query.getMany();
            return emailTemplates;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving email templates', error.message);
        }
    }
};
exports.EmailTemplatesService = EmailTemplatesService;
exports.EmailTemplatesService = EmailTemplatesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(emailTemplates_entity_1.EmailTemplates)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EmailTemplatesService);
//# sourceMappingURL=email-templates.service.js.map