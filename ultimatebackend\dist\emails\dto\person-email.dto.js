"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonEmailDto = exports.PersonEmailType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var PersonEmailType;
(function (PersonEmailType) {
    PersonEmailType["PERSONAL"] = "PERSONAL";
    PersonEmailType["BUSINESS"] = "BUSINESS";
})(PersonEmailType || (exports.PersonEmailType = PersonEmailType = {}));
class PersonEmailDto {
}
exports.PersonEmailDto = PersonEmailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '<EMAIL>',
        description: 'The email address of the person',
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PersonEmailDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'PERSONAL',
        description: 'Type of email (PERSONAL or BUSINESS)',
        enum: PersonEmailType,
    }),
    (0, class_validator_1.IsEnum)(PersonEmailType),
    __metadata("design:type", String)
], PersonEmailDto.prototype, "email_type", void 0);
//# sourceMappingURL=person-email.dto.js.map