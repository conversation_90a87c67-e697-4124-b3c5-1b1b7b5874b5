import { PersonEmailDto } from './dto/person-email.dto';
import { PersonEmailService } from './emails.service';
import { PersonEmail } from './emails.entity';
export declare class PersonEmailController {
    private readonly personEmailService;
    constructor(personEmailService: PersonEmailService);
    createWithoutPerson(emailDto: PersonEmailDto): Promise<PersonEmail>;
    create(personId: number, emailDto: PersonEmailDto): Promise<PersonEmail>;
    findAllByPerson(personId: number): Promise<PersonEmail[]>;
    update(emailId: number, emailDto: PersonEmailDto): Promise<PersonEmail>;
    delete(emailId: number): Promise<void>;
}
