"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonEmailController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const person_email_dto_1 = require("./dto/person-email.dto");
const emails_service_1 = require("./emails.service");
const emails_entity_1 = require("./emails.entity");
let PersonEmailController = class PersonEmailController {
    constructor(personEmailService) {
        this.personEmailService = personEmailService;
    }
    async createWithoutPerson(emailDto) {
        return this.personEmailService.createWithoutPerson(emailDto);
    }
    async create(personId, emailDto) {
        return this.personEmailService.create(personId, emailDto);
    }
    async findAllByPerson(personId) {
        return this.personEmailService.findAllByPerson(personId);
    }
    async update(emailId, emailDto) {
        return this.personEmailService.update(emailId, emailDto);
    }
    async delete(emailId) {
        return this.personEmailService.delete(emailId);
    }
};
exports.PersonEmailController = PersonEmailController;
__decorate([
    (0, common_1.Post)('create-without-personid'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new email without person ID' }),
    (0, swagger_1.ApiBody)({ type: person_email_dto_1.PersonEmailDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Email created successfully',
        type: emails_entity_1.PersonEmail,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [person_email_dto_1.PersonEmailDto]),
    __metadata("design:returntype", Promise)
], PersonEmailController.prototype, "createWithoutPerson", null);
__decorate([
    (0, common_1.Post)(':personId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new email for a person' }),
    (0, swagger_1.ApiParam)({ name: 'personId', description: 'ID of the person' }),
    (0, swagger_1.ApiBody)({ type: person_email_dto_1.PersonEmailDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Email created successfully',
        type: emails_entity_1.PersonEmail,
    }),
    __param(0, (0, common_1.Param)('personId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, person_email_dto_1.PersonEmailDto]),
    __metadata("design:returntype", Promise)
], PersonEmailController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':personId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all emails associated with a person' }),
    (0, swagger_1.ApiParam)({ name: 'personId', description: 'ID of the person' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of emails',
        type: [emails_entity_1.PersonEmail],
    }),
    __param(0, (0, common_1.Param)('personId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PersonEmailController.prototype, "findAllByPerson", null);
__decorate([
    (0, common_1.Put)(':emailId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update an email' }),
    (0, swagger_1.ApiParam)({ name: 'emailId', description: 'ID of the email to update' }),
    (0, swagger_1.ApiBody)({ type: person_email_dto_1.PersonEmailDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Updated email', type: emails_entity_1.PersonEmail }),
    __param(0, (0, common_1.Param)('emailId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, person_email_dto_1.PersonEmailDto]),
    __metadata("design:returntype", Promise)
], PersonEmailController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':emailId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an email' }),
    (0, swagger_1.ApiParam)({ name: 'emailId', description: 'ID of the email to delete' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Email deleted successfully' }),
    __param(0, (0, common_1.Param)('emailId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PersonEmailController.prototype, "delete", null);
exports.PersonEmailController = PersonEmailController = __decorate([
    (0, swagger_1.ApiTags)('Person Emails'),
    (0, common_1.Controller)('person-emails'),
    __metadata("design:paramtypes", [emails_service_1.PersonEmailService])
], PersonEmailController);
//# sourceMappingURL=emails.controller.js.map