"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailsModule = void 0;
const common_1 = require("@nestjs/common");
const emails_service_1 = require("./emails.service");
const emails_controller_1 = require("./emails.controller");
const typeorm_1 = require("@nestjs/typeorm");
const emails_entity_1 = require("./emails.entity");
const people_entity_1 = require("../people/people.entity");
let EmailsModule = class EmailsModule {
};
exports.EmailsModule = EmailsModule;
exports.EmailsModule = EmailsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([emails_entity_1.PersonEmail, people_entity_1.People])],
        providers: [emails_service_1.PersonEmailService],
        controllers: [emails_controller_1.PersonEmailController],
    })
], EmailsModule);
//# sourceMappingURL=emails.module.js.map