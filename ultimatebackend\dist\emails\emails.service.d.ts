import { Repository } from 'typeorm';
import { PersonEmailDto } from './dto/person-email.dto';
import { People } from 'src/people/people.entity';
import { PersonEmail } from './emails.entity';
export declare class PersonEmailService {
    private readonly personEmailRepository;
    private readonly peopleRepository;
    constructor(personEmailRepository: Repository<PersonEmail>, peopleRepository: Repository<People>);
    create(personId: number, emailDto: PersonEmailDto): Promise<PersonEmail>;
    createWithoutPerson(emailDto: PersonEmailDto): Promise<PersonEmail>;
    findAllByPerson(personId: number): Promise<PersonEmail[]>;
    update(emailId: number, emailDto: PersonEmailDto): Promise<PersonEmail>;
    delete(emailId: number): Promise<void>;
}
