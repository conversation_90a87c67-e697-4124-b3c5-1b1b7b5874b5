"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonEmailService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("./emails.entity");
let PersonEmailService = class PersonEmailService {
    constructor(personEmailRepository, peopleRepository) {
        this.personEmailRepository = personEmailRepository;
        this.peopleRepository = peopleRepository;
    }
    async create(personId, emailDto) {
        const person = await this.peopleRepository.findOne({ where: { id: personId } });
        if (!person) {
            throw new common_1.NotFoundException(`Person with ID ${personId} not found`);
        }
        const email = this.personEmailRepository.create({ ...emailDto, person });
        return this.personEmailRepository.save(email);
    }
    async createWithoutPerson(emailDto) {
        const email = this.personEmailRepository.create(emailDto);
        return this.personEmailRepository.save(email);
    }
    async findAllByPerson(personId) {
        return this.personEmailRepository.find({ where: { person: { id: personId } } });
    }
    async update(emailId, emailDto) {
        const email = await this.personEmailRepository.findOne({ where: { id: emailId } });
        if (!email) {
            throw new common_1.NotFoundException(`Email with ID ${emailId} not found`);
        }
        Object.assign(email, emailDto);
        return this.personEmailRepository.save(email);
    }
    async delete(emailId) {
        const email = await this.personEmailRepository.findOne({ where: { id: emailId } });
        if (!email) {
            throw new common_1.NotFoundException(`Email with ID ${emailId} not found`);
        }
        await this.personEmailRepository.remove(email);
    }
};
exports.PersonEmailService = PersonEmailService;
exports.PersonEmailService = PersonEmailService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(1, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PersonEmailService);
//# sourceMappingURL=emails.service.js.map