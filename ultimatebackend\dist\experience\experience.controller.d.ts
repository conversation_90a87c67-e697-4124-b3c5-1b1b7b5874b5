import { ExperienceService } from './experience.service';
import { Experience } from './experience.entity';
import { ExperienceDto } from './dto/experience.dto';
import { UpdateExperienceDto } from './dto/updateExperience.dto';
export declare class ExperienceController {
    private readonly experienceService;
    constructor(experienceService: ExperienceService);
    createExperience(experienceDto: ExperienceDto): Promise<Experience>;
    getAllExperiences(): Promise<Experience[]>;
    getExperienceById(id: number): Promise<Experience>;
    updateExperience(id: number, updateExperienceDto: UpdateExperienceDto): Promise<Experience>;
    deleteExperience(id: number): Promise<void>;
}
