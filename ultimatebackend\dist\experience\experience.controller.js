"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExperienceController = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const experience_service_1 = require("./experience.service");
const experience_entity_1 = require("./experience.entity");
const experience_dto_1 = require("./dto/experience.dto");
const updateExperience_dto_1 = require("./dto/updateExperience.dto");
let ExperienceController = class ExperienceController {
    constructor(experienceService) {
        this.experienceService = experienceService;
    }
    async createExperience(experienceDto) {
        return await this.experienceService.createExperience(experienceDto);
    }
    async getAllExperiences() {
        return await this.experienceService.getAllExperiences();
    }
    async getExperienceById(id) {
        return await this.experienceService.getExperienceById(id);
    }
    async updateExperience(id, updateExperienceDto) {
        return await this.experienceService.updateExperience(id, updateExperienceDto);
    }
    async deleteExperience(id) {
        return await this.experienceService.deleteExperience(id);
    }
};
exports.ExperienceController = ExperienceController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Create a new experience' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The experience has been successfully created.',
        type: experience_entity_1.Experience,
    }),
    (0, common_2.Post)(),
    __param(0, (0, common_2.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [experience_dto_1.ExperienceDto]),
    __metadata("design:returntype", Promise)
], ExperienceController.prototype, "createExperience", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get all experiences' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of all experiences.',
        type: [experience_entity_1.Experience],
    }),
    (0, common_2.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ExperienceController.prototype, "getAllExperiences", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get experience by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The experience with the given ID.',
        type: experience_entity_1.Experience,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Experience not found.' }),
    (0, common_2.Get)(':id'),
    __param(0, (0, common_2.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ExperienceController.prototype, "getExperienceById", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Update an experience' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The experience has been successfully updated.',
        type: experience_entity_1.Experience,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Experience not found.' }),
    (0, common_2.Put)(':id'),
    __param(0, (0, common_2.Param)('id')),
    __param(1, (0, common_2.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateExperience_dto_1.UpdateExperienceDto]),
    __metadata("design:returntype", Promise)
], ExperienceController.prototype, "updateExperience", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Delete an experience' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The experience has been successfully deleted.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Experience not found.' }),
    (0, common_2.Delete)(':id'),
    __param(0, (0, common_2.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ExperienceController.prototype, "deleteExperience", null);
exports.ExperienceController = ExperienceController = __decorate([
    (0, swagger_1.ApiTags)('Experience'),
    (0, common_1.Controller)('experience'),
    __metadata("design:paramtypes", [experience_service_1.ExperienceService])
], ExperienceController);
//# sourceMappingURL=experience.controller.js.map