import { Experience } from './experience.entity';
import { Repository } from 'typeorm';
import { ExperienceDto } from './dto/experience.dto';
import { UpdateExperienceDto } from './dto/updateExperience.dto';
export declare class ExperienceService {
    private readonly experienceRepository;
    constructor(experienceRepository: Repository<Experience>);
    createExperience(experienceDto: ExperienceDto): Promise<Experience>;
    getAllExperiences(): Promise<Experience[]>;
    getExperienceById(id: number): Promise<Experience>;
    updateExperience(id: number, dto: UpdateExperienceDto): Promise<Experience>;
    deleteExperience(id: number): Promise<void>;
}
