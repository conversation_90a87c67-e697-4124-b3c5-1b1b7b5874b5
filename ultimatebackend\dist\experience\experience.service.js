"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExperienceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const experience_entity_1 = require("./experience.entity");
const typeorm_2 = require("typeorm");
let ExperienceService = class ExperienceService {
    constructor(experienceRepository) {
        this.experienceRepository = experienceRepository;
    }
    async createExperience(experienceDto) {
        const experience = this.experienceRepository.create(experienceDto);
        return await this.experienceRepository.save(experience);
    }
    async getAllExperiences() {
        return await this.experienceRepository.find();
    }
    async getExperienceById(id) {
        return await this.experienceRepository.findOneOrFail({ where: { id } });
    }
    async updateExperience(id, dto) {
        await this.experienceRepository.update(id, dto);
        return await this.getExperienceById(id);
    }
    async deleteExperience(id) {
        await this.experienceRepository.delete(id);
    }
};
exports.ExperienceService = ExperienceService;
exports.ExperienceService = ExperienceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(experience_entity_1.Experience)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ExperienceService);
//# sourceMappingURL=experience.service.js.map