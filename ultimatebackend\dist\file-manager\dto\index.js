"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListFilesDto = exports.UploadFileDto = exports.MoveToTrashDto = exports.RenameFileDto = exports.CreateFolderDto = void 0;
var create_folder_dto_1 = require("./create-folder.dto");
Object.defineProperty(exports, "CreateFolderDto", { enumerable: true, get: function () { return create_folder_dto_1.CreateFolderDto; } });
var rename_file_dto_1 = require("./rename-file.dto");
Object.defineProperty(exports, "RenameFileDto", { enumerable: true, get: function () { return rename_file_dto_1.RenameFileDto; } });
var move_to_trash_dto_1 = require("./move-to-trash.dto");
Object.defineProperty(exports, "MoveToTrashDto", { enumerable: true, get: function () { return move_to_trash_dto_1.MoveToTrashDto; } });
var upload_file_dto_1 = require("./upload-file.dto");
Object.defineProperty(exports, "UploadFileDto", { enumerable: true, get: function () { return upload_file_dto_1.UploadFileDto; } });
var list_files_dto_1 = require("./list-files.dto");
Object.defineProperty(exports, "ListFilesDto", { enumerable: true, get: function () { return list_files_dto_1.ListFilesDto; } });
//# sourceMappingURL=index.js.map