import { FileManagerService } from './file-manager.service';
import { CreateFolderDto, RenameFileDto, MoveToTrashDto, UploadFileDto, ListFilesDto } from './dto';
import { FileManager } from './file-manager.entity';
export declare class FileManagerController {
    private readonly fileManagerService;
    constructor(fileManagerService: FileManagerService);
    createFolder(req: any, createFolderDto: CreateFolderDto): Promise<FileManager>;
    listFiles(req: any, listFilesDto: ListFilesDto): Promise<FileManager[]>;
    getUploadUrl(req: any, uploadFileDto: UploadFileDto): Promise<{
        uploadUrl: string;
        fileId: number;
    }>;
    getDownloadUrl(req: any, fileId: number): Promise<{
        downloadUrl: string;
    }>;
    renameFile(req: any, fileId: number, renameFileDto: RenameFileDto): Promise<FileManager>;
    moveToTrash(req: any, moveToTrashDto: MoveToTrashDto): Promise<{
        message: string;
    }>;
    restoreFromTrash(req: any, fileId: number): Promise<FileManager>;
    permanentlyDelete(req: any, fileId: number): Promise<{
        message: string;
    }>;
    getFolderBreadcrumb(req: any, folderId: number): Promise<Array<{
        id: number;
        name: string;
    }>>;
}
