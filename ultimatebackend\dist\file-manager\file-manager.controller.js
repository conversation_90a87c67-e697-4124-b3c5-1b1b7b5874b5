"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManagerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const file_manager_service_1 = require("./file-manager.service");
const dto_1 = require("./dto");
const file_manager_entity_1 = require("./file-manager.entity");
let FileManagerController = class FileManagerController {
    constructor(fileManagerService) {
        this.fileManagerService = fileManagerService;
    }
    async createFolder(req, createFolderDto) {
        const userId = req.user?.id || 1;
        return this.fileManagerService.createFolder(userId, createFolderDto);
    }
    async listFiles(req, listFilesDto) {
        const userId = req.user?.id || 1;
        return this.fileManagerService.listFiles(userId, listFilesDto);
    }
    async getUploadUrl(req, uploadFileDto) {
        const userId = req.user?.id || 1;
        return this.fileManagerService.getUploadUrl(userId, uploadFileDto);
    }
    async getDownloadUrl(req, fileId) {
        const userId = req.user?.id || 1;
        const downloadUrl = await this.fileManagerService.getDownloadUrl(fileId, userId);
        return { downloadUrl };
    }
    async renameFile(req, fileId, renameFileDto) {
        const userId = req.user?.id || 1;
        return this.fileManagerService.renameFile(fileId, userId, renameFileDto);
    }
    async moveToTrash(req, moveToTrashDto) {
        const userId = req.user?.id || 1;
        await this.fileManagerService.moveToTrash(userId, moveToTrashDto);
        return { message: 'Files moved to trash successfully' };
    }
    async restoreFromTrash(req, fileId) {
        const userId = req.user?.id || 1;
        return this.fileManagerService.restoreFromTrash(fileId, userId);
    }
    async permanentlyDelete(req, fileId) {
        const userId = req.user?.id || 1;
        await this.fileManagerService.permanentlyDelete(fileId, userId);
        return { message: 'File permanently deleted successfully' };
    }
    async getFolderBreadcrumb(req, folderId) {
        return [
            { id: 0, name: 'Root' },
            { id: folderId, name: 'Current Folder' },
        ];
    }
};
exports.FileManagerController = FileManagerController;
__decorate([
    (0, common_1.Post)('folders'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new folder' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Folder created successfully',
        type: file_manager_entity_1.FileManager,
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.CreateFolderDto]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "createFolder", null);
__decorate([
    (0, common_1.Get)('files'),
    (0, swagger_1.ApiOperation)({ summary: 'List files and folders' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Files and folders retrieved successfully',
        type: [file_manager_entity_1.FileManager],
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.ListFilesDto]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "listFiles", null);
__decorate([
    (0, common_1.Post)('files/upload-url'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pre-signed URL for file upload' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Upload URL generated successfully',
        schema: {
            type: 'object',
            properties: {
                uploadUrl: { type: 'string' },
                fileId: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.UploadFileDto]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "getUploadUrl", null);
__decorate([
    (0, common_1.Get)('files/:id/download-url'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pre-signed URL for file download' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Download URL generated successfully',
        schema: {
            type: 'object',
            properties: {
                downloadUrl: { type: 'string' },
            },
        },
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "getDownloadUrl", null);
__decorate([
    (0, common_1.Patch)('files/:id/rename'),
    (0, swagger_1.ApiOperation)({ summary: 'Rename a file or folder' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File or folder ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File or folder renamed successfully',
        type: file_manager_entity_1.FileManager,
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dto_1.RenameFileDto]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "renameFile", null);
__decorate([
    (0, common_1.Post)('files/trash'),
    (0, swagger_1.ApiOperation)({ summary: 'Move files/folders to trash' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Files moved to trash successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.MoveToTrashDto]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "moveToTrash", null);
__decorate([
    (0, common_1.Patch)('files/:id/restore'),
    (0, swagger_1.ApiOperation)({ summary: 'Restore file/folder from trash' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File or folder ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File restored from trash successfully',
        type: file_manager_entity_1.FileManager,
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "restoreFromTrash", null);
__decorate([
    (0, common_1.Delete)('files/:id/permanent'),
    (0, swagger_1.ApiOperation)({ summary: 'Permanently delete a file/folder' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File or folder ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'File permanently deleted successfully',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "permanentlyDelete", null);
__decorate([
    (0, common_1.Get)('folders/:id/breadcrumb'),
    (0, swagger_1.ApiOperation)({ summary: 'Get folder breadcrumb path' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Folder ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Breadcrumb path retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number' },
                    name: { type: 'string' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FileManagerController.prototype, "getFolderBreadcrumb", null);
exports.FileManagerController = FileManagerController = __decorate([
    (0, common_1.Controller)('file-manager'),
    (0, swagger_1.ApiTags)('File Manager'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [file_manager_service_1.FileManagerService])
], FileManagerController);
//# sourceMappingURL=file-manager.controller.js.map