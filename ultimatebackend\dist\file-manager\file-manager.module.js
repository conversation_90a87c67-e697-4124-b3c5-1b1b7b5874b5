"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManagerModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const file_manager_controller_1 = require("./file-manager.controller");
const file_manager_service_1 = require("./file-manager.service");
const file_manager_entity_1 = require("./file-manager.entity");
const s3bucket_module_1 = require("../s3bucket/s3bucket.module");
let FileManagerModule = class FileManagerModule {
};
exports.FileManagerModule = FileManagerModule;
exports.FileManagerModule = FileManagerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([file_manager_entity_1.FileManager]),
            s3bucket_module_1.S3bucketModule,
        ],
        controllers: [file_manager_controller_1.FileManagerController],
        providers: [file_manager_service_1.FileManagerService],
        exports: [file_manager_service_1.FileManagerService],
    })
], FileManagerModule);
//# sourceMappingURL=file-manager.module.js.map