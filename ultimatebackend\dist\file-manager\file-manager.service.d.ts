import { Repository } from 'typeorm';
import { FileManager } from './file-manager.entity';
import { S3bucketService } from '../s3bucket/s3bucket.service';
import { CreateFolderDto, RenameFileDto, MoveToTrashDto, UploadFileDto, ListFilesDto } from './dto';
export declare class FileManagerService {
    private readonly fileManagerRepository;
    private readonly s3Service;
    constructor(fileManagerRepository: Repository<FileManager>, s3Service: S3bucketService);
    createFolder(userId: number, createFolderDto: CreateFolderDto): Promise<FileManager>;
    getUploadUrl(userId: number, uploadFileDto: UploadFileDto): Promise<{
        uploadUrl: string;
        fileId: number;
    }>;
    getDownloadUrl(fileId: number, userId: number): Promise<string>;
    listFiles(userId: number, listFilesDto: ListFilesDto): Promise<FileManager[]>;
    renameFile(fileId: number, userId: number, renameFileDto: RenameFileDto): Promise<FileManager>;
    moveToTrash(userId: number, moveToTrashDto: MoveToTrashDto): Promise<void>;
    restoreFromTrash(fileId: number, userId: number): Promise<FileManager>;
    permanentlyDelete(fileId: number, userId: number): Promise<void>;
    private findFileById;
    private generateS3FolderPath;
}
