"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManagerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const file_manager_entity_1 = require("./file-manager.entity");
const s3bucket_service_1 = require("../s3bucket/s3bucket.service");
let FileManagerService = class FileManagerService {
    constructor(fileManagerRepository, s3Service) {
        this.fileManagerRepository = fileManagerRepository;
        this.s3Service = s3Service;
    }
    async createFolder(userId, createFolderDto) {
        const { name, parentId, description } = createFolderDto;
        if (parentId) {
            const parentFolder = await this.findFileById(parentId, userId);
            if (!parentFolder.is_folder) {
                throw new common_1.BadRequestException('Parent must be a folder');
            }
        }
        const existingFolder = await this.fileManagerRepository.findOne({
            where: {
                user_id: userId,
                parent_id: parentId || (0, typeorm_2.IsNull)(),
                name,
                is_folder: true,
                deleted_at: (0, typeorm_2.IsNull)(),
            },
        });
        if (existingFolder) {
            throw new common_1.BadRequestException('Folder with this name already exists in this location');
        }
        const folder = this.fileManagerRepository.create({
            user_id: userId,
            name,
            is_folder: true,
            parent_id: parentId,
            description,
        });
        return await this.fileManagerRepository.save(folder);
    }
    async getUploadUrl(userId, uploadFileDto) {
        const { fileName, mimeType, fileSize, parentId, description } = uploadFileDto;
        if (parentId) {
            const parentFolder = await this.findFileById(parentId, userId);
            if (!parentFolder.is_folder) {
                throw new common_1.BadRequestException('Parent must be a folder');
            }
        }
        const folderPath = await this.generateS3FolderPath(userId, parentId);
        const { uploadUrl, s3Key } = await this.s3Service.generateUploadUrl(fileName, mimeType, userId, folderPath);
        const file = this.fileManagerRepository.create({
            user_id: userId,
            name: fileName,
            is_folder: false,
            s3_key: s3Key,
            parent_id: parentId,
            mime_type: mimeType,
            file_size: fileSize,
            description,
            s3_url: `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${s3Key}`,
        });
        const savedFile = await this.fileManagerRepository.save(file);
        return {
            uploadUrl,
            fileId: savedFile.id,
        };
    }
    async getDownloadUrl(fileId, userId) {
        const file = await this.findFileById(fileId, userId);
        if (file.is_folder) {
            throw new common_1.BadRequestException('Cannot download a folder');
        }
        if (!file.s3_key) {
            throw new common_1.BadRequestException('File has no S3 key');
        }
        return await this.s3Service.generateDownloadUrl(file.s3_key);
    }
    async listFiles(userId, listFilesDto) {
        const { parentId, includeTrash, search } = listFilesDto;
        if (parentId) {
            await this.findFileById(parentId, userId);
        }
        const queryBuilder = this.fileManagerRepository
            .createQueryBuilder('file')
            .where('file.user_id = :userId', { userId })
            .andWhere('file.parent_id = :parentId', { parentId: parentId || null });
        if (!includeTrash) {
            queryBuilder.andWhere('file.deleted_at IS NULL');
        }
        if (search) {
            queryBuilder.andWhere('file.name ILIKE :search', { search: `%${search}%` });
        }
        queryBuilder.orderBy('file.is_folder', 'DESC').addOrderBy('file.name', 'ASC');
        return await queryBuilder.getMany();
    }
    async renameFile(fileId, userId, renameFileDto) {
        const { newName } = renameFileDto;
        const file = await this.findFileById(fileId, userId);
        const existingFile = await this.fileManagerRepository.findOne({
            where: {
                user_id: userId,
                parent_id: file.parent_id || (0, typeorm_2.IsNull)(),
                name: newName,
                deleted_at: (0, typeorm_2.IsNull)(),
            },
        });
        if (existingFile && existingFile.id !== fileId) {
            throw new common_1.BadRequestException('File with this name already exists in this location');
        }
        if (!file.is_folder && file.s3_key) {
            const folderPath = await this.generateS3FolderPath(userId, file.parent_id);
            const newS3Key = `users/${userId}/${folderPath}${folderPath ? '/' : ''}${Date.now()}-${newName}`;
            await this.s3Service.copyObject(file.s3_key, newS3Key);
            await this.s3Service.deleteObjectByKey(file.s3_key);
            file.s3_key = newS3Key;
            file.s3_url = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${newS3Key}`;
        }
        file.name = newName;
        return await this.fileManagerRepository.save(file);
    }
    async moveToTrash(userId, moveToTrashDto) {
        const { fileIds } = moveToTrashDto;
        for (const fileId of fileIds) {
            const file = await this.findFileById(fileId, userId);
            file.deleted_at = new Date();
            await this.fileManagerRepository.save(file);
        }
    }
    async restoreFromTrash(fileId, userId) {
        const file = await this.findFileById(fileId, userId);
        if (!file.deleted_at) {
            throw new common_1.BadRequestException('File is not in trash');
        }
        file.deleted_at = null;
        return await this.fileManagerRepository.save(file);
    }
    async permanentlyDelete(fileId, userId) {
        const file = await this.findFileById(fileId, userId);
        if (!file.is_folder && file.s3_key) {
            await this.s3Service.deleteObjectByKey(file.s3_key);
        }
        await this.fileManagerRepository.remove(file);
    }
    async findFileById(fileId, userId) {
        const file = await this.fileManagerRepository.findOne({
            where: { id: fileId, user_id: userId },
        });
        if (!file) {
            throw new common_1.NotFoundException('File or folder not found');
        }
        return file;
    }
    async generateS3FolderPath(userId, parentId) {
        if (!parentId) {
            return '';
        }
        const pathParts = [];
        let currentParentId = parentId;
        while (currentParentId) {
            const parent = await this.findFileById(currentParentId, userId);
            pathParts.unshift(parent.name);
            currentParentId = parent.parent_id;
        }
        return pathParts.join('/');
    }
};
exports.FileManagerService = FileManagerService;
exports.FileManagerService = FileManagerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(file_manager_entity_1.FileManager)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        s3bucket_service_1.S3bucketService])
], FileManagerService);
//# sourceMappingURL=file-manager.service.js.map