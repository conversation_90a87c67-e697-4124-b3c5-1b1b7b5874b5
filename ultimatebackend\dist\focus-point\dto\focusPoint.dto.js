"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FocusPointDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class FocusPointDto {
}
exports.FocusPointDto = FocusPointDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the focus point',
        enum: ['INFO', 'QUERY', 'SUGGESTION', 'ISSUE', 'OTHER'],
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['INFO', 'QUERY', 'SUGGESTION', 'ISSUE', 'OTHER']),
    __metadata("design:type", String)
], FocusPointDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message associated with the focus point',
        type: String,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FocusPointDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date when the focus point was added',
        type: Date,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], FocusPointDto.prototype, "added_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date when the focus point was last updated',
        type: Date,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], FocusPointDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the focus point is soft deleted',
        type: Boolean,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FocusPointDto.prototype, "is_deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date when the focus point was soft deleted',
        type: Date,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], FocusPointDto.prototype, "deleted_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the associated user',
        type: String,
        format: 'uuid',
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], FocusPointDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the associated role',
        type: Number,
        nullable: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], FocusPointDto.prototype, "roleId", void 0);
//# sourceMappingURL=focusPoint.dto.js.map