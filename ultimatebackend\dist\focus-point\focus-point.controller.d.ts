import { FocusPointService } from './focus-point.service';
import { FocusPointDto } from './dto/focusPoint.dto';
import { UpdateFocusPointDto } from './dto/updateFocusPoint.dto';
export declare class FocusPointController {
    private readonly focusPointService;
    constructor(focusPointService: FocusPointService);
    createFocusPoint(focusPointDto: FocusPointDto): Promise<import("./focusPoint.entity").FocusPoint>;
    getAllFocusPoints(searchString?: string, roleId?: string): Promise<any>;
    deleteFocusPoint(id: string): Promise<void>;
    getFocusPointById(id: string): Promise<import("./focusPoint.entity").FocusPoint>;
    updateFocusPoint(id: string, focusPointDto: UpdateFocusPointDto): Promise<void>;
}
