"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FocusPointController = void 0;
const common_1 = require("@nestjs/common");
const focus_point_service_1 = require("./focus-point.service");
const swagger_1 = require("@nestjs/swagger");
const focusPoint_dto_1 = require("./dto/focusPoint.dto");
const updateFocusPoint_dto_1 = require("./dto/updateFocusPoint.dto");
let FocusPointController = class FocusPointController {
    constructor(focusPointService) {
        this.focusPointService = focusPointService;
    }
    async createFocusPoint(focusPointDto) {
        return this.focusPointService.createFocusPoint(focusPointDto);
    }
    async getAllFocusPoints(searchString = null, roleId = null) {
        return this.focusPointService.getAllFocusPoints(searchString, roleId);
    }
    async deleteFocusPoint(id) {
        return this.focusPointService.deleteFocusPoint(id);
    }
    async getFocusPointById(id) {
        return this.focusPointService.getFocusPointById(id);
    }
    async updateFocusPoint(id, focusPointDto) {
        return this.focusPointService.updateFocusPoint(id, focusPointDto);
    }
};
exports.FocusPointController = FocusPointController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new focus point' }),
    (0, swagger_1.ApiBody)({
        type: focusPoint_dto_1.FocusPointDto,
        description: 'Focus point data',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [focusPoint_dto_1.FocusPointDto]),
    __metadata("design:returntype", Promise)
], FocusPointController.prototype, "createFocusPoint", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all focus points' }),
    (0, swagger_1.ApiQuery)({ name: 'searchString', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'roleId', required: false, type: String }),
    __param(0, (0, common_1.Query)('searchString')),
    __param(1, (0, common_1.Query)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], FocusPointController.prototype, "getAllFocusPoints", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a focus point' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FocusPointController.prototype, "deleteFocusPoint", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a focus point by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FocusPointController.prototype, "getFocusPointById", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a focus point' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, updateFocusPoint_dto_1.UpdateFocusPointDto]),
    __metadata("design:returntype", Promise)
], FocusPointController.prototype, "updateFocusPoint", null);
exports.FocusPointController = FocusPointController = __decorate([
    (0, swagger_1.ApiTags)('Focus Point'),
    (0, common_1.Controller)('focus-point'),
    __metadata("design:paramtypes", [focus_point_service_1.FocusPointService])
], FocusPointController);
//# sourceMappingURL=focus-point.controller.js.map