"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FocusPointModule = void 0;
const common_1 = require("@nestjs/common");
const focus_point_service_1 = require("./focus-point.service");
const focus_point_controller_1 = require("./focus-point.controller");
const focusPoint_entity_1 = require("./focusPoint.entity");
const typeorm_1 = require("@nestjs/typeorm");
let FocusPointModule = class FocusPointModule {
};
exports.FocusPointModule = FocusPointModule;
exports.FocusPointModule = FocusPointModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([focusPoint_entity_1.FocusPoint])],
        providers: [focus_point_service_1.FocusPointService],
        controllers: [focus_point_controller_1.FocusPointController],
    })
], FocusPointModule);
//# sourceMappingURL=focus-point.module.js.map