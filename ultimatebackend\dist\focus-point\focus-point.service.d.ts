import { FocusPoint } from './focusPoint.entity';
import { Repository } from 'typeorm';
import { FocusPointDto } from './dto/focusPoint.dto';
import { UpdateFocusPointDto } from './dto/updateFocusPoint.dto';
export declare class FocusPointService {
    private focusPointRepository;
    constructor(focusPointRepository: Repository<FocusPoint>);
    createFocusPoint(focusPoint: FocusPointDto): Promise<FocusPoint>;
    deleteFocusPoint(id: string): Promise<void>;
    getFocusPointById(id: string): Promise<FocusPoint>;
    getAllFocusPoints(searchString?: string, roleId?: string): Promise<any>;
    updateFocusPoint(id: string, focusPoint: UpdateFocusPointDto): Promise<void>;
    getFocusPointGroupByType(roleId: string, userId: string): Promise<any>;
}
