"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FocusPointService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const focusPoint_entity_1 = require("./focusPoint.entity");
const typeorm_2 = require("typeorm");
let FocusPointService = class FocusPointService {
    constructor(focusPointRepository) {
        this.focusPointRepository = focusPointRepository;
    }
    async createFocusPoint(focusPoint) {
        try {
            const newFocusPoint = this.focusPointRepository.create(focusPoint);
            return await this.focusPointRepository.save(newFocusPoint);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error creating focus point', error.message);
        }
    }
    async deleteFocusPoint(id) {
        try {
            const focusPoint = await this.focusPointRepository.findOneBy({ id });
            if (!focusPoint) {
                throw new common_1.NotFoundException('Focus point not found');
            }
            await this.focusPointRepository.update(id, {
                is_deleted: true,
                deleted_at: new Date(),
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error deleting focus point', error.message);
        }
    }
    async getFocusPointById(id) {
        try {
            const focusPoint = await this.focusPointRepository.findOneBy({ id });
            if (!focusPoint) {
                throw new common_1.NotFoundException('Focus point not found');
            }
            return focusPoint;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving focus point', error.message);
        }
    }
    async getAllFocusPoints(searchString = null, roleId = null) {
        try {
            const queryBuilder = this.focusPointRepository.createQueryBuilder('focusPoint');
            queryBuilder.leftJoinAndSelect('focusPoint.user', 'user');
            if (searchString) {
                queryBuilder.andWhere('(focusPoint.message LIKE :searchString OR focusPoint.type LIKE :searchString)', { searchString: `%${searchString}%` });
            }
            if (roleId) {
                queryBuilder.andWhere('focusPoint.roleId = :roleId', { roleId });
            }
            queryBuilder.groupBy('focusPoint.id');
            queryBuilder.addGroupBy('user.id');
            queryBuilder.addGroupBy('user.first_name');
            queryBuilder.addGroupBy('user.last_name');
            queryBuilder.orderBy('focusPoint.type', 'ASC');
            queryBuilder.addOrderBy('focusPoint.added_at', 'DESC');
            const results = await queryBuilder.getMany();
            const groupedResults = results.reduce((acc, focusPoint) => {
                const type = focusPoint.type;
                if (!acc[type]) {
                    acc[type] = [];
                }
                acc[type].push({
                    id: focusPoint.id,
                    type: focusPoint.type,
                    message: focusPoint.message,
                    added_at: focusPoint.added_at,
                    updated_at: focusPoint.updated_at,
                    is_deleted: focusPoint.is_deleted,
                    deleted_at: focusPoint.deleted_at,
                    userId: focusPoint.userId,
                    roleId: focusPoint.roleId,
                    fullName: focusPoint.user
                        ? `${focusPoint.user.first_name} ${focusPoint.user.last_name}`
                        : 'Unknown User',
                });
                return acc;
            }, {});
            return groupedResults;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving focus points', error.message || error);
        }
    }
    async updateFocusPoint(id, focusPoint) {
        try {
            const existingFocusPoint = await this.focusPointRepository.findOneBy({
                id,
            });
            if (!existingFocusPoint) {
                throw new common_1.NotFoundException('Focus point not found');
            }
            await this.focusPointRepository.update(id, focusPoint);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error updating focus point', error.message);
        }
    }
    async getFocusPointGroupByType(roleId, userId) {
        try {
            const focusPoints = await this.focusPointRepository
                .createQueryBuilder('focusPoint')
                .leftJoinAndSelect('focusPoint.user', 'user')
                .leftJoinAndSelect('focusPoint.role', 'role')
                .where('focusPoint.roleId = :roleId', { roleId })
                .andWhere('focusPoint.userId = :userId', { userId })
                .select([
                'focusPoint.type',
                'focusPoint.message',
                'focusPoint.added_at',
                'focusPoint.updated_at',
                'user.id',
                'user.first_name',
                'user.last_name',
                'user.email',
                'role.id',
                'role.title',
            ])
                .orderBy('focusPoint.type', 'ASC')
                .getMany();
            const groupedFocusPoints = focusPoints.reduce((acc, focusPoint) => {
                if (!acc[focusPoint.type]) {
                    acc[focusPoint.type] = [];
                }
                acc[focusPoint.type].push(focusPoint);
                return acc;
            }, {});
            return groupedFocusPoints;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error retrieving focus points grouped by type', error.message);
        }
    }
};
exports.FocusPointService = FocusPointService;
exports.FocusPointService = FocusPointService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(focusPoint_entity_1.FocusPoint)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], FocusPointService);
//# sourceMappingURL=focus-point.service.js.map