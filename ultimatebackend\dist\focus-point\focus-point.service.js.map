{"version": 3, "file": "focus-point.service.js", "sourceRoot": "", "sources": ["../../src/focus-point/focus-point.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,6CAAmD;AACnD,2DAAiD;AACjD,qCAAqC;AAK9B,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAEU,oBAA4C;QAA5C,yBAAoB,GAApB,oBAAoB,CAAwB;IACnD,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CAAC,UAAyB;QAC9C,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnE,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,4BAA4B,EAC5B,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,EAAE;gBACzC,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,4BAA4B,EAC5B,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU;QAChC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,8BAA8B,EAC9B,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,eAAuB,IAAI,EAC3B,SAAiB,IAAI;QAErB,IAAI,CAAC;YACH,MAAM,YAAY,GAChB,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAE7D,YAAY,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAE1D,IAAI,YAAY,EAAE,CAAC;gBACjB,YAAY,CAAC,QAAQ,CACnB,+EAA+E,EAC/E,EAAE,YAAY,EAAE,IAAI,YAAY,GAAG,EAAE,CACtC,CAAC;YACJ,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YACnE,CAAC;YAKD,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACtC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACnC,YAAY,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC3C,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAG1C,YAAY,CAAC,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YAC/C,YAAY,CAAC,UAAU,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAEvD,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAI7C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;gBACxD,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gBAG7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;oBACf,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACjB,CAAC;gBAGD,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;oBACb,EAAE,EAAE,UAAU,CAAC,EAAE;oBACjB,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,UAAU,EAAE,UAAU,CAAC,UAAU;oBACjC,UAAU,EAAE,UAAU,CAAC,UAAU;oBACjC,UAAU,EAAE,UAAU,CAAC,UAAU;oBACjC,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,QAAQ,EAAE,UAAU,CAAC,IAAI;wBACvB,CAAC,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC9D,CAAC,CAAC,cAAc;iBACnB,CAAC,CAAC;gBAEH,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,+BAA+B,EAC/B,KAAK,CAAC,OAAO,IAAI,KAAK,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,EAAU,EACV,UAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;gBACnE,EAAE;aACH,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,CAAC,CAAC;YACvD,CAAC;YACD,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,4BAA4B,EAC5B,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;IAkBD,KAAK,CAAC,wBAAwB,CAAC,MAAc,EAAE,MAAc;QAC3D,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB;iBAChD,kBAAkB,CAAC,YAAY,CAAC;iBAChC,iBAAiB,CAAC,iBAAiB,EAAE,MAAM,CAAC;iBAC5C,iBAAiB,CAAC,iBAAiB,EAAE,MAAM,CAAC;iBAC5C,KAAK,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC;iBAChD,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC;iBACnD,MAAM,CAAC;gBACN,iBAAiB;gBACjB,oBAAoB;gBACpB,qBAAqB;gBACrB,uBAAuB;gBACvB,SAAS;gBACT,iBAAiB;gBACjB,gBAAgB;gBAChB,YAAY;gBACZ,SAAS;gBACT,YAAY;aACb,CAAC;iBACD,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC;iBACjC,OAAO,EAAE,CAAC;YAGb,MAAM,kBAAkB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;gBAChE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC5B,CAAC;gBACD,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtC,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,qCAA4B,CACpC,+CAA+C,EAC/C,KAAK,CAAC,OAAO,CACd,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AAzMY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;qCACC,oBAAU;GAH/B,iBAAiB,CAyM7B"}