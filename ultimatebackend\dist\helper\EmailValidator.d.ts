export declare const disposableDomains: string[];
interface EmailValidationResult {
    isValid: boolean;
    validators: {
        regex: {
            valid: boolean;
        };
        disposable: {
            valid: boolean;
        };
        mx: {
            valid: boolean;
        };
    };
    reasons?: string[];
}
export declare const validateEmail: (email: string) => Promise<EmailValidationResult>;
export declare const isBusinessEmail: (email: string, websiteLink: string) => boolean;
export declare const extractWordsFromDomain: (domain: string) => string[];
export declare const levenshteinDistance: (str1: string, str2: string) => number;
export {};
