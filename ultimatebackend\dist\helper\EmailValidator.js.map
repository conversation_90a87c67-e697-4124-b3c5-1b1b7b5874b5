{"version": 3, "file": "EmailValidator.js", "sourceRoot": "", "sources": ["../../src/helper/EmailValidator.ts"], "names": [], "mappings": ";;;AAAA,2CAAyC;AAE5B,QAAA,iBAAiB,GAAG;IAC/B,gBAAgB;IAChB,aAAa;IACb,cAAc;IACd,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,eAAe;IACf,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IACjB,eAAe;IACf,mBAAmB;IACnB,cAAc;IACd,aAAa;IACb,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,iBAAiB;IACjB,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,wBAAwB;IACxB,mBAAmB;IACnB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;CACnB,CAAC;AAYK,MAAM,aAAa,GAAG,KAAK,EAAE,KAAa,EAAkC,EAAE;IACnF,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,MAAM,MAAM,GAA0B;QACpC,OAAO,EAAE,IAAI;QACb,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;YACtB,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;YAC3B,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;SACpB;QACD,OAAO;KACR,CAAC;IAEF,MAAM,UAAU,GACd,sIAAsI,CAAC;IAGzI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACvC,CAAC;IAGD,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI,MAAM,EAAE,CAAC;QAEX,IAAI,yBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACrD,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAA,oBAAS,EAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAGD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAC1E,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAjDW,QAAA,aAAa,iBAiDxB;AAEK,MAAM,eAAe,GAAG,CAAC,KAAa,EAAE,WAAmB,EAAW,EAAE;IAC7E,IAAI,CAAC;QACH,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QAEzC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QAE/B,MAAM,gBAAgB,GAAG,WAAW;aACjC,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC;aACtC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACtB,MAAM,aAAa,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACrD,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAGpD,IAAI,WAAW,KAAK,aAAa,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,WAAW,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,IACE,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACvC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,EACvC,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAGD,MAAM,UAAU,GAAG,IAAA,8BAAsB,EAAC,aAAa,CAAC,CAAC;QACzD,MAAM,YAAY,GAAG,IAAA,8BAAsB,EAAC,eAAe,CAAC,CAAC;QAE7D,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CACnC,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,CACvE,CAAC;QAEF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,QAAQ,GAAG,IAAA,2BAAmB,EAAC,aAAa,EAAE,eAAe,CAAC,CAAC;YACrE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;YAEzE,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,SAAS,GAAG,GAAG,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAvEW,QAAA,eAAe,mBAuE1B;AAEK,MAAM,sBAAsB,GAAG,CAAC,MAAc,EAAY,EAAE;IACjE,MAAM,UAAU,GAAG,MAAM;SACtB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;SACrB,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC;SACnC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC;SACpC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC;SACpC,WAAW,EAAE,CAAC;IAEjB,OAAO,UAAU;SACd,KAAK,CAAC,GAAG,CAAC;SACV,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;SAClC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AAZW,QAAA,sBAAsB,0BAYjC;AAEK,MAAM,mBAAmB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAU,EAAE;IACxE,MAAM,KAAK,GAAe,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SAC7C,IAAI,CAAC,IAAI,CAAC;SACV,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAEhD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CACpB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EACnB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EACnB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAChC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,CAAC,CAAC;AAzBW,QAAA,mBAAmB,uBAyB9B"}