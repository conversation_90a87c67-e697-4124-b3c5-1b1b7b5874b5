import { InvoiceCurrency } from '../invoices.entity';
export declare class InvoiceDto {
    total_roles?: number;
    amount?: number;
    status: string;
    currency: InvoiceCurrency;
    invoice_number: number;
    invoice_date: string;
    due_date: string;
    invoice_file?: string;
    userId: string;
    serviceId?: number;
    personId?: number;
}
declare const UpdateInvoiceDto_base: import("@nestjs/common").Type<Partial<InvoiceDto>>;
export declare class UpdateInvoiceDto extends UpdateInvoiceDto_base {
    id: number;
}
export {};
