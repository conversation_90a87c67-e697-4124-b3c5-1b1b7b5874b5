"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateInvoiceDto = exports.InvoiceDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const invoices_entity_1 = require("../invoices.entity");
class InvoiceDto {
}
exports.InvoiceDto = InvoiceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The total roles associated with the invoice',
        example: 5,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "total_roles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The total amount of the invoice',
        example: 1500,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The status of the invoice',
        example: 'PAID',
        enum: [
            'PAID',
            'UNPAID',
            'INVOICED',
            'NO_RESPONSE',
            'ISSUE',
            'CANCELLED',
            'REFUNDED',
            'EXPIRED',
            'DISPUTED',
            'PENDING',
            'FAILED',
            'COMPLETED',
            'PROCESSING',
            'CHARGEBACK',
        ],
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)([
        'PAID',
        'UNPAID',
        'INVOICED',
        'NO_RESPONSE',
        'ISSUE',
        'CANCELLED',
        'REFUNDED',
        'EXPIRED',
        'DISPUTED',
        'PENDING',
        'FAILED',
        'COMPLETED',
        'PROCESSING',
        'CHARGEBACK',
    ]),
    __metadata("design:type", String)
], InvoiceDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'currency of invoice',
        example: 'USD',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique invoice number',
        example: 12345,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "invoice_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date the invoice was issued',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "invoice_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The due date for the invoice payment',
        example: '2023-01-15T00:00:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "due_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The file associated with the invoice',
        example: 'invoice_12345.pdf',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], InvoiceDto.prototype, "invoice_file", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the user associated with the invoice',
        example: 'user_123',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], InvoiceDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'The ID of the service associated with the invoice',
        example: 'service_123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "serviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'The ID of the person associated with the invoice',
        example: 'person_123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], InvoiceDto.prototype, "personId", void 0);
class UpdateInvoiceDto extends (0, swagger_1.PartialType)(InvoiceDto) {
}
exports.UpdateInvoiceDto = UpdateInvoiceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the invoice',
        example: 5,
        required: false,
        type: 'number',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateInvoiceDto.prototype, "id", void 0);
//# sourceMappingURL=invoices.dto.js.map