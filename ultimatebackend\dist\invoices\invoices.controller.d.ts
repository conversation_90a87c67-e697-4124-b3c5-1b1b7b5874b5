import { InvoicesService } from './invoices.service';
import { InvoiceDto, UpdateInvoiceDto } from './dto/invoices.dto';
export declare class InvoicesController {
    private readonly invoicesService;
    constructor(invoicesService: InvoicesService);
    createInvoice(invoiceDto: InvoiceDto): Promise<import("./invoices.entity").Invoices>;
    getAllInvoices(page: number, pageSize: number, searchString: string, start_date: Date, end_date: Date, status: string): Promise<import("./invoices.entity").Invoices[]>;
    getInvoicesStats(): Promise<{
        status: string;
        count: number;
    }[]>;
    getInvoiceById(id: number): Promise<import("./invoices.entity").Invoices>;
    updateInvoice(id: number, invoiceDto: UpdateInvoiceDto): Promise<import("./invoices.entity").Invoices>;
}
