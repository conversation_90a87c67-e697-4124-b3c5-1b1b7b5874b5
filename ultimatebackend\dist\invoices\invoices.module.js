"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesModule = void 0;
const common_1 = require("@nestjs/common");
const invoices_service_1 = require("./invoices.service");
const invoices_controller_1 = require("./invoices.controller");
const typeorm_1 = require("@nestjs/typeorm");
const invoices_entity_1 = require("./invoices.entity");
const stripe_service_1 = require("../stripe/stripe.service");
const service_entity_1 = require("../service/service.entity");
const users_entity_1 = require("../users/users.entity");
const people_entity_1 = require("../people/people.entity");
let InvoicesModule = class InvoicesModule {
};
exports.InvoicesModule = InvoicesModule;
exports.InvoicesModule = InvoicesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([invoices_entity_1.Invoices, service_entity_1.Service, users_entity_1.Users, people_entity_1.People])],
        providers: [invoices_service_1.InvoicesService, stripe_service_1.StripeService],
        controllers: [invoices_controller_1.InvoicesController],
    })
], InvoicesModule);
//# sourceMappingURL=invoices.module.js.map