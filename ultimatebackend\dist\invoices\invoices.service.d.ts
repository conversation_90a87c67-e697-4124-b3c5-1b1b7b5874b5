import { StripeService } from './../stripe/stripe.service';
import { Invoices } from './invoices.entity';
import { InvoiceDto, UpdateInvoiceDto } from './dto/invoices.dto';
import { Repository } from 'typeorm';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';
export declare class InvoicesService {
    private readonly invoicesRepository;
    private readonly serviceRepository;
    private readonly usersRepository;
    private readonly peopleRepository;
    private stripeService;
    constructor(invoicesRepository: Repository<Invoices>, serviceRepository: Repository<Service>, usersRepository: Repository<Users>, peopleRepository: Repository<People>, stripeService: StripeService);
    createInvoice(invoiceDto: InvoiceDto): Promise<Invoices>;
    updateInvoice(id: number, invoice: UpdateInvoiceDto): Promise<Invoices>;
    deleteInvoice(id: number): Promise<void>;
    getInvoiceById(id: number): Promise<Invoices>;
    getAllInvoices(page?: number, pageSize?: number, searchString?: string, start_date?: Date, end_date?: Date, status?: string): Promise<Invoices[]>;
    getAllInvoicesStatGroupByStatus(): Promise<{
        status: string;
        count: number;
    }[]>;
}
