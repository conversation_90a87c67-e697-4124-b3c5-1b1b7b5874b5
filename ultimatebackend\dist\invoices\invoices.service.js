"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesService = void 0;
const stripe_service_1 = require("./../stripe/stripe.service");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const invoices_entity_1 = require("./invoices.entity");
const typeorm_2 = require("typeorm");
const service_entity_1 = require("../service/service.entity");
const users_entity_1 = require("../users/users.entity");
const people_entity_1 = require("../people/people.entity");
let InvoicesService = class InvoicesService {
    constructor(invoicesRepository, serviceRepository, usersRepository, peopleRepository, stripeService) {
        this.invoicesRepository = invoicesRepository;
        this.serviceRepository = serviceRepository;
        this.usersRepository = usersRepository;
        this.peopleRepository = peopleRepository;
        this.stripeService = stripeService;
    }
    async createInvoice(invoiceDto) {
        try {
            const service = await this.serviceRepository.findOne({
                where: { id: invoiceDto.serviceId },
            });
            const prospect = await this.peopleRepository.findOne({
                where: { id: invoiceDto.personId },
            });
            const successUrl = 'https://www.google.com';
            const cancelUrl = 'https://www.fb.com';
            const session = await this.stripeService.createCheckoutSession({
                amount: invoiceDto.amount,
                currency: invoiceDto.currency,
                service: service.name,
                prospect: prospect.full_name,
                invoiceNumber: invoiceDto.invoice_number.toString(),
                invoiceDate: invoiceDto.invoice_date.toString(),
                dueDate: invoiceDto.due_date.toString(),
                successUrl: successUrl,
                cancelUrl: cancelUrl,
            });
            if (!session || !session.id || !session.url) {
                throw new common_1.BadRequestException('Failed to create Stripe checkout session');
            }
            const invoice = this.invoicesRepository.create({
                ...invoiceDto,
                session_id: session.id,
                session_url: session.url,
            });
            return await this.invoicesRepository.save(invoice);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error creating invoice',
                error: error.message,
            });
        }
    }
    async updateInvoice(id, invoice) {
        try {
            const invoiceToUpdate = await this.invoicesRepository.findOne({
                where: { id },
            });
            if (!invoiceToUpdate) {
                throw new common_1.NotFoundException('Invoice not found');
            }
            Object.assign(invoiceToUpdate, invoice);
            return await this.invoicesRepository.save(invoiceToUpdate);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error updating invoice',
                error: error.message,
            });
        }
    }
    async deleteInvoice(id) {
        try {
            const invoiceToDelete = await this.invoicesRepository.findOne({
                where: { id },
            });
            if (!invoiceToDelete) {
                throw new common_1.NotFoundException('Invoice not found');
            }
            await this.invoicesRepository.remove(invoiceToDelete);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error deleting invoice',
                error: error.message,
            });
        }
    }
    async getInvoiceById(id) {
        try {
            const invoice = await this.invoicesRepository.findOne({
                where: { id },
            });
            if (!invoice) {
                throw new common_1.NotFoundException('Invoice not found');
            }
            return invoice;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching invoice',
                error: error.message,
            });
        }
    }
    async getAllInvoices(page = 0, pageSize = 10, searchString, start_date, end_date, status) {
        const skip = page * pageSize;
        const limit = pageSize;
        try {
            const query = this.invoicesRepository.createQueryBuilder('invoice');
            query.leftJoin('invoice.user', 'user');
            query.addSelect(['user.first_name', 'user.last_name']);
            query.leftJoinAndSelect('invoice.person', 'person');
            query.leftJoin('person.company', 'company');
            query.addSelect(['company.name']);
            if (searchString) {
                query.andWhere('invoice.invoice_number LIKE :searchString', {
                    searchString: `%${searchString}%`,
                });
            }
            if (start_date) {
                query.andWhere('invoice.invoice_date >= :start_date', { start_date });
            }
            if (end_date) {
                query.andWhere('invoice.invoice_date <= :end_date', { end_date });
            }
            if (status) {
                query.andWhere('invoice.status = :status', { status });
            }
            query.orderBy('invoice.invoice_date', 'DESC');
            query.skip(skip).take(limit);
            return await query.getMany();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching invoices',
                error: error.message,
            });
        }
    }
    async getAllInvoicesStatGroupByStatus() {
        try {
            const query = this.invoicesRepository
                .createQueryBuilder('invoice')
                .select('invoice.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .groupBy('invoice.status');
            return await query.getRawMany();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching invoices stats',
                error: error.message,
            });
        }
    }
};
exports.InvoicesService = InvoicesService;
exports.InvoicesService = InvoicesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoices_entity_1.Invoices)),
    __param(1, (0, typeorm_1.InjectRepository)(service_entity_1.Service)),
    __param(2, (0, typeorm_1.InjectRepository)(users_entity_1.Users)),
    __param(3, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        stripe_service_1.StripeService])
], InvoicesService);
//# sourceMappingURL=invoices.service.js.map