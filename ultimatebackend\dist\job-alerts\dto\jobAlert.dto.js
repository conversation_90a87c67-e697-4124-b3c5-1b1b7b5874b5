"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJobAlertDto = exports.JobAlert = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
let JobAlert = class JobAlert {
};
exports.JobAlert = JobAlert;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], JobAlert.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], JobAlert.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], JobAlert.prototype, "keywords", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], JobAlert.prototype, "country", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], JobAlert.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], JobAlert.prototype, "jobType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], JobAlert.prototype, "is_active", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], JobAlert.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], JobAlert.prototype, "updatedAt", void 0);
exports.JobAlert = JobAlert = __decorate([
    (0, typeorm_1.Entity)('job_alerts')
], JobAlert);
class CreateJobAlertDto {
}
exports.CreateJobAlertDto = CreateJobAlertDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '<EMAIL>',
        description: 'Email address for receiving job alerts',
    }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateJobAlertDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Software Engineer, Remote',
        description: 'Keywords for job matching',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobAlertDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'USA',
        description: 'Country of job preference',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobAlertDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'New York',
        description: 'City of job preference',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobAlertDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ['REMOTE', 'ONSITE', 'HYBRID'],
        description: 'Preferred job type',
        required: false,
        type: 'enum',
        enum: ['REMOTE', 'ONSITE', 'HYBRID'],
        enumName: 'JobTypeEnum',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(['REMOTE', 'ONSITE', 'HYBRID']),
    __metadata("design:type", String)
], CreateJobAlertDto.prototype, "job_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: true,
        description: 'Status of the job alert subscription',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateJobAlertDto.prototype, "is_active", void 0);
//# sourceMappingURL=jobAlert.dto.js.map