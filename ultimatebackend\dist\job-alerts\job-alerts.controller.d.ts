import { JobAlertsService } from './job-alerts.service';
import { CreateJobAlertDto } from './dto/jobAlert.dto';
import { JobAlerts } from './job-alerts.entity';
export declare class JobAlertsController {
    private readonly jobAlertsService;
    constructor(jobAlertsService: JobAlertsService);
    createJobAlert(data: CreateJobAlertDto): Promise<JobAlerts>;
    getAllJobAlerts(page: number, limit: number): Promise<JobAlerts[]>;
    sendJobAlert(id: number): Promise<boolean>;
    getJobAlertByCandidateEmail(email: string): Promise<JobAlerts | null>;
    getJobAlertsByKeywords(keywords: string): Promise<JobAlerts[]>;
    updateJobAlert(id: number, data: Partial<CreateJobAlertDto>): Promise<JobAlerts | null>;
    deleteJobAlert(id: number): Promise<boolean>;
}
