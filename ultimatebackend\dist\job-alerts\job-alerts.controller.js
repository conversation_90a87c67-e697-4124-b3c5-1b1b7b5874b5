"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobAlertsController = void 0;
const common_1 = require("@nestjs/common");
const job_alerts_service_1 = require("./job-alerts.service");
const swagger_1 = require("@nestjs/swagger");
const jobAlert_dto_1 = require("./dto/jobAlert.dto");
let JobAlertsController = class JobAlertsController {
    constructor(jobAlertsService) {
        this.jobAlertsService = jobAlertsService;
    }
    async createJobAlert(data) {
        return await this.jobAlertsService.createJobAlert(data);
    }
    async getAllJobAlerts(page, limit) {
        return await this.jobAlertsService.getAllJobAlerts(page, limit);
    }
    async sendJobAlert(id) {
        return await this.jobAlertsService.sendJobAlert(id);
    }
    async getJobAlertByCandidateEmail(email) {
        return await this.jobAlertsService.getJobAlertByCandidateEmail(email);
    }
    async getJobAlertsByKeywords(keywords) {
        return await this.jobAlertsService.getJobAlertsByKeywords(keywords);
    }
    async updateJobAlert(id, data) {
        return await this.jobAlertsService.updateJobAlert(id, data);
    }
    async deleteJobAlert(id) {
        return await this.jobAlertsService.deleteJobAlert(id);
    }
};
exports.JobAlertsController = JobAlertsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a job alert' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Job alert successfully created.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Failed to create job alert.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [jobAlert_dto_1.CreateJobAlertDto]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "createJobAlert", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all job alerts with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of job alerts retrieved successfully.',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "getAllJobAlerts", null);
__decorate([
    (0, common_1.Get)(':id/send'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a job alert' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job alert sent successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job alert not found.' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "sendJobAlert", null);
__decorate([
    (0, common_1.Get)('candidate'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job alert by candidate email' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Job alert retrieved successfully.',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job alert not found.' }),
    __param(0, (0, common_1.Query)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "getJobAlertByCandidateEmail", null);
__decorate([
    (0, common_1.Get)('search'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job alerts by keywords' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Job alerts retrieved successfully.',
    }),
    __param(0, (0, common_1.Query)('keywords')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "getJobAlertsByKeywords", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a job alert' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job alert updated successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job alert not found.' }),
    (0, swagger_1.ApiBody)({ type: jobAlert_dto_1.CreateJobAlertDto }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "updateJobAlert", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a job alert' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Job alert deleted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Job alert not found.' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobAlertsController.prototype, "deleteJobAlert", null);
exports.JobAlertsController = JobAlertsController = __decorate([
    (0, swagger_1.ApiTags)('Job Alerts'),
    (0, common_1.Controller)('job-alerts'),
    __metadata("design:paramtypes", [job_alerts_service_1.JobAlertsService])
], JobAlertsController);
//# sourceMappingURL=job-alerts.controller.js.map