"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobAlertsModule = void 0;
const common_1 = require("@nestjs/common");
const job_alerts_service_1 = require("./job-alerts.service");
const job_alerts_controller_1 = require("./job-alerts.controller");
const typeorm_1 = require("@nestjs/typeorm");
const job_alerts_entity_1 = require("./job-alerts.entity");
let JobAlertsModule = class JobAlertsModule {
};
exports.JobAlertsModule = JobAlertsModule;
exports.JobAlertsModule = JobAlertsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([job_alerts_entity_1.JobAlerts])],
        providers: [job_alerts_service_1.JobAlertsService],
        controllers: [job_alerts_controller_1.JobAlertsController],
    })
], JobAlertsModule);
//# sourceMappingURL=job-alerts.module.js.map