import { Repository } from 'typeorm';
import { JobAlerts } from './job-alerts.entity';
import { CreateJobAlertDto } from './dto/jobAlert.dto';
export declare class JobAlertsService {
    private readonly jobAlertRepository;
    constructor(jobAlertRepository: Repository<JobAlerts>);
    createJobAlert(data: CreateJobAlertDto): Promise<JobAlerts>;
    sendJobAlert(id: number): Promise<boolean>;
    getAllJobAlerts(page: number, limit: number): Promise<JobAlerts[]>;
    getJobAlertByCandidateEmail(email: string): Promise<JobAlerts | null>;
    getJobAlertsByKeywords(keywords: string): Promise<JobAlerts[]>;
    updateJobAlert(id: number, data: Partial<CreateJobAlertDto>): Promise<JobAlerts | null>;
    deleteJobAlert(id: number): Promise<boolean>;
}
