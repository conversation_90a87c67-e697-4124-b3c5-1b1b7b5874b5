"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobAlertsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const job_alerts_entity_1 = require("./job-alerts.entity");
let JobAlertsService = class JobAlertsService {
    constructor(jobAlertRepository) {
        this.jobAlertRepository = jobAlertRepository;
    }
    async createJobAlert(data) {
        try {
            const jobAlert = this.jobAlertRepository.create(data);
            return await this.jobAlertRepository.save(jobAlert);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to create job alert.');
        }
    }
    async sendJobAlert(id) {
        try {
            const jobAlert = await this.jobAlertRepository.findOne({ where: { id } });
            if (!jobAlert)
                throw new common_1.NotFoundException('Job alert not found.');
            return true;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to send job alert.');
        }
    }
    async getAllJobAlerts(page, limit) {
        try {
            return await this.jobAlertRepository.find({
                skip: (page - 1) * limit,
                take: limit,
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to retrieve job alerts.');
        }
    }
    async getJobAlertByCandidateEmail(email) {
        try {
            return await this.jobAlertRepository.findOne({ where: { email } });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch job alert.');
        }
    }
    async getJobAlertsByKeywords(keywords) {
        try {
            return await this.jobAlertRepository.find({
                where: { keywords: (0, typeorm_2.Like)(`%${keywords}%`) },
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to retrieve job alerts by keywords.');
        }
    }
    async updateJobAlert(id, data) {
        try {
            const result = await this.jobAlertRepository.update(id, data);
            if (result.affected === 0)
                throw new common_1.NotFoundException('Job alert not found.');
            return await this.jobAlertRepository.findOne({ where: { id } });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to update job alert.',
                error: error.message,
            });
        }
    }
    async deleteJobAlert(id) {
        try {
            const result = await this.jobAlertRepository.delete(id);
            if (result.affected === 0)
                throw new common_1.NotFoundException('Job alert not found.');
            return true;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to delete job alert.');
        }
    }
};
exports.JobAlertsService = JobAlertsService;
exports.JobAlertsService = JobAlertsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(job_alerts_entity_1.JobAlerts)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], JobAlertsService);
//# sourceMappingURL=job-alerts.service.js.map