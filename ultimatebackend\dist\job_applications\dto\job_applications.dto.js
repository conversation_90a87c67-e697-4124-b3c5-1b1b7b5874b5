"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateJobApplicationDto = exports.JobApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class JobApplicationDto {
}
exports.JobApplicationDto = JobApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the job application',
        enum: ['PENDING', 'ACCEPTED', 'REJECTED'],
        default: 'PENDING',
    }),
    (0, class_validator_1.IsEnum)(['PENDING', 'ACCEPTED', 'REJECTED']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], JobApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Action type of the job application',
        enum: ['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'],
        default: 'APPLIED',
    }),
    (0, class_validator_1.IsEnum)(['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], JobApplicationDto.prototype, "action_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the job associated with the application',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], JobApplicationDto.prototype, "jobId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the user who applied for the job',
        example: 1,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], JobApplicationDto.prototype, "userId", void 0);
class UpdateJobApplicationDto {
}
exports.UpdateJobApplicationDto = UpdateJobApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the job application',
        enum: ['PENDING', 'ACCEPTED', 'REJECTED'],
        required: false,
    }),
    (0, class_validator_1.IsEnum)(['PENDING', 'ACCEPTED', 'REJECTED']),
    __metadata("design:type", String)
], UpdateJobApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Action type of the job application',
        enum: ['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'],
        required: false,
    }),
    (0, class_validator_1.IsEnum)(['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES']),
    __metadata("design:type", String)
], UpdateJobApplicationDto.prototype, "action_type", void 0);
//# sourceMappingURL=job_applications.dto.js.map