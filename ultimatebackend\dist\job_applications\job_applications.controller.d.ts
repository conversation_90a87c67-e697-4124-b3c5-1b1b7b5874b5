import { JobApplicationsService } from './job_applications.service';
import { JobApplicationDto } from './dto/job_applications.dto';
import { JobApplications } from './job_applications.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';
export declare class JobApplicationsController {
    private readonly jobApplicationsService;
    constructor(jobApplicationsService: JobApplicationsService);
    createJobApplication(jobApplicationDto: JobApplicationDto): Promise<JobApplications>;
    getJobApplicationsByUserId(userId: string): Promise<JobApplications[]>;
    getJobApplicationsByJobId(jobId: number): Promise<JobApplications[]>;
    updateJobApplicationStatus(id: number, status: string): Promise<JobApplications>;
    deleteJobApplication(id: number): Promise<void>;
    getJobApplicantsByJobId(jobId: number): Promise<JobApplications[]>;
    getAllApplicationStatusByUserId(userId: string): Promise<JobApplications[]>;
    getAllJobApplicants(): Promise<{
        job: Jobs;
        applicants: Users[];
    }[]>;
}
