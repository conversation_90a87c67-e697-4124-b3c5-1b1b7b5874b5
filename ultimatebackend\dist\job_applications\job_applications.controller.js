"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const job_applications_service_1 = require("./job_applications.service");
const job_applications_dto_1 = require("./dto/job_applications.dto");
const job_applications_entity_1 = require("./job_applications.entity");
let JobApplicationsController = class JobApplicationsController {
    constructor(jobApplicationsService) {
        this.jobApplicationsService = jobApplicationsService;
    }
    async createJobApplication(jobApplicationDto) {
        return this.jobApplicationsService.createJobApplication(jobApplicationDto);
    }
    async getJobApplicationsByUserId(userId) {
        return this.jobApplicationsService.getJobApplicationsByUserId(userId);
    }
    async getJobApplicationsByJobId(jobId) {
        return this.jobApplicationsService.getJobApplicationsByJobId(jobId);
    }
    async updateJobApplicationStatus(id, status) {
        return this.jobApplicationsService.updateJobApplicationStatus(id, status);
    }
    async deleteJobApplication(id) {
        return this.jobApplicationsService.deleteJobApplication(id);
    }
    async getJobApplicantsByJobId(jobId) {
        return this.jobApplicationsService.getJobApplicantsByJobId(jobId);
    }
    async getAllApplicationStatusByUserId(userId) {
        return this.jobApplicationsService.getAllApplicantJobStatuses(userId);
    }
    async getAllJobApplicants() {
        return this.jobApplicationsService.getAllJobApplicants();
    }
};
exports.JobApplicationsController = JobApplicationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new job application' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The job application has been successfully created.',
        type: job_applications_entity_1.JobApplications,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [job_applications_dto_1.JobApplicationDto]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "createJobApplication", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job applications by user ID' }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String, description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of job applications for the specified user.',
        type: [job_applications_entity_1.JobApplications],
    }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "getJobApplicationsByUserId", null);
__decorate([
    (0, common_1.Get)('job/:jobId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job applications by job ID' }),
    (0, swagger_1.ApiParam)({ name: 'jobId', type: Number, description: 'Job ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of job applications for the specified job.',
        type: [job_applications_entity_1.JobApplications],
    }),
    __param(0, (0, common_1.Param)('jobId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "getJobApplicationsByJobId", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update the status of a job application' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number, description: 'Job Application ID' }),
    (0, swagger_1.ApiBody)({
        description: 'Status to update',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The job application status has been successfully updated.',
        type: job_applications_entity_1.JobApplications,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "updateJobApplicationStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a job application' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number, description: 'Job Application ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The job application has been successfully deleted.',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "deleteJobApplication", null);
__decorate([
    (0, common_1.Get)('job/:jobId/applicants'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job applicants by job ID' }),
    (0, swagger_1.ApiParam)({ name: 'jobId', type: Number, description: 'Job ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of job applicants for the specified job.',
        type: [job_applications_entity_1.JobApplications],
    }),
    __param(0, (0, common_1.Param)('jobId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "getJobApplicantsByJobId", null);
__decorate([
    (0, common_1.Get)('getAllApplicationStatusByUserId/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get job application status by user ID' }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: Number, description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of job application status for the specified user.',
        type: [job_applications_entity_1.JobApplications],
    }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "getAllApplicationStatusByUserId", null);
__decorate([
    (0, common_1.Get)('getAllJobApplicants'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all job applicants' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of all job applicants.',
        type: [job_applications_entity_1.JobApplications],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], JobApplicationsController.prototype, "getAllJobApplicants", null);
exports.JobApplicationsController = JobApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('Job Applications'),
    (0, common_1.Controller)('job-applications'),
    __metadata("design:paramtypes", [job_applications_service_1.JobApplicationsService])
], JobApplicationsController);
//# sourceMappingURL=job_applications.controller.js.map