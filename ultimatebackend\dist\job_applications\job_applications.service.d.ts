import { JobApplications } from './job_applications.entity';
import { Repository } from 'typeorm';
import { JobApplicationDto } from './dto/job_applications.dto';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';
export declare class JobApplicationsService {
    private jobApplicationsRepository;
    constructor(jobApplicationsRepository: Repository<JobApplications>);
    createJobApplication(jobApplication: JobApplicationDto): Promise<JobApplications>;
    getJobApplicationsByUserId(userId: string): Promise<JobApplications[]>;
    getJobApplicationsByJobId(jobId: number): Promise<JobApplications[]>;
    updateJobApplicationStatus(id: number, status: string): Promise<JobApplications>;
    deleteJobApplication(id: number): Promise<void>;
    getJobApplicantsByJobId(jobId: number): Promise<JobApplications[]>;
    getAllApplicantJobStatuses(userId: string): Promise<JobApplications[]>;
    getAllJobApplicants(): Promise<{
        job: Jobs;
        applicants: Users[];
    }[]>;
}
