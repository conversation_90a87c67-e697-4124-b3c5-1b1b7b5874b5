"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const job_applications_entity_1 = require("./job_applications.entity");
const typeorm_2 = require("typeorm");
let JobApplicationsService = class JobApplicationsService {
    constructor(jobApplicationsRepository) {
        this.jobApplicationsRepository = jobApplicationsRepository;
    }
    async createJobApplication(jobApplication) {
        try {
            const newJobApplication = this.jobApplicationsRepository.create(jobApplication);
            const savedJobApplication = await this.jobApplicationsRepository.insert(newJobApplication);
            return savedJobApplication.raw[0];
        }
        catch (error) {
            throw new Error('Failed to create job application: ' + error.message);
        }
    }
    async getJobApplicationsByUserId(userId) {
        try {
            const jobApplications = await this.jobApplicationsRepository.find({
                where: { userId },
                relations: ['job'],
            });
            return jobApplications;
        }
        catch (error) {
            throw new Error('Failed to get job applications: ' + error.message);
        }
    }
    async getJobApplicationsByJobId(jobId) {
        try {
            const jobApplications = await this.jobApplicationsRepository.find({
                where: { jobId },
            });
            return jobApplications;
        }
        catch (error) {
            throw new Error('Failed to get job applications: ' + error.message);
        }
    }
    async updateJobApplicationStatus(id, status) {
        try {
            const jobApplication = await this.jobApplicationsRepository.findOne({
                where: { id },
            });
            if (!jobApplication) {
                throw new Error('Job application not found');
            }
            jobApplication.status = status;
            const updatedJobApplication = await this.jobApplicationsRepository.save(jobApplication);
            return updatedJobApplication;
        }
        catch (error) {
            throw new Error('Failed to update job application status: ' + error.message);
        }
    }
    async deleteJobApplication(id) {
        try {
            await this.jobApplicationsRepository.delete(id);
        }
        catch (error) {
            throw new Error('Failed to delete job application: ' + error.message);
        }
    }
    async getJobApplicantsByJobId(jobId) {
        try {
            const jobApplicants = await this.jobApplicationsRepository.find({
                where: { jobId },
            });
            return jobApplicants;
        }
        catch (error) {
            throw new Error('Failed to get job applicants: ' + error.message);
        }
    }
    async getAllApplicantJobStatuses(userId) {
        try {
            const jobApplicants = await this.jobApplicationsRepository.find({
                where: { userId },
                relations: ['user', 'user.people'],
            });
            return jobApplicants;
        }
        catch (error) {
            throw new Error('Failed to get job applicants: ' + error.message);
        }
    }
    async getAllJobApplicants() {
        try {
            const jobApplications = await this.jobApplicationsRepository.find({
                relations: ['job', 'user.people'],
            });
            const groupedByJob = jobApplications.reduce((acc, application) => {
                const jobId = application.job.id;
                if (!acc[jobId]) {
                    acc[jobId] = { job: application.job, applicants: [] };
                }
                acc[jobId].applicants.push(application.user);
                return acc;
            }, {});
            return Object.values(groupedByJob);
        }
        catch (error) {
            throw new Error('Failed to get job applicants: ' + error.message);
        }
    }
};
exports.JobApplicationsService = JobApplicationsService;
exports.JobApplicationsService = JobApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(job_applications_entity_1.JobApplications)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], JobApplicationsService);
//# sourceMappingURL=job_applications.service.js.map