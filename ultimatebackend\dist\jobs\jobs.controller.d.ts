import { JobsService } from './jobs.service';
import { Create<PERSON>obDto, GetAllJobsDto, searchJobPostsOfCompanyDto } from './dto/createJob.dto';
import { UpdateJobDto } from './dto/updateJob.dto';
export declare class JobsController {
    private readonly jobsService;
    constructor(jobsService: JobsService);
    createJob(body: CreateJobDto): Promise<import("./jobs.entity").Jobs>;
    updateJob(id: number, body: UpdateJobDto): Promise<import("./jobs.entity").Jobs>;
    findAll(page?: number, pageSize?: number, searchString?: string, sortBy?: string, sortingOrder?: string, userId?: string): Promise<import("./jobs.entity").Jobs[]>;
    findAllGeenral(page?: number, pageSize?: number, searchString?: string, sortBy?: string, sortingOrder?: string): Promise<import("./jobs.entity").Jobs[]>;
    findOne(id: number): Promise<import("./jobs.entity").Jobs>;
    findJobByTitle(title: string): Promise<import("./jobs.entity").Jobs>;
    findJobByPersonId(personId: number): Promise<import("./jobs.entity").Jobs>;
    findJobByLocation(job_location: string): Promise<import("./jobs.entity").Jobs>;
    findJobByType(job_type: string): Promise<import("./jobs.entity").Jobs>;
    findJobByCompanyId(companyId: number): Promise<import("./jobs.entity").Jobs>;
    deleteJob(id: number): Promise<void>;
    findJobByJobId(userId: string): Promise<import("./jobs.entity").Jobs[]>;
    getJobPostsByCompanyId(companyId: string): Promise<import("./jobs.entity").Jobs[]>;
    getAllJobs(queryParams: GetAllJobsDto): Promise<{
        jobPosts: import("./jobs.entity").Jobs[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
    }>;
    searchJobPostsOfCompany(queryParams: searchJobPostsOfCompanyDto): Promise<import("./jobs.entity").Jobs[]>;
    searchJobPosts(searchTerm: string): Promise<{
        company: {
            id: number;
            name: string;
            profile_url: string;
        };
        person: {
            id: number;
            full_name: string;
            profile_url: string;
        };
        id: number;
        title: string;
        description: string;
        status: string;
        job_posting_link: string;
        job_posting_date: Date;
        job_closing_date: Date;
        job_source: string;
        experience_level: string;
        job_type: string;
        job_location_type: string;
        SR_specified_industry: string;
        applicants: string;
        job_location_city: string;
        job_location_state: string;
        job_location_country: string;
        job_location_zip: string;
        currency: string;
        salary_min: number;
        salary_max: number;
        salary_period: string;
        industry: string;
        skill_required: string;
        skills: string[];
        scrapper_profile_name: string;
        companyId: number;
        personId: number;
        user: import("../users/users.entity").Users;
        userId: string;
        sector: import("../sector/sector.entity").Sector;
        sectorId: number;
        country: import("../country/country.entity").Country;
        countryId: number;
        job_applications: import("../job_applications/job_applications.entity").JobApplications[];
        created_at: Date;
        updated_at: Date;
    }[]>;
}
