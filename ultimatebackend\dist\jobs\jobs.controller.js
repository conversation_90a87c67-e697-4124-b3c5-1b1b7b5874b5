"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jobs_service_1 = require("./jobs.service");
const createJob_dto_1 = require("./dto/createJob.dto");
const updateJob_dto_1 = require("./dto/updateJob.dto");
let JobsController = class JobsController {
    constructor(jobsService) {
        this.jobsService = jobsService;
    }
    async createJob(body) {
        return this.jobsService.createJob(body);
    }
    async updateJob(id, body) {
        return this.jobsService.updateJob(id, body);
    }
    async findAll(page, pageSize, searchString, sortBy, sortingOrder, userId) {
        return this.jobsService.findAll(page, pageSize, searchString, sortingOrder, sortBy, userId);
    }
    async findAllGeenral(page, pageSize, searchString, sortBy, sortingOrder) {
        return this.jobsService.findAllGeneral(page, pageSize, searchString, sortingOrder, sortBy);
    }
    async findOne(id) {
        return this.jobsService.findOne(id);
    }
    async findJobByTitle(title) {
        return this.jobsService.findJobByTitle(title);
    }
    async findJobByPersonId(personId) {
        return this.jobsService.findJobByPersonId(personId);
    }
    async findJobByLocation(job_location) {
        return this.jobsService.findJobByLocation(job_location);
    }
    async findJobByType(job_type) {
        return this.jobsService.findJobByType(job_type);
    }
    async findJobByCompanyId(companyId) {
        return this.jobsService.findJobByCompanyId(companyId);
    }
    async deleteJob(id) {
        return this.jobsService.deleteJob(id);
    }
    async findJobByJobId(userId) {
        return this.jobsService.findAllJobApplicantsByUserId(userId);
    }
    async getJobPostsByCompanyId(companyId) {
        return this.jobsService.getJobPostsByCompanyId(companyId);
    }
    async getAllJobs(queryParams) {
        return this.jobsService.getAllJobs(queryParams);
    }
    async searchJobPostsOfCompany(queryParams) {
        return this.jobsService.searchJobPostsOfCompany(queryParams);
    }
    async searchJobPosts(searchTerm) {
        return this.jobsService.searchJobPosts(searchTerm);
    }
};
exports.JobsController = JobsController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new job' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createJob_dto_1.CreateJobDto]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "createJob", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a job' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateJob_dto_1.UpdateJobDto]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "updateJob", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all jobs' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchString',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortingOrder',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        required: true,
        type: String,
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('searchString')),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortingOrder')),
    __param(5, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('all-candidate-jobs'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all jobs' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchString',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortingOrder',
        required: false,
        type: String,
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('searchString')),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortingOrder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findAllGeenral", null);
__decorate([
    (0, common_1.Get)('find/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('find/title'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by title' }),
    __param(0, (0, common_1.Query)('title')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByTitle", null);
__decorate([
    (0, common_1.Get)('find/personId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by personId' }),
    __param(0, (0, common_1.Query)('personId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByPersonId", null);
__decorate([
    (0, common_1.Get)('find/location'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by location' }),
    __param(0, (0, common_1.Query)('job_location')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByLocation", null);
__decorate([
    (0, common_1.Get)('find/type'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by type' }),
    __param(0, (0, common_1.Query)('job_type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByType", null);
__decorate([
    (0, common_1.Get)('find/companyId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by companyId' }),
    __param(0, (0, common_1.Query)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByCompanyId", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a job' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "deleteJob", null);
__decorate([
    (0, common_1.Get)('findJobWithApllicants/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by jobId' }),
    __param(0, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "findJobByJobId", null);
__decorate([
    (0, common_1.Get)('getJobPostsByCompanyId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a job by jobId' }),
    __param(0, (0, common_1.Query)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "getJobPostsByCompanyId", null);
__decorate([
    (0, common_1.Get)('getAllJobs'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all jobs' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createJob_dto_1.GetAllJobsDto]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "getAllJobs", null);
__decorate([
    (0, common_1.Get)('searchJobPostsOfCompany'),
    (0, swagger_1.ApiOperation)({ summary: 'search job posts of company' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createJob_dto_1.searchJobPostsOfCompanyDto]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "searchJobPostsOfCompany", null);
__decorate([
    (0, common_1.Get)('searchJobPosts'),
    (0, swagger_1.ApiOperation)({ summary: 'search job posts' }),
    __param(0, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], JobsController.prototype, "searchJobPosts", null);
exports.JobsController = JobsController = __decorate([
    (0, common_1.Controller)('jobs'),
    (0, swagger_1.ApiTags)('jobs'),
    __metadata("design:paramtypes", [jobs_service_1.JobsService])
], JobsController);
//# sourceMappingURL=jobs.controller.js.map