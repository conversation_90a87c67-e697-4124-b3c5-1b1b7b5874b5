import { Jobs } from './jobs.entity';
import { Repository } from 'typeorm';
import { CreateJobDto, GetAllJobsDto, searchJobPostsOfCompanyDto } from './dto/createJob.dto';
import { UpdateJobDto } from './dto/updateJob.dto';
export declare class JobsService {
    private jobRepository;
    constructor(jobRepository: Repository<Jobs>);
    createJob(createJobDto: CreateJobDto): Promise<Jobs>;
    updateJob(id: number, updateJobDto: UpdateJobDto): Promise<Jobs>;
    deleteJob(id: number): Promise<void>;
    findAll(page?: number, pageSize?: number, searchString?: string, sortingOrder?: string, sortBy?: string, userId?: string): Promise<Jobs[]>;
    findAllGeneral(page?: number, pageSize?: number, searchString?: string, sortingOrder?: string, sortBy?: string): Promise<Jobs[]>;
    findOne(id: number): Promise<Jobs>;
    findJobByTitle(title: string): Promise<Jobs>;
    findJobByPersonId(personId: number): Promise<Jobs>;
    findJobByLocation(job_location_type: string): Promise<Jobs>;
    findJobByType(job_type: string): Promise<Jobs>;
    findJobByCompanyId(companyId: number): Promise<Jobs>;
    findAllJobApplicantsByUserId(userId: string): Promise<Jobs[]>;
    getJobPostsByCompanyId(companyId: string): Promise<Jobs[]>;
    getAllJobs(queryParam: GetAllJobsDto): Promise<{
        jobPosts: Jobs[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
    }>;
    searchJobPostsOfCompany(queryParams: searchJobPostsOfCompanyDto): Promise<Jobs[]>;
    searchJobPosts(searchTerm?: string): Promise<{
        company: {
            id: number;
            name: string;
            profile_url: string;
        };
        person: {
            id: number;
            full_name: string;
            profile_url: string;
        };
        id: number;
        title: string;
        description: string;
        status: string;
        job_posting_link: string;
        job_posting_date: Date;
        job_closing_date: Date;
        job_source: string;
        experience_level: string;
        job_type: string;
        job_location_type: string;
        SR_specified_industry: string;
        applicants: string;
        job_location_city: string;
        job_location_state: string;
        job_location_country: string;
        job_location_zip: string;
        currency: string;
        salary_min: number;
        salary_max: number;
        salary_period: string;
        industry: string;
        skill_required: string;
        skills: string[];
        scrapper_profile_name: string;
        companyId: number;
        personId: number;
        user: import("../users/users.entity").Users;
        userId: string;
        sector: import("../sector/sector.entity").Sector;
        sectorId: number;
        country: import("../country/country.entity").Country;
        countryId: number;
        job_applications: import("../job_applications/job_applications.entity").JobApplications[];
        created_at: Date;
        updated_at: Date;
    }[]>;
}
