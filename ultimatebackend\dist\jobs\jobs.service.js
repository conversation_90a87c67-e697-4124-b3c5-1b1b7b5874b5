"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jobs_entity_1 = require("./jobs.entity");
const typeorm_2 = require("typeorm");
let JobsService = class JobsService {
    constructor(jobRepository) {
        this.jobRepository = jobRepository;
    }
    async createJob(createJobDto) {
        try {
            const job = this.jobRepository.create(createJobDto);
            await this.jobRepository.save(job);
            return job;
        }
        catch (error) {
            console.error('Error creating job:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to create job: ${error.message}`);
        }
    }
    async updateJob(id, updateJobDto) {
        try {
            const job = await this.jobRepository.findOne({ where: { id } });
            if (!job) {
                throw new common_1.NotFoundException(`Job with ID ${id} not found`);
            }
            await this.jobRepository.update({ id }, updateJobDto);
            return { ...job, ...updateJobDto };
        }
        catch (error) {
            console.error('Error updating job:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to update job: ${error.message}`);
        }
    }
    async deleteJob(id) {
        try {
            const result = await this.jobRepository.delete({ id });
            if (result.affected === 0) {
                throw new common_1.NotFoundException(`Job with ID ${id} not found`);
            }
        }
        catch (error) {
            console.error('Error deleting job:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to delete job: ${error.message}`);
        }
    }
    async findAll(page = 0, pageSize = 10, searchString = '', sortingOrder = 'ASC', sortBy = 'id', userId = '1') {
        try {
            const jobs = await this.jobRepository.find({
                where: { userId: userId },
                take: pageSize,
                skip: page * pageSize,
                order: {
                    [sortBy]: sortingOrder,
                },
                relations: ['company'],
            });
            if (!jobs.length) {
                throw new common_1.NotFoundException('No jobs found');
            }
            return jobs;
        }
        catch (error) {
            console.error('Error finding jobs:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve jobs: ${error.message}`);
        }
    }
    async findAllGeneral(page = 0, pageSize = 10, searchString = '', sortingOrder = 'ASC', sortBy = 'id') {
        try {
            if (searchString === '') {
                searchString = null;
            }
            const whereCondition = searchString
                ? { title: (0, typeorm_2.ILike)(`%${searchString}%`) }
                : {};
            const jobs = await this.jobRepository.find({
                where: whereCondition,
                take: pageSize,
                skip: page * pageSize,
                order: {
                    [sortBy]: sortingOrder,
                },
                relations: ['company'],
            });
            if (!jobs.length) {
                throw new common_1.NotFoundException('No jobs found');
            }
            return jobs;
        }
        catch (error) {
            console.error('Error finding jobs:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve jobs: ${error.message}`);
        }
    }
    async findOne(id) {
        try {
            const job = await this.jobRepository.findOne({ where: { id } });
            if (!job) {
                throw new common_1.NotFoundException(`Job with ID ${id} not found`);
            }
            return job;
        }
        catch (error) {
            console.error(`Error retrieving job ID ${id}:`, error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findJobByTitle(title) {
        try {
            const job = await this.jobRepository.findOne({ where: { title } });
            if (!job) {
                throw new common_1.NotFoundException(`No job found with title "${title}"`);
            }
            return job;
        }
        catch (error) {
            console.error('Error finding job by title:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findJobByPersonId(personId) {
        try {
            const job = await this.jobRepository.findOne({ where: { personId } });
            if (!job) {
                throw new common_1.NotFoundException(`No job found for person ID ${personId}`);
            }
            return job;
        }
        catch (error) {
            console.error('Error finding job by person ID:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findJobByLocation(job_location_type) {
        try {
            const job = await this.jobRepository.findOne({
                where: { job_location_type },
            });
            if (!job) {
                throw new common_1.NotFoundException(`No job found at location "${job_location_type}"`);
            }
            return job;
        }
        catch (error) {
            console.error('Error finding job by location:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findJobByType(job_type) {
        try {
            const job = await this.jobRepository.findOne({ where: { job_type } });
            if (!job) {
                throw new common_1.NotFoundException(`No job found of type "${job_type}"`);
            }
            return job;
        }
        catch (error) {
            console.error('Error finding job by type:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findJobByCompanyId(companyId) {
        try {
            const job = await this.jobRepository.findOne({ where: { companyId } });
            if (!job) {
                throw new common_1.NotFoundException(`No job found for company ID ${companyId}`);
            }
            return job;
        }
        catch (error) {
            console.error('Error finding job by company ID:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job: ${error.message}`);
        }
    }
    async findAllJobApplicantsByUserId(userId) {
        try {
            const jobs = await this.jobRepository.find({
                where: { userId: userId },
                relations: ['job_applications', 'job_applications.user.people'],
            });
            if (!jobs.length) {
                throw new common_1.NotFoundException('No jobs found for this user');
            }
            return jobs;
        }
        catch (error) {
            console.error('Error finding job applicants by user ID:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job applicants: ${error.message}`);
        }
    }
    async getJobPostsByCompanyId(companyId) {
        if (!companyId) {
            throw new common_1.InternalServerErrorException('Company ID is required');
        }
        const companyIdInt = parseInt(companyId);
        try {
            const jobs = await this.jobRepository.find({
                where: { companyId: companyIdInt },
            });
            if (!jobs.length) {
                throw new common_1.NotFoundException('No jobs found for this user');
            }
            return jobs;
        }
        catch (error) {
            console.error('Error finding job applicants by user ID:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve job applicants: ${error.message}`);
        }
    }
    async getAllJobs(queryParam) {
        const { page, pageSize, search, country_id, sector_id, remote, jobType, experienceLevel, selectedCountry, selectedSector, startDate, endDate, } = queryParam;
        const pageInt = parseInt(page) || 0;
        const pageSizeInt = parseInt(pageSize) || 10;
        const limit = pageSizeInt;
        const skip = pageInt * pageSizeInt;
        let whereCondition = {};
        if (search && search.trim()) {
            const searchConditions = [
                { name: (0, typeorm_2.ILike)(`%${search}%`) },
                { website: (0, typeorm_2.ILike)(`%${search}%`) },
                { profile_url: (0, typeorm_2.ILike)(`%${search}%`) },
                { industry: (0, typeorm_2.ILike)(`%${search}%`) },
                { address: (0, typeorm_2.ILike)(`%${search}%`) },
                { description: (0, typeorm_2.ILike)(`%${search}%`) },
                { headquarter_country: (0, typeorm_2.ILike)(`%${search}%`) },
            ];
            if (!isNaN(Number(search))) {
                searchConditions.push({ staff_count: Number(search) });
                searchConditions.push({ founded: Number(search) });
            }
            whereCondition = searchConditions.map((condition) => ({
                ...whereCondition,
                ...condition,
            }));
        }
        if (country_id || selectedCountry) {
            whereCondition.countryId =
                parseInt(country_id) || parseInt(selectedCountry);
        }
        if (sector_id || selectedSector) {
            whereCondition.sectorId = parseInt(sector_id) || parseInt(selectedSector);
        }
        if (startDate && endDate) {
            whereCondition.created_at =
                startDate === endDate
                    ? (0, typeorm_2.MoreThanOrEqual)(`${startDate}T00:00:00.000Z`)
                    : (0, typeorm_2.Between)(`${startDate}T00:00:00.000Z`, `${endDate}T23:59:59.999Z`);
        }
        else if (startDate) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(`${startDate}T00:00:00.000Z`);
        }
        else if (endDate) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(`${endDate}T23:59:59.999Z`);
        }
        if (remote) {
            whereCondition.job_location_type = remote;
        }
        if (jobType) {
            whereCondition.job_type = jobType;
        }
        if (experienceLevel) {
            whereCondition.experience_level = experienceLevel;
        }
        const [jobs, total] = await this.jobRepository.findAndCount({
            where: whereCondition,
            order: { created_at: 'DESC' },
            take: limit,
            skip: skip,
            relations: ['company'],
        });
        let sr_count = 0, direct_count = 0, unknown_count = 0;
        if (Array.isArray(whereCondition)) {
            sr_count = await this.jobRepository.count({
                where: whereCondition.map((cond) => ({ ...cond, sectorId: 2 })),
            });
            direct_count = await this.jobRepository.count({
                where: whereCondition.map((cond) => ({ ...cond, sectorId: 1 })),
            });
            unknown_count = await this.jobRepository.count({
                where: whereCondition.map((cond) => ({ ...cond, sectorId: (0, typeorm_2.IsNull)() })),
            });
        }
        else {
            sr_count = await this.jobRepository.count({
                where: { ...whereCondition, sectorId: 2 },
            });
            direct_count = await this.jobRepository.count({
                where: { ...whereCondition, sectorId: 1 },
            });
            unknown_count = await this.jobRepository.count({
                where: { ...whereCondition, sectorId: (0, typeorm_2.IsNull)() },
            });
        }
        const responseData = {
            jobPosts: jobs,
            totalCount: total,
            sr_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? sr_count
                : sector_id === '2'
                    ? sr_count
                    : 0,
            direct_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? direct_count
                : sector_id === '1'
                    ? direct_count
                    : 0,
            unknown_count: !sector_id ||
                sector_id === '' ||
                sector_id === undefined ||
                sector_id === null
                ? unknown_count
                : sector_id === 'null'
                    ? unknown_count
                    : 0,
        };
        return responseData;
    }
    async searchJobPostsOfCompany(queryParams) {
        try {
            const { searchTerm, company_id } = queryParams;
            const ilikeCondition = searchTerm
                ? [
                    { title: (0, typeorm_2.ILike)(`%${searchTerm}%`), companyId: company_id },
                    { industry: (0, typeorm_2.ILike)(`%${searchTerm}%`), companyId: company_id },
                    {
                        job_location_city: (0, typeorm_2.ILike)(`%${searchTerm}%`),
                        companyId: company_id,
                    },
                    {
                        job_location_state: (0, typeorm_2.ILike)(`%${searchTerm}%`),
                        companyId: company_id,
                    },
                    { description: (0, typeorm_2.ILike)(`%${searchTerm}%`), companyId: company_id },
                    {
                        job_posting_link: (0, typeorm_2.ILike)(`%${searchTerm}%`),
                        companyId: company_id,
                    },
                ]
                : [{ companyId: company_id }];
            const data = await this.jobRepository.find({
                where: ilikeCondition,
            });
            return data;
        }
        catch (error) {
            console.log('Error finding all companies:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to retrieve all companies: ${error.message}`);
        }
    }
    async searchJobPosts(searchTerm) {
        try {
            const query = this.jobRepository
                .createQueryBuilder('job')
                .leftJoinAndSelect('job.company', 'company')
                .leftJoinAndSelect('job.person', 'person');
            if (searchTerm) {
                query.andWhere(`
            job.title ILIKE :term
            OR job.industry ILIKE :term
            OR job.job_location_city ILIKE :term
            OR job.job_location_state ILIKE :term
            OR job.job_location_type::text ILIKE :term
            OR job.description ILIKE :term
            OR job.job_posting_link ILIKE :term
          `, { term: `%${searchTerm}%` });
            }
            const data = await query.getMany();
            return data.map((job) => ({
                ...job,
                company: job.company
                    ? {
                        id: job.company.id,
                        name: job.company.name,
                        profile_url: job.company.profile_url,
                    }
                    : null,
                person: job.person
                    ? {
                        id: job.person.id,
                        full_name: job.person.full_name,
                        profile_url: job.person.profile_url,
                    }
                    : null,
            }));
        }
        catch (err) {
            throw new common_1.InternalServerErrorException(err.message || 'Some error occurred while retrieving job posts.');
        }
    }
};
exports.JobsService = JobsService;
exports.JobsService = JobsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(jobs_entity_1.Jobs)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], JobsService);
//# sourceMappingURL=jobs.service.js.map