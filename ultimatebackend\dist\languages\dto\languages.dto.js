"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateLanguagesDto = exports.LanguageDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class LanguageDto {
}
exports.LanguageDto = LanguageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the language',
        example: 'English',
        required: true,
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LanguageDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The level of the language',
        example: 'Fluent',
        required: true,
        type: 'enum',
        enum: ['BASIC', 'INTERMEDIATE', 'ADVANCED', 'FLUENT', 'NATIVE'],
    }),
    (0, class_validator_1.IsEnum)(['BASIC', 'INTERMEDIATE', 'ADVANCED', 'FLUENT', 'NATIVE']),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LanguageDto.prototype, "proficiency_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The id of the person',
        example: 1,
        required: true,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], LanguageDto.prototype, "personId", void 0);
class UpdateLanguagesDto extends (0, swagger_1.PartialType)(LanguageDto) {
}
exports.UpdateLanguagesDto = UpdateLanguagesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The id of the language',
        example: 1,
        required: true,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateLanguagesDto.prototype, "id", void 0);
//# sourceMappingURL=languages.dto.js.map