{"version": 3, "file": "languages.dto.js", "sourceRoot": "", "sources": ["../../../src/languages/dto/languages.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA2D;AAC3D,qDAA6D;AAE7D,MAAa,WAAW;CA2BvB;AA3BD,kCA2BC;AApBC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;;yCACE;AAWb;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2BAA2B;QACxC,OAAO,EAAE,QAAQ;QACjB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;KAChE,CAAC;IACD,IAAA,wBAAM,EAAC,CAAC,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACjE,IAAA,0BAAQ,GAAE;;sDACe;AAQ1B;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;;6CACM;AAGnB,MAAa,kBAAmB,SAAQ,IAAA,qBAAW,EAAC,WAAW,CAAC;CAQ/D;AARD,gDAQC;AADC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,IAAI;KACf,CAAC;IACD,IAAA,0BAAQ,GAAE;;8CACA"}