import { LanguagesService } from './languages.service';
import { LanguageDto, UpdateLanguagesDto } from './dto/languages.dto';
export declare class LanguagesController {
    private readonly languagesService;
    constructor(languagesService: LanguagesService);
    createLanguage(language: LanguageDto): Promise<import("./langauges.entity").Languages>;
    updateLanguage(language: UpdateLanguagesDto): Promise<import("./langauges.entity").Languages>;
    deleteLanguage(id: number): Promise<void>;
    listLanguages(): Promise<import("./langauges.entity").Languages[]>;
    getLanguageById(id: number): Promise<import("./langauges.entity").Languages>;
}
