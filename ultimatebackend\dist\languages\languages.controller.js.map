{"version": 3, "file": "languages.controller.js", "sourceRoot": "", "sources": ["../../src/languages/languages.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAQwB;AACxB,2DAAuD;AACvD,6CAA2E;AAC3E,uDAAsE;AAI/D,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC9B,YAA6B,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;IAAG,CAAC;IAK7D,AAAN,KAAK,CAAC,cAAc,CAAS,QAAqB;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAKK,AAAN,KAAK,CAAC,cAAc,CACV,QAA4B;QAEpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CAAc,EAAU;QAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAIK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;IAClD,CAAC;IAIK,AAAN,KAAK,CAAC,eAAe,CAAc,EAAU;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;CACF,CAAA;AApCY,kDAAmB;AAMxB;IAHL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;IAClD,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,2BAAW,EAAE,CAAC;IACT,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAW,2BAAW;;yDAEjD;AAKK;IAHL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC9C,IAAA,iBAAO,EAAC,EAAE,IAAI,EAAE,kCAAkB,EAAE,CAAC;IAEnC,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAW,kCAAkB;;yDAGrC;AAIK;IAFL,IAAA,eAAM,EAAC,YAAY,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IACzB,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;yDAEhC;AAIK;IAFL,IAAA,YAAG,EAAC,MAAM,CAAC;IACX,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;;;;wDAG/C;AAIK;IAFL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC3B,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;0DAEjC;8BAnCU,mBAAmB;IAF/B,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,WAAW,CAAC;qCAEyB,oCAAgB;GADpD,mBAAmB,CAoC/B"}