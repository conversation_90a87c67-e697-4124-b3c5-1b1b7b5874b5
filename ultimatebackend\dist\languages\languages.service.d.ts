import { Languages } from './langauges.entity';
import { Repository } from 'typeorm';
import { LanguageDto, UpdateLanguagesDto } from './dto/languages.dto';
export declare class LanguagesService {
    private readonly languagesRepository;
    constructor(languagesRepository: Repository<Languages>);
    createLanguage(languageData: LanguageDto): Promise<Languages>;
    findAllLanguages(): Promise<Languages[]>;
    findLanguageById(id: number): Promise<Languages>;
    updateLanguage(updateData: UpdateLanguagesDto): Promise<Languages>;
    deleteLanguage(id: number): Promise<void>;
}
