"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LanguagesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const langauges_entity_1 = require("./langauges.entity");
const typeorm_2 = require("typeorm");
let LanguagesService = class LanguagesService {
    constructor(languagesRepository) {
        this.languagesRepository = languagesRepository;
    }
    async createLanguage(languageData) {
        try {
            const newLanguage = this.languagesRepository.create(languageData);
            return await this.languagesRepository.save(newLanguage);
        }
        catch (error) {
            throw new Error(`Failed to create language: ${error.message}`);
        }
    }
    async findAllLanguages() {
        try {
            return await this.languagesRepository.find();
        }
        catch (error) {
            throw new Error(`Failed to retrieve languages: ${error.message}`);
        }
    }
    async findLanguageById(id) {
        try {
            const language = await this.languagesRepository.findOne({
                where: { id },
            });
            if (!language) {
                throw new Error(`Language with ID ${id} not found`);
            }
            return language;
        }
        catch (error) {
            throw new Error(`Failed to retrieve language: ${error.message}`);
        }
    }
    async updateLanguage(updateData) {
        try {
            const language = await this.findLanguageById(updateData.id);
            Object.assign(language, updateData);
            return await this.languagesRepository.save(language);
        }
        catch (error) {
            throw new Error(`Failed to update language: ${error.message}`);
        }
    }
    async deleteLanguage(id) {
        try {
            const language = await this.findLanguageById(id);
            await this.languagesRepository.remove(language);
        }
        catch (error) {
            throw new Error(`Failed to delete language: ${error.message}`);
        }
    }
};
exports.LanguagesService = LanguagesService;
exports.LanguagesService = LanguagesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(langauges_entity_1.Languages)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LanguagesService);
//# sourceMappingURL=languages.service.js.map