export declare class GetMarketingEmailsDto {
    startDate?: string;
    endDate?: string;
    searchString?: string;
    sector_id?: string;
    page?: string;
    pageSize?: string;
}
export declare class GetCountryAndSectorWiseLeadsStatsDto {
    country_id?: string;
    sector_id?: string;
    from_date?: string;
    to_date?: string;
    user_id?: string;
    industry?: string;
}
export declare class GetCountryAndSectorWisePersonsDto {
    country_id?: string;
    sector_id?: string;
    from_date?: string;
    to_date?: string;
    user_id?: string;
    industry?: string;
    page?: string;
    size?: string;
    selectedFilter?: string;
}
export declare class GetTeamAssignedPersonsStatsDto {
    user_id?: string;
    from_date?: string;
    to_date?: string;
    country_id?: string;
    sector_id?: string;
}
export declare class GetUserWorkReportDto {
    user_id?: string;
    from_date?: string;
    to_date?: string;
    country_id?: string;
    sector_id?: string;
}
export declare class GetRegionWiseLeadsContactStatsDto {
    startDate?: string;
    endDate?: string;
}
export declare class GetDetailLeadReportsDto {
    startDate?: string;
    endDate?: string;
}
export declare class GetPersonWiseLeadsStatsDto {
    user_id?: string;
    from_date?: string;
    to_date?: string;
    country_id?: string;
    sector_id?: string;
    industry?: string;
}
