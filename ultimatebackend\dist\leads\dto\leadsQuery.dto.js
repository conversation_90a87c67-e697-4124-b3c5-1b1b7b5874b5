"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPersonWiseLeadsStatsDto = exports.GetDetailLeadReportsDto = exports.GetRegionWiseLeadsContactStatsDto = exports.GetUserWorkReportDto = exports.GetTeamAssignedPersonsStatsDto = exports.GetCountryAndSectorWisePersonsDto = exports.GetCountryAndSectorWiseLeadsStatsDto = exports.GetMarketingEmailsDto = void 0;
const class_validator_1 = require("class-validator");
class GetMarketingEmailsDto {
}
exports.GetMarketingEmailsDto = GetMarketingEmailsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "searchString", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "sector_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetMarketingEmailsDto.prototype, "pageSize", void 0);
class GetCountryAndSectorWiseLeadsStatsDto {
}
exports.GetCountryAndSectorWiseLeadsStatsDto = GetCountryAndSectorWiseLeadsStatsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "sector_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWiseLeadsStatsDto.prototype, "industry", void 0);
class GetCountryAndSectorWisePersonsDto {
}
exports.GetCountryAndSectorWisePersonsDto = GetCountryAndSectorWisePersonsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "sector_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "industry", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "size", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetCountryAndSectorWisePersonsDto.prototype, "selectedFilter", void 0);
class GetTeamAssignedPersonsStatsDto {
}
exports.GetTeamAssignedPersonsStatsDto = GetTeamAssignedPersonsStatsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetTeamAssignedPersonsStatsDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetTeamAssignedPersonsStatsDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetTeamAssignedPersonsStatsDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetTeamAssignedPersonsStatsDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetTeamAssignedPersonsStatsDto.prototype, "sector_id", void 0);
class GetUserWorkReportDto {
}
exports.GetUserWorkReportDto = GetUserWorkReportDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetUserWorkReportDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetUserWorkReportDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetUserWorkReportDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetUserWorkReportDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetUserWorkReportDto.prototype, "sector_id", void 0);
class GetRegionWiseLeadsContactStatsDto {
}
exports.GetRegionWiseLeadsContactStatsDto = GetRegionWiseLeadsContactStatsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetRegionWiseLeadsContactStatsDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetRegionWiseLeadsContactStatsDto.prototype, "endDate", void 0);
class GetDetailLeadReportsDto {
}
exports.GetDetailLeadReportsDto = GetDetailLeadReportsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetDetailLeadReportsDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetDetailLeadReportsDto.prototype, "endDate", void 0);
class GetPersonWiseLeadsStatsDto {
}
exports.GetPersonWiseLeadsStatsDto = GetPersonWiseLeadsStatsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "user_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "sector_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetPersonWiseLeadsStatsDto.prototype, "industry", void 0);
//# sourceMappingURL=leadsQuery.dto.js.map