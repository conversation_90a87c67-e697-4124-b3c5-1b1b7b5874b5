import { LeadsService } from './leads.service';
import { GetCountryAndSectorWiseLeadsStatsDto, GetCountryAndSectorWisePersonsDto, GetDetailLeadReportsDto, GetMarketingEmailsDto, GetPersonWiseLeadsStatsDto, GetRegionWiseLeadsContactStatsDto, GetTeamAssignedPersonsStatsDto, GetUserWorkReportDto } from './dto/leadsQuery.dto';
export declare class LeadsController {
    private readonly leadsService;
    constructor(leadsService: LeadsService);
    getMaerketingEmailsWithPersonName(queryParam: GetMarketingEmailsDto): Promise<{
        total: number;
        emails: import("../emails/emails.entity").PersonEmail[];
        page: number;
        pageSize: number;
    }>;
    getCountryAndSectorWiseLeadsStats(queryParam: GetCountryAndSectorWiseLeadsStatsDto): Promise<{
        totalPersons: number;
        withContactInfo: number;
        withoutContactInfo: number;
        verifiedLeads: number;
        bounceLeads: number;
        emailNotFoundLeads: number;
        notWorked: number;
        notAssigned: number;
    }>;
    getCountryAndSectorWisePersons(queryParam: GetCountryAndSectorWisePersonsDto): Promise<{
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            first_name: string;
            last_name: string;
            current_title: string;
            avator: string;
            headline: string;
            industry: string;
            SR_specied_industry: string;
            is_hiring_person: boolean;
            summary: string;
            date_of_birth: any;
            is_replacement_needed: boolean;
            is_email_info_added: boolean;
            createdAt: Date;
            updatedAt: Date;
            company_id: number;
            user_id: string;
            sector_id: number;
            is_email_not_found: boolean;
            is_replacement_not_found: boolean;
            company: import("../company/company.entity").Company;
            is_business_email_added: boolean;
            country: import("../country/country.entity").Country;
            sector: import("../sector/sector.entity").Sector;
            user_assigned: import("../users/users.entity").Users;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                is_default_email: boolean;
                type: string;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                is_default_email: boolean;
                type: string;
            }[];
            email_not_found_claim_users: import("../people/people.entity").People[];
            replacement_not_found_claim_users: import("../emails/emails.entity").PersonEmail[];
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    getTeamAssignedPersonsStats(queryParam: GetTeamAssignedPersonsStatsDto): Promise<{
        totalPersons: number;
        withEmailsCount: number;
        checkedEmailsCount: number;
        unCheckedEmailsCount: number;
        verifiedEmailsCount: number;
        bouncedEmailsCount: number;
        withoutEmailsCount: number;
    }>;
    getUserWorkReportV3(queryParam: GetUserWorkReportDto): Promise<{
        total_person_assigned: number;
        worked: number;
        not_worked: number;
        email_added: number;
        not_email_added: number;
        added_and_verified: number;
        replacements_added: number;
        status_not_updated: number;
    }>;
    getRegionWiseLeadsContactStats(queryParam: GetRegionWiseLeadsContactStatsDto): Promise<{
        regionWiseLeads: {
            region: string;
            companies: number;
            persons: number;
            jobposts: number;
            sectors: {
                id: number;
                sector: string;
                companies: number;
                persons: number;
                jobposts: number;
                basicCompanies: number;
            }[];
            basicCompanies: number;
        }[];
        totalStats: {
            companies: number;
            persons: number;
            jobposts: number;
            basicCompanies: number;
        };
    }>;
    getDetailLeadReports(queryParam: GetDetailLeadReportsDto): Promise<{
        companyData: {
            allCompaniesCount: number;
            allSnrCompaniesCount: number;
            allDirectCompaiesCount: number;
            UkCompaniesCount: number;
            UkSnrCompaniesCount: number;
            UkDirectCompaniesCount: number;
            UsCompaniesCount: number;
            UsSnrCompaniesCount: number;
            UsDirectCompaniesCount: number;
            basicCompaniesCount: number;
            advancedCompaniesCount: number;
            basicManualCompaniesCount: number;
            advancedManualCompaniesCount: number;
            jobPostScrapperCompaniesCount: number;
            jobPostScrapperSnrCompaniesCount: number;
            jobPostScrapperDirectCompaniesCount: number;
            manualCompaniesCount: number;
            manualSnrCompaniesCount: number;
            manualDirectCompaniesCount: number;
            companyProfileScrapperCompaniesCount: number;
            jobsPostsScrapperBasicCompaniesCount: number;
            jobsPostsScrapperAdvancedCompaniesCount: number;
            jobPostScrapperSnrBasicCompaniesCount: number;
            jobPostScrapperSnrAdvancedCompaniesCount: number;
            jobPostScrapperDirectBasicCompaniesCount: number;
            jobPostScrapperDirectAdvancedCompaniesCount: number;
            manualBasicCompaniesCount: number;
            manualAdvancedCompaniesCount: number;
            manualDirectBasicCompaniesCount: number;
            manualSnrBasicCompaniesCount: number;
            manualDirectAdvancedCompaniesCount: number;
            manualSnrAdvancedCompaniesCount: number;
            companyNotScrapped: number;
        };
        peopleData: {
            allPeopleCount: number;
            allSnrPeopleCount: number;
            allDirectPeopleCount: number;
            UkPeopleCount: number;
            UkSnrPeopleCount: number;
            UkDirectPeopleCount: number;
            UsPeopleCount: number;
            UsSnrPeopleCount: number;
            UsDirectPeopleCount: number;
            peopleFromLeadCount: number;
            peopleSnrFromLeadCount: number;
            peopleDirectFromLeadCount: number;
            totalAssignedPeopleCount: number;
            totalSnrAssignedPeopleCount: number;
            totalDirectAssignedPeopleCount: number;
            peopleWithEmailsCount: number;
            peopleSnrWithEmailsCount: number;
            peopleDirectWithEmailsCount: number;
            peopleWithoutEmailsCount: number;
            peopleSnrWithoutEmailsCount: number;
            peopleDirectWithoutEmailsCount: number;
            peopleWithWorkingCompleted: number;
            peopleWithoutWorkingCompleted: number;
            peopleWithReplacementNeeded: number;
            peopleWithBouncedEmailsCount: number;
        };
        emailsData: {
            allEmailsCount: number;
            allPersonalEmailsCount: number;
            allBusinessEmailsCount: number;
            allDefaultEmailsCount: number;
            allReplacedEmailsCount: number;
        };
        jobsData: {
            allJobPostsCount: number;
            allUkJobPostsCount: number;
            allUsJobPostsCount: number;
            allSnrJobPostsCount: number;
            allDirectJobPostsCount: number;
            allUkSnrJobPostsCount: number;
            allUkDirectJobPostsCount: number;
            allUsSnrJobPostsCount: number;
            allUsDirectJobPostsCount: number;
            allJobsPostedToday: number;
        };
        marketingEmailsData: {
            allMarketingEmailsCount: number;
            allInboxMarketingEmailsCount: number;
            allSentMarketingEmailsCount: number;
            allBouncedMarketingEmailsCount: number;
            allDraftMarketingEmailsCount: number;
        };
        scrapperDailyReportsData: import("../scrapper/scrapperStats.entity").ScrapperStats[];
    }>;
    getPersonWiseLeadsStats(queryParam: GetPersonWiseLeadsStatsDto): Promise<{
        totalPersons: number;
        withEmailsCount: number;
        checkedEmailsCount: number;
        unCheckedEmailsCount: number;
        verifiedEmailsCount: number;
        bouncedEmailsCount: number;
        withoutEmailsCount: number;
    }>;
}
