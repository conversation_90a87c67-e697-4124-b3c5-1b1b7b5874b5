"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadsController = void 0;
const common_1 = require("@nestjs/common");
const leads_service_1 = require("./leads.service");
const leadsQuery_dto_1 = require("./dto/leadsQuery.dto");
let LeadsController = class LeadsController {
    constructor(leadsService) {
        this.leadsService = leadsService;
    }
    async getMaerketingEmailsWithPersonName(queryParam) {
        return this.leadsService.getMarketingEmailsWithPersonName(queryParam);
    }
    async getCountryAndSectorWiseLeadsStats(queryParam) {
        return this.leadsService.getCountryAndSectorWiseLeadsStats(queryParam);
    }
    async getCountryAndSectorWisePersons(queryParam) {
        return this.leadsService.getCountryAndSectorWisePersons(queryParam);
    }
    async getTeamAssignedPersonsStats(queryParam) {
        return this.leadsService.getTeamAssignedPersonsStats(queryParam);
    }
    async getUserWorkReportV3(queryParam) {
        return this.leadsService.getUserWorkReportV3(queryParam);
    }
    async getRegionWiseLeadsContactStats(queryParam) {
        return this.leadsService.getRegionWiseLeadsContactStats(queryParam);
    }
    async getDetailLeadReports(queryParam) {
        return this.leadsService.detailLeadsReport(queryParam);
    }
    async getPersonWiseLeadsStats(queryParam) {
        return this.leadsService.getPersonWiseLeadsStats(queryParam);
    }
};
exports.LeadsController = LeadsController;
__decorate([
    (0, common_1.Get)('getMarketingEmailsWithPersonName'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetMarketingEmailsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getMaerketingEmailsWithPersonName", null);
__decorate([
    (0, common_1.Get)('getCountryAndSectorWiseLeadsStats'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetCountryAndSectorWiseLeadsStatsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getCountryAndSectorWiseLeadsStats", null);
__decorate([
    (0, common_1.Get)('getCountryAndSectorWisePersons'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetCountryAndSectorWisePersonsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getCountryAndSectorWisePersons", null);
__decorate([
    (0, common_1.Get)('getTeamAssignedPersonsStats'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetTeamAssignedPersonsStatsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getTeamAssignedPersonsStats", null);
__decorate([
    (0, common_1.Get)('getUserWorkReportV3'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetUserWorkReportDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getUserWorkReportV3", null);
__decorate([
    (0, common_1.Get)('getRegionWiseLeadsContactStats'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetRegionWiseLeadsContactStatsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getRegionWiseLeadsContactStats", null);
__decorate([
    (0, common_1.Get)('getDetailLeadReports'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetDetailLeadReportsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getDetailLeadReports", null);
__decorate([
    (0, common_1.Get)('getPersonWiseLeadsStats'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [leadsQuery_dto_1.GetPersonWiseLeadsStatsDto]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getPersonWiseLeadsStats", null);
exports.LeadsController = LeadsController = __decorate([
    (0, common_1.Controller)('leads'),
    __metadata("design:paramtypes", [leads_service_1.LeadsService])
], LeadsController);
//# sourceMappingURL=leads.controller.js.map