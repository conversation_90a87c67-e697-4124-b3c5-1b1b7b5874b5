"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadsModule = void 0;
const common_1 = require("@nestjs/common");
const leads_service_1 = require("./leads.service");
const leads_controller_1 = require("./leads.controller");
const typeorm_1 = require("@nestjs/typeorm");
const people_entity_1 = require("../people/people.entity");
const users_entity_1 = require("../users/users.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
const company_entity_1 = require("../company/company.entity");
const emails_entity_1 = require("../emails/emails.entity");
const country_entity_1 = require("../country/country.entity");
const sector_entity_1 = require("../sector/sector.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const mailBox_entity_1 = require("../mail-box/mailBox.entity");
const scrapperStats_entity_1 = require("../scrapper/scrapperStats.entity");
let LeadsModule = class LeadsModule {
};
exports.LeadsModule = LeadsModule;
exports.LeadsModule = LeadsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                people_entity_1.People,
                users_entity_1.Users,
                people_assignment_entity_1.PeopleAssignment,
                company_entity_1.Company,
                emails_entity_1.PersonEmail,
                country_entity_1.Country,
                sector_entity_1.Sector,
                jobs_entity_1.Jobs,
                mailBox_entity_1.MailBox,
                scrapperStats_entity_1.ScrapperStats,
            ]),
        ],
        controllers: [leads_controller_1.LeadsController],
        providers: [leads_service_1.LeadsService],
    })
], LeadsModule);
//# sourceMappingURL=leads.module.js.map