import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { People } from 'src/people/people.entity';
import { Users } from 'src/users/users.entity';
import { Repository } from 'typeorm';
import { GetCountryAndSectorWiseLeadsStatsDto, GetCountryAndSectorWisePersonsDto, GetDetailLeadReportsDto, GetMarketingEmailsDto, GetPersonWiseLeadsStatsDto, GetRegionWiseLeadsContactStatsDto, GetTeamAssignedPersonsStatsDto, GetUserWorkReportDto } from './dto/leadsQuery.dto';
import { Country } from 'src/country/country.entity';
import { Sector } from 'src/sector/sector.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { ScrapperStats } from 'src/scrapper/scrapperStats.entity';
export declare class LeadsService {
    private peopleRepository;
    private companyRepository;
    private peopleAssignmentRepository;
    private usersRepository;
    private personEmailRepository;
    private countryRepository;
    private sectorRepository;
    private jobsRepository;
    private mailBoxRepository;
    private scrapperStatsRepository;
    constructor(peopleRepository: Repository<People>, companyRepository: Repository<Company>, peopleAssignmentRepository: Repository<PeopleAssignment>, usersRepository: Repository<Users>, personEmailRepository: Repository<PersonEmail>, countryRepository: Repository<Country>, sectorRepository: Repository<Sector>, jobsRepository: Repository<Jobs>, mailBoxRepository: Repository<MailBox>, scrapperStatsRepository: Repository<ScrapperStats>);
    getMarketingEmailsWithPersonName(queryParam: GetMarketingEmailsDto): Promise<{
        total: number;
        emails: PersonEmail[];
        page: number;
        pageSize: number;
    }>;
    getCountryAndSectorWiseLeadsStats(queryParam: GetCountryAndSectorWiseLeadsStatsDto): Promise<{
        totalPersons: number;
        withContactInfo: number;
        withoutContactInfo: number;
        verifiedLeads: number;
        bounceLeads: number;
        emailNotFoundLeads: number;
        notWorked: number;
        notAssigned: number;
    }>;
    getCountryAndSectorWisePersons(queryParam: GetCountryAndSectorWisePersonsDto): Promise<{
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            first_name: string;
            last_name: string;
            current_title: string;
            avator: string;
            headline: string;
            industry: string;
            SR_specied_industry: string;
            is_hiring_person: boolean;
            summary: string;
            date_of_birth: any;
            is_replacement_needed: boolean;
            is_email_info_added: boolean;
            createdAt: Date;
            updatedAt: Date;
            company_id: number;
            user_id: string;
            sector_id: number;
            is_email_not_found: boolean;
            is_replacement_not_found: boolean;
            company: Company;
            is_business_email_added: boolean;
            country: Country;
            sector: Sector;
            user_assigned: Users;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                is_default_email: boolean;
                type: string;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                is_default_email: boolean;
                type: string;
            }[];
            email_not_found_claim_users: People[];
            replacement_not_found_claim_users: PersonEmail[];
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    getTeamAssignedPersonsStats(queryParam: GetTeamAssignedPersonsStatsDto): Promise<{
        totalPersons: number;
        withEmailsCount: number;
        checkedEmailsCount: number;
        unCheckedEmailsCount: number;
        verifiedEmailsCount: number;
        bouncedEmailsCount: number;
        withoutEmailsCount: number;
    }>;
    getUserWorkReportV3(queryParam: GetUserWorkReportDto): Promise<{
        total_person_assigned: number;
        worked: number;
        not_worked: number;
        email_added: number;
        not_email_added: number;
        added_and_verified: number;
        replacements_added: number;
        status_not_updated: number;
    }>;
    getRegionWiseLeadsContactStats(queryParam: GetRegionWiseLeadsContactStatsDto): Promise<{
        regionWiseLeads: {
            region: string;
            companies: number;
            persons: number;
            jobposts: number;
            sectors: {
                id: number;
                sector: string;
                companies: number;
                persons: number;
                jobposts: number;
                basicCompanies: number;
            }[];
            basicCompanies: number;
        }[];
        totalStats: {
            companies: number;
            persons: number;
            jobposts: number;
            basicCompanies: number;
        };
    }>;
    getPersonWiseLeadsStats(queryParams: GetPersonWiseLeadsStatsDto): Promise<{
        totalPersons: number;
        withEmailsCount: number;
        checkedEmailsCount: number;
        unCheckedEmailsCount: number;
        verifiedEmailsCount: number;
        bouncedEmailsCount: number;
        withoutEmailsCount: number;
    }>;
    detailLeadsReport(queryParams: GetDetailLeadReportsDto): Promise<{
        companyData: {
            allCompaniesCount: number;
            allSnrCompaniesCount: number;
            allDirectCompaiesCount: number;
            UkCompaniesCount: number;
            UkSnrCompaniesCount: number;
            UkDirectCompaniesCount: number;
            UsCompaniesCount: number;
            UsSnrCompaniesCount: number;
            UsDirectCompaniesCount: number;
            basicCompaniesCount: number;
            advancedCompaniesCount: number;
            basicManualCompaniesCount: number;
            advancedManualCompaniesCount: number;
            jobPostScrapperCompaniesCount: number;
            jobPostScrapperSnrCompaniesCount: number;
            jobPostScrapperDirectCompaniesCount: number;
            manualCompaniesCount: number;
            manualSnrCompaniesCount: number;
            manualDirectCompaniesCount: number;
            companyProfileScrapperCompaniesCount: number;
            jobsPostsScrapperBasicCompaniesCount: number;
            jobsPostsScrapperAdvancedCompaniesCount: number;
            jobPostScrapperSnrBasicCompaniesCount: number;
            jobPostScrapperSnrAdvancedCompaniesCount: number;
            jobPostScrapperDirectBasicCompaniesCount: number;
            jobPostScrapperDirectAdvancedCompaniesCount: number;
            manualBasicCompaniesCount: number;
            manualAdvancedCompaniesCount: number;
            manualDirectBasicCompaniesCount: number;
            manualSnrBasicCompaniesCount: number;
            manualDirectAdvancedCompaniesCount: number;
            manualSnrAdvancedCompaniesCount: number;
            companyNotScrapped: number;
        };
        peopleData: {
            allPeopleCount: number;
            allSnrPeopleCount: number;
            allDirectPeopleCount: number;
            UkPeopleCount: number;
            UkSnrPeopleCount: number;
            UkDirectPeopleCount: number;
            UsPeopleCount: number;
            UsSnrPeopleCount: number;
            UsDirectPeopleCount: number;
            peopleFromLeadCount: number;
            peopleSnrFromLeadCount: number;
            peopleDirectFromLeadCount: number;
            totalAssignedPeopleCount: number;
            totalSnrAssignedPeopleCount: number;
            totalDirectAssignedPeopleCount: number;
            peopleWithEmailsCount: number;
            peopleSnrWithEmailsCount: number;
            peopleDirectWithEmailsCount: number;
            peopleWithoutEmailsCount: number;
            peopleSnrWithoutEmailsCount: number;
            peopleDirectWithoutEmailsCount: number;
            peopleWithWorkingCompleted: number;
            peopleWithoutWorkingCompleted: number;
            peopleWithReplacementNeeded: number;
            peopleWithBouncedEmailsCount: number;
        };
        emailsData: {
            allEmailsCount: number;
            allPersonalEmailsCount: number;
            allBusinessEmailsCount: number;
            allDefaultEmailsCount: number;
            allReplacedEmailsCount: number;
        };
        jobsData: {
            allJobPostsCount: number;
            allUkJobPostsCount: number;
            allUsJobPostsCount: number;
            allSnrJobPostsCount: number;
            allDirectJobPostsCount: number;
            allUkSnrJobPostsCount: number;
            allUkDirectJobPostsCount: number;
            allUsSnrJobPostsCount: number;
            allUsDirectJobPostsCount: number;
            allJobsPostedToday: number;
        };
        marketingEmailsData: {
            allMarketingEmailsCount: number;
            allInboxMarketingEmailsCount: number;
            allSentMarketingEmailsCount: number;
            allBouncedMarketingEmailsCount: number;
            allDraftMarketingEmailsCount: number;
        };
        scrapperDailyReportsData: ScrapperStats[];
    }>;
}
