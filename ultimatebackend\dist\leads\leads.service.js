"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadsService = void 0;
const people_enums_1 = require("./../people/dto/people.enums");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const company_entity_1 = require("../company/company.entity");
const emails_entity_1 = require("../emails/emails.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
const people_entity_1 = require("../people/people.entity");
const users_entity_1 = require("../users/users.entity");
const typeorm_2 = require("typeorm");
const country_entity_1 = require("../country/country.entity");
const sector_entity_1 = require("../sector/sector.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const person_email_dto_1 = require("../emails/dto/person-email.dto");
const mailBox_entity_1 = require("../mail-box/mailBox.entity");
const scrapperStats_entity_1 = require("../scrapper/scrapperStats.entity");
let LeadsService = class LeadsService {
    constructor(peopleRepository, companyRepository, peopleAssignmentRepository, usersRepository, personEmailRepository, countryRepository, sectorRepository, jobsRepository, mailBoxRepository, scrapperStatsRepository) {
        this.peopleRepository = peopleRepository;
        this.companyRepository = companyRepository;
        this.peopleAssignmentRepository = peopleAssignmentRepository;
        this.usersRepository = usersRepository;
        this.personEmailRepository = personEmailRepository;
        this.countryRepository = countryRepository;
        this.sectorRepository = sectorRepository;
        this.jobsRepository = jobsRepository;
        this.mailBoxRepository = mailBoxRepository;
        this.scrapperStatsRepository = scrapperStatsRepository;
    }
    async getMarketingEmailsWithPersonName(queryParam) {
        const { startDate, endDate, searchString, sector_id, page, pageSize } = queryParam;
        const safeStartDate = typeof startDate === 'string' ? startDate.trim() : undefined;
        const safeEndDate = typeof endDate === 'string' ? endDate.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        const pageInt = parseInt(page) || 0;
        const pageSizeInt = parseInt(pageSize) || 10;
        const skip = pageInt * pageSizeInt;
        const query = this.personEmailRepository
            .createQueryBuilder('email')
            .leftJoinAndSelect('email.person', 'person')
            .where('email.is_verified = :isVerified', { isVerified: true })
            .andWhere('email.is_unsubscribed = :isUnsubscribed', {
            isUnsubscribed: false,
        });
        if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
            query.andWhere('email.created_at BETWEEN :start AND :end', {
                start: `${safeStartDate}T00:00:00.000Z`,
                end: `${safeEndDate}T23:59:59.999Z`,
            });
        }
        else if (isValidDate(safeStartDate)) {
            query.andWhere('email.created_at >= :start', {
                start: `${safeStartDate}T00:00:00.000Z`,
            });
        }
        else if (isValidDate(safeEndDate)) {
            query.andWhere('email.created_at <= :end', {
                end: `${safeEndDate}T23:59:59.999Z`,
            });
        }
        if (sector_id && !isNaN(Number(sector_id))) {
            query.andWhere('person.sectorId = :sectorId', {
                sectorId: Number(sector_id),
            });
        }
        else {
            query.andWhere('person.sectorId IS NOT NULL');
        }
        if (searchString) {
            query.andWhere('person.first_name ILIKE :search', {
                search: `%${searchString}%`,
            });
        }
        query.orderBy('email.created_at', 'DESC');
        query.skip(skip).take(pageSizeInt);
        const [emails, total] = await query.getManyAndCount();
        return {
            total,
            emails,
            page: pageInt,
            pageSize: pageSizeInt,
        };
    }
    async getCountryAndSectorWiseLeadsStats(queryParam) {
        const { country_id, sector_id, from_date, to_date, user_id, industry } = queryParam;
        const safeFromDate = typeof from_date === 'string' ? from_date.trim() : undefined;
        const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        const whereQuery = {};
        const allPersons = await this.peopleRepository.findAndCount({
            select: ['id'],
        });
        const personIds = allPersons[0].map((person) => person.id);
        const totalPersons = allPersons[1];
        if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
            whereQuery.created_at = (0, typeorm_2.Between)(safeFromDate, safeToDate);
        }
        else if (isValidDate(safeFromDate)) {
            whereQuery.created_at = (0, typeorm_2.MoreThanOrEqual)(safeFromDate);
        }
        else if (isValidDate(safeToDate)) {
            whereQuery.created_at = (0, typeorm_2.LessThanOrEqual)(safeToDate);
        }
        if (country_id && !isNaN(Number(country_id))) {
            whereQuery['person.countryId'] = parseInt(country_id);
        }
        if (sector_id && !isNaN(Number(sector_id))) {
            whereQuery['person.sectorId'] = parseInt(sector_id);
        }
        if (user_id) {
            whereQuery.leadUserId = user_id;
        }
        if (industry) {
            whereQuery['person.industry'] = industry;
        }
        const withContactInfo = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                is_business_email_added: true,
                ...whereQuery,
            },
        });
        const withoutContactInfo = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                is_business_email_added: false,
                ...whereQuery,
            },
        });
        const verifiedLeads = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                is_working_completed: true,
                ...whereQuery,
            },
        });
        const bounceLeads = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                is_replacement_needed: true,
                is_bounce_back: true,
                ...whereQuery,
            },
        });
        const emailNotFoundLeads = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                is_email_not_found: true,
                ...whereQuery,
            },
        });
        const notWorked = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.In)(personIds),
                leadUser: {
                    id: (0, typeorm_2.Not)(null),
                },
                is_business_email_added: false,
                ...whereQuery,
            },
        });
        const notAssigned = await this.peopleAssignmentRepository.count({
            where: {
                personId: (0, typeorm_2.Not)((0, typeorm_2.In)(personIds)),
                ...whereQuery,
            },
        });
        return {
            totalPersons,
            withContactInfo,
            withoutContactInfo,
            verifiedLeads,
            bounceLeads,
            emailNotFoundLeads,
            notWorked,
            notAssigned,
        };
    }
    async getCountryAndSectorWisePersons(queryParam) {
        const { country_id, sector_id, from_date, to_date, user_id, industry, page, size, selectedFilter, } = queryParam;
        const pageInt = parseInt(page);
        const pageSizeInt = parseInt(size);
        const limit = pageSizeInt;
        const skip = pageInt * limit;
        const safeFromDate = typeof from_date === 'string' ? from_date.trim() : undefined;
        const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        let whereCondition = {};
        if (selectedFilter === 'with') {
            whereCondition.is_business_email_added = true;
        }
        if (selectedFilter === 'without') {
            whereCondition.is_business_email_added = false;
        }
        if (selectedFilter === 'verified') {
            whereCondition.is_working_completed = true;
        }
        if (selectedFilter === 'bounced') {
            whereCondition.is_replacement_needed = true;
        }
        if (selectedFilter === 'checked') {
            whereCondition.is_business_email_added = true;
            whereCondition = [
                { is_working_completed: true },
                { is_replacement_needed: true },
            ];
        }
        if (selectedFilter === 'unchecked') {
            whereCondition.is_business_email_added = true;
            whereCondition = [
                { is_working_completed: false },
                { is_replacement_needed: false },
            ];
        }
        if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.Between)(safeFromDate, safeToDate);
        }
        else if (isValidDate(safeFromDate)) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(safeFromDate);
        }
        else if (isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(safeToDate);
        }
        if (country_id && !isNaN(Number(country_id))) {
            whereCondition['person.countryId'] = parseInt(country_id);
        }
        if (sector_id && !isNaN(Number(sector_id))) {
            whereCondition['person.sectorId'] = parseInt(sector_id);
        }
        if (user_id) {
            whereCondition.leadUserId = user_id;
        }
        if (industry) {
            whereCondition['person.industry'] = industry;
        }
        const [persons, total] = await this.peopleAssignmentRepository.findAndCount({
            where: {
                ...whereCondition,
            },
            relations: [
                'person',
                'person.company',
                'person.country',
                'person.sector',
                'person.emails',
                'leadUser',
            ],
            skip: skip,
            take: limit,
            order: {
                created_at: 'DESC',
            },
        });
        const processedPersons = persons.map((assignment) => {
            const businessEmails = assignment.person?.emails?.filter((email) => email.email_type === 'BUSINESS') || [];
            const personalEmails = assignment.person?.emails?.filter((email) => email.email_type === 'PERSONAL') || [];
            const emailNotFoundUsers = assignment.is_email_not_found === true ? [assignment.person] : [];
            const replacementNotFoundUsers = assignment.person?.emails?.filter((email) => email.isReplacedBy != null) || [];
            return {
                id: assignment.person?.id,
                profile_url: assignment.person?.profile_url,
                full_name: `${assignment.person?.first_name || ''} ${assignment.person?.last_name || ''}`.trim(),
                first_name: assignment.person?.first_name,
                last_name: assignment.person?.last_name,
                current_title: assignment.person?.current_title,
                avator: assignment.person?.profile_img,
                headline: assignment.person?.headline,
                industry: assignment.person?.industry,
                SR_specied_industry: assignment.person?.industry,
                is_hiring_person: assignment.is_hiring_person,
                summary: assignment.person?.summary,
                date_of_birth: null,
                is_replacement_needed: assignment.is_replacement_needed,
                is_email_info_added: assignment.is_email_info_added,
                createdAt: assignment.created_at,
                updatedAt: assignment.updated_at,
                company_id: assignment.person?.companyId,
                user_id: assignment.person?.userId,
                sector_id: assignment.person?.sectorId,
                is_email_not_found: assignment.is_email_not_found,
                is_replacement_not_found: assignment.is_replacement_not_found,
                company: assignment.person?.company,
                is_business_email_added: assignment.is_business_email_added,
                country: assignment.person?.country,
                sector: assignment.person?.sector,
                user_assigned: assignment.leadUser,
                businessEmails: businessEmails.map((e) => ({
                    id: e.id,
                    email_id: e.email,
                    user_id: e.isAddedBy,
                    is_default_email: e.is_default,
                    type: e.email_type,
                })),
                personalEmails: personalEmails.map((e) => ({
                    id: e.id,
                    email_id: e.email,
                    user_id: e.isAddedBy,
                    is_default_email: e.is_default,
                    type: e.email_type,
                })),
                email_not_found_claim_users: emailNotFoundUsers,
                replacement_not_found_claim_users: replacementNotFoundUsers,
            };
        });
        return {
            data: processedPersons,
            total: total,
            page: pageInt,
            pageSize: pageSizeInt,
            totalPages: Math.ceil(total / pageSizeInt),
        };
    }
    async getTeamAssignedPersonsStats(queryParam) {
        const { user_id, from_date, to_date, country_id, sector_id } = queryParam;
        const safeFromDate = typeof from_date === 'string' ? from_date.trim() : undefined;
        const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        const whereCondition = {};
        if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.Between)(safeFromDate, safeToDate);
        }
        else if (isValidDate(safeFromDate)) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(safeFromDate);
        }
        else if (isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(safeToDate);
        }
        if (country_id && !isNaN(Number(country_id))) {
            whereCondition['person.countryId'] = parseInt(country_id);
        }
        if (sector_id && !isNaN(Number(sector_id))) {
            whereCondition['person.sectorId'] = parseInt(sector_id);
        }
        if (user_id) {
            whereCondition['leadUser.id'] = user_id;
        }
        const allPersons = await this.peopleRepository.findAndCount({
            select: ['id'],
        });
        const personIds = allPersons[0].map((person) => person.id);
        const totalPersons = allPersons[1];
        const withEmailsCount = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: true,
            },
        });
        const checkedEmailsCount = await this.peopleAssignmentRepository.count({
            where: [
                {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_email_not_found: true,
                },
                {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_replacement_not_found: true,
                },
            ],
        });
        const unCheckedEmailsCount = await this.peopleAssignmentRepository.count({
            where: [
                {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_email_not_found: true,
                },
                {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_replacement_not_found: true,
                },
            ],
        });
        const verifiedEmailsCount = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: true,
                is_working_completed: true,
            },
        });
        const bouncedEmailsCount = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: true,
                is_replacement_needed: true,
            },
        });
        const withoutEmailsCount = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: false,
            },
        });
        return {
            totalPersons,
            withEmailsCount,
            checkedEmailsCount,
            unCheckedEmailsCount,
            verifiedEmailsCount,
            bouncedEmailsCount,
            withoutEmailsCount,
        };
    }
    async getUserWorkReportV3(queryParam) {
        const { user_id, from_date, to_date, country_id, sector_id } = queryParam;
        const safeFromDate = typeof from_date === 'string' ? from_date.trim() : undefined;
        const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        const whereCondition = {};
        if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.Between)(safeFromDate, safeToDate);
        }
        else if (isValidDate(safeFromDate)) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(safeFromDate);
        }
        else if (isValidDate(safeToDate)) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(safeToDate);
        }
        if (country_id && !isNaN(Number(country_id))) {
            whereCondition['person.countryId'] = parseInt(country_id);
        }
        if (sector_id && !isNaN(Number(sector_id))) {
            whereCondition['person.sectorId'] = parseInt(sector_id);
        }
        if (user_id) {
            whereCondition['leadUser.id'] = user_id;
        }
        const total_person_assigned = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                leadUserId: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
            },
        });
        const worked = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                leadUserId: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
                is_working_completed: true,
            },
            relations: {
                person: true,
            },
        });
        const not_worked = Math.max(0, total_person_assigned - worked);
        const email_added = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: true,
            },
            relations: {
                person: true,
            },
        });
        const not_email_added = Math.max(0, total_person_assigned - email_added);
        const added_and_verified = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_verified: true,
                is_business_email_added: true,
            },
            relations: {
                person: true,
            },
        });
        const replacements_added = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                is_business_email_added: true,
                is_replacement: true,
            },
            relations: {
                person: true,
            },
        });
        const status_not_updated = await this.peopleAssignmentRepository.count({
            where: {
                ...whereCondition,
                status: people_assignment_entity_1.AssignmentStatus.NOT_VERIFIED,
            },
            relations: {
                person: true,
            },
        });
        return {
            total_person_assigned,
            worked,
            not_worked,
            email_added,
            not_email_added,
            added_and_verified,
            replacements_added,
            status_not_updated,
        };
    }
    async getRegionWiseLeadsContactStats(queryParam) {
        const { startDate, endDate } = queryParam;
        const safeStartDate = typeof startDate === 'string' ? startDate.trim() : undefined;
        const safeEndDate = typeof endDate === 'string' ? endDate.trim() : undefined;
        const whereCondition = {};
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
            if (safeStartDate === safeEndDate) {
                whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(`${safeStartDate}T00:00:00.000Z`);
            }
            else {
                whereCondition.created_at = (0, typeorm_2.Between)(`${safeStartDate}T00:00:00.000Z`, `${safeEndDate}T23:59:59.999Z`);
            }
        }
        else if (isValidDate(safeStartDate)) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(`${safeStartDate}T00:00:00.000Z`);
        }
        else if (isValidDate(safeEndDate)) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(`${safeEndDate}T23:59:59.999Z`);
        }
        const allCountries = await this.countryRepository.find();
        const allSectors = await this.sectorRepository.find();
        const regions = [...new Set(allCountries.map((country) => country.region))];
        const regionWiseLeads = await Promise.all(regions.map(async (region) => {
            const countriesInRegion = allCountries
                .filter((country) => country.region === region)
                .map((country) => country.id);
            const [companiesCount, peopleCount, jobPostsCount, basicCompaniesCount,] = await Promise.all([
                this.companyRepository.count({
                    where: { ...whereCondition, countryId: (0, typeorm_2.In)(countriesInRegion) },
                }),
                this.peopleRepository.count({
                    where: { ...whereCondition, countryId: (0, typeorm_2.In)(countriesInRegion) },
                }),
                this.jobsRepository.count({
                    where: { ...whereCondition, countryId: (0, typeorm_2.In)(countriesInRegion) },
                }),
                this.companyRepository.count({
                    where: {
                        ...whereCondition,
                        countryId: (0, typeorm_2.In)(countriesInRegion),
                        scrapper_level: 2,
                    },
                }),
            ]);
            const sectorsData = await Promise.all(allSectors.map(async (sector) => {
                const sectorCompaniesCount = await this.companyRepository.count({
                    where: {
                        ...whereCondition,
                        sectorId: sector.id,
                        countryId: (0, typeorm_2.In)(countriesInRegion),
                    },
                });
                const sectorPeopleCount = await this.peopleRepository.count({
                    where: {
                        ...whereCondition,
                        sectorId: sector.id,
                        countryId: (0, typeorm_2.In)(countriesInRegion),
                    },
                });
                const sectorJobPostsCount = await this.jobsRepository.count({
                    where: {
                        ...whereCondition,
                        sectorId: sector.id,
                        countryId: (0, typeorm_2.In)(countriesInRegion),
                    },
                });
                const basicSectorCompaniesCount = await this.companyRepository.count({
                    where: {
                        ...whereCondition,
                        sectorId: sector.id,
                        countryId: (0, typeorm_2.In)(countriesInRegion),
                        scrapper_level: 2,
                    },
                });
                return {
                    id: sector.id,
                    sector: sector.name,
                    companies: sectorCompaniesCount,
                    persons: sectorPeopleCount,
                    jobposts: sectorJobPostsCount,
                    basicCompanies: basicSectorCompaniesCount,
                };
            }));
            return {
                region,
                companies: companiesCount,
                persons: peopleCount,
                jobposts: jobPostsCount,
                sectors: sectorsData,
                basicCompanies: basicCompaniesCount,
            };
        }));
        const totalStats = regionWiseLeads.reduce((acc, regionData) => {
            acc.companies += regionData.companies;
            acc.persons += regionData.persons;
            acc.jobposts += regionData.jobposts;
            acc.basicCompanies += regionData.basicCompanies;
            return acc;
        }, { companies: 0, persons: 0, jobposts: 0, basicCompanies: 0 });
        return {
            regionWiseLeads,
            totalStats,
        };
    }
    async getPersonWiseLeadsStats(queryParams) {
        const { user_id, from_date, to_date, country_id, sector_id, industry } = queryParams;
        const safeStartDate = typeof from_date === 'string' ? from_date.trim() : undefined;
        const safeEndDate = typeof to_date === 'string' ? to_date.trim() : undefined;
        const whereCondition = {};
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
            if (safeStartDate === safeEndDate) {
                whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(`${safeStartDate}T00:00:00.000Z`);
            }
            else {
                whereCondition.created_at = (0, typeorm_2.Between)(`${safeStartDate}T00:00:00.000Z`, `${safeEndDate}T23:59:59.999Z`);
            }
        }
        else if (isValidDate(safeStartDate)) {
            whereCondition.created_at = (0, typeorm_2.MoreThanOrEqual)(`${safeStartDate}T00:00:00.000Z`);
        }
        else if (isValidDate(safeEndDate)) {
            whereCondition.created_at = (0, typeorm_2.LessThanOrEqual)(`${safeEndDate}T23:59:59.999Z`);
        }
        if (country_id) {
            whereCondition.countryId = country_id;
        }
        if (sector_id) {
            whereCondition.sectorId = sector_id;
        }
        if (industry) {
            whereCondition.industry = industry;
        }
        if (user_id) {
            whereCondition.leadUserId = user_id;
        }
        try {
            const allPersons = await this.peopleRepository.findAndCount({
                select: ['id'],
            });
            const personIds = allPersons[0].map((person) => person.id);
            const totalPersons = allPersons[1];
            const withEmailsCount = await this.peopleAssignmentRepository.count({
                where: {
                    ...whereCondition,
                    is_business_email_added: true,
                },
            });
            const checkedEmailsCount = await this.peopleAssignmentRepository.count({
                where: [
                    {
                        ...whereCondition,
                        is_business_email_added: true,
                        is_email_not_found: true,
                    },
                    {
                        ...whereCondition,
                        is_business_email_added: true,
                        is_replacement_not_found: true,
                    },
                ],
            });
            const unCheckedEmailsCount = await this.peopleAssignmentRepository.count({
                where: [
                    {
                        ...whereCondition,
                        is_business_email_added: true,
                        is_email_not_found: true,
                    },
                    {
                        ...whereCondition,
                        is_business_email_added: true,
                        is_replacement_not_found: true,
                    },
                ],
            });
            const verifiedEmailsCount = await this.peopleAssignmentRepository.count({
                where: {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_working_completed: true,
                },
            });
            const bouncedEmailsCount = await this.peopleAssignmentRepository.count({
                where: {
                    ...whereCondition,
                    is_business_email_added: true,
                    is_replacement_needed: true,
                },
            });
            const withoutEmailsCount = await this.peopleAssignmentRepository.count({
                where: {
                    ...whereCondition,
                    is_business_email_added: false,
                },
            });
            return {
                totalPersons,
                withEmailsCount,
                checkedEmailsCount,
                unCheckedEmailsCount,
                verifiedEmailsCount,
                bouncedEmailsCount,
                withoutEmailsCount,
            };
        }
        catch (error) {
            throw error;
        }
    }
    async detailLeadsReport(queryParams) {
        const { startDate, endDate } = queryParams;
        const safeStartDate = typeof startDate === 'string' ? startDate.trim() : undefined;
        const safeEndDate = typeof endDate === 'string' ? endDate.trim() : undefined;
        const dateClause = [];
        const dateParams = {};
        if (safeStartDate && safeEndDate && safeStartDate === safeEndDate) {
            dateClause.push('DATE(updated_at) = :date');
            dateParams.date = safeStartDate;
        }
        else if (safeStartDate && safeEndDate) {
            dateClause.push('DATE(updated_at) BETWEEN :start AND :end');
            dateParams.start = safeStartDate;
            dateParams.end = safeEndDate;
        }
        else if (safeStartDate) {
            dateClause.push('DATE(updated_at) >= :start');
            dateParams.start = safeStartDate;
        }
        else if (safeEndDate) {
            dateClause.push('DATE(updated_at) <= :end');
            dateParams.end = safeEndDate;
        }
        const applyDateClause = (qb, alias = '') => {
            if (dateClause.length > 0) {
                qb.andWhere(dateClause[0].replace(/updated_at/g, `${alias}updated_at`), dateParams);
            }
        };
        const baseCompanyQB = () => {
            const qb = this.companyRepository.createQueryBuilder('company');
            applyDateClause(qb, 'company.');
            return qb;
        };
        const allCompaniesCount = await baseCompanyQB().getCount();
        const allSnrCompaniesCount = await baseCompanyQB()
            .andWhere('company.sectorId = 2')
            .getCount();
        const allDirectCompaiesCount = await baseCompanyQB()
            .andWhere('company.sectorId = 1')
            .getCount();
        const UkCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 1')
            .getCount();
        const UkSnrCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 1')
            .andWhere('company.sectorId = 2')
            .getCount();
        const UkDirectCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 1')
            .andWhere('company.sectorId = 1')
            .getCount();
        const UsCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 2')
            .getCount();
        const UsSnrCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 2')
            .andWhere('company.sectorId = 2')
            .getCount();
        const UsDirectCompaniesCount = await baseCompanyQB()
            .andWhere('company.countryId = 2')
            .andWhere('company.sectorId = 1')
            .getCount();
        const basicCompaniesCount = await baseCompanyQB()
            .andWhere('company.scrapper_level = 2')
            .getCount();
        const advancedCompaniesCount = await baseCompanyQB()
            .andWhere('company.scrapper_level = 3')
            .getCount();
        const basicManualCompaniesCount = await baseCompanyQB()
            .andWhere('company.scrapper_level = 2')
            .andWhere("company.company_source = 'MANUAL'")
            .getCount();
        const advancedManualCompaniesCount = await baseCompanyQB()
            .andWhere('company.scrapper_level = 3')
            .andWhere("company.company_source = 'MANUAL'")
            .getCount();
        const jobPostScrapperCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .getCount();
        const jobsPostsScrapperBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 2')
            .getCount();
        const jobsPostsScrapperAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 3')
            .getCount();
        const jobPostScrapperSnrCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.sectorId = 2')
            .getCount();
        const jobPostScrapperSnrBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 2')
            .andWhere('company.sectorId = 2')
            .getCount();
        const jobPostScrapperSnrAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 3')
            .andWhere('company.sectorId = 2')
            .getCount();
        const jobPostScrapperDirectBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 2')
            .andWhere('company.sectorId = 1')
            .getCount();
        const jobPostScrapperDirectAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.scrapper_level = 3')
            .andWhere('company.sectorId = 1')
            .getCount();
        const jobPostScrapperDirectCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
            .andWhere('company.sectorId = 1')
            .getCount();
        const manualCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .getCount();
        const manualBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 2')
            .getCount();
        const manualSnrCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.sectorId = 2')
            .getCount();
        const manualSnrBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 2')
            .andWhere('company.sectorId = 2')
            .getCount();
        const manualDirectBasicCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 2')
            .andWhere('company.sectorId = 1')
            .getCount();
        const manualDirectAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 3')
            .andWhere('company.sectorId = 2')
            .getCount();
        const manualSnrAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 3')
            .andWhere('company.sectorId = 2')
            .getCount();
        const manualDirectCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.sectorId = 1')
            .getCount();
        const manualAdvancedCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'MANUAL'")
            .andWhere('company.scrapper_level = 3')
            .getCount();
        const companyProfileScrapperCompaniesCount = await baseCompanyQB()
            .andWhere("company.company_source = 'COMPANY_PROFILE_SCRAPPER'")
            .getCount();
        const companyNotScrapped = await baseCompanyQB()
            .andWhere('company.sectorId IS NULL AND company.scrapper_level = 2')
            .getCount();
        const companyData = {
            allCompaniesCount,
            allSnrCompaniesCount,
            allDirectCompaiesCount,
            UkCompaniesCount,
            UkSnrCompaniesCount,
            UkDirectCompaniesCount,
            UsCompaniesCount,
            UsSnrCompaniesCount,
            UsDirectCompaniesCount,
            basicCompaniesCount,
            advancedCompaniesCount,
            basicManualCompaniesCount,
            advancedManualCompaniesCount,
            jobPostScrapperCompaniesCount,
            jobPostScrapperSnrCompaniesCount,
            jobPostScrapperDirectCompaniesCount,
            manualCompaniesCount,
            manualSnrCompaniesCount,
            manualDirectCompaniesCount,
            companyProfileScrapperCompaniesCount,
            jobsPostsScrapperBasicCompaniesCount,
            jobsPostsScrapperAdvancedCompaniesCount,
            jobPostScrapperSnrBasicCompaniesCount,
            jobPostScrapperSnrAdvancedCompaniesCount,
            jobPostScrapperDirectBasicCompaniesCount,
            jobPostScrapperDirectAdvancedCompaniesCount,
            manualBasicCompaniesCount,
            manualAdvancedCompaniesCount,
            manualDirectBasicCompaniesCount,
            manualSnrBasicCompaniesCount,
            manualDirectAdvancedCompaniesCount,
            manualSnrAdvancedCompaniesCount,
            companyNotScrapped,
        };
        const basePeopleQB = () => {
            const qb = this.peopleRepository.createQueryBuilder('people');
            applyDateClause(qb, 'people.');
            return qb;
        };
        const allPeopleCount = await basePeopleQB()
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const allSnrPeopleCount = await basePeopleQB()
            .andWhere('people.sectorId = 2')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const allDirectPeopleCount = await basePeopleQB()
            .andWhere('people.sectorId = 1')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UkPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 1')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UkSnrPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 1')
            .andWhere('people.sectorId = 2')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UkDirectPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 1')
            .andWhere('people.sectorId = 1')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UsPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 2')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UsSnrPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 2')
            .andWhere('people.sectorId = 2')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const UsDirectPeopleCount = await basePeopleQB()
            .andWhere('people.countryId = 2')
            .andWhere('people.sectorId = 1')
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .getCount();
        const peopleFromLeadCount = await basePeopleQB()
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .andWhere('people.is_hiring = :isHiring', { isHiring: true })
            .getCount();
        const peopleSnrFromLeadCount = await basePeopleQB()
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .andWhere('people.sectorId = 2')
            .andWhere('people.is_hiring = :isHiring', { isHiring: true })
            .getCount();
        const peopleDirectFromLeadCount = await basePeopleQB()
            .andWhere('people.person_type = :ptype', {
            ptype: people_enums_1.PersonType.JOB_POST_LEAD,
        })
            .andWhere('people.sectorId = 1')
            .andWhere('people.is_hiring = :isHiring', { isHiring: true })
            .getCount();
        const basePeopleAssignmentQB = () => {
            const qb = this.peopleAssignmentRepository.createQueryBuilder('pa');
            applyDateClause(qb, 'pa.');
            return qb;
        };
        const totalAssignedPeopleCount = await basePeopleAssignmentQB().getCount();
        const totalSnrAssignedPeopleCount = await basePeopleAssignmentQB()
            .andWhere('pa.sectorId = 2')
            .getCount();
        const totalDirectAssignedPeopleCount = await basePeopleAssignmentQB()
            .andWhere('pa.sectorId = 1')
            .getCount();
        const peopleWithEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = true')
            .andWhere('pa.is_found = true')
            .getCount();
        const peopleSnrWithEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = true')
            .andWhere('pa.is_found = true')
            .andWhere('pa.sectorId = 2')
            .getCount();
        const peopleDirectWithEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = true')
            .andWhere('pa.is_found = true')
            .andWhere('pa.sectorId = 1')
            .getCount();
        const peopleWithoutEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = false')
            .andWhere('pa.is_found = false')
            .getCount();
        const peopleSnrWithoutEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = false')
            .andWhere('pa.is_found = false')
            .andWhere('pa.sectorId = 2')
            .getCount();
        const peopleDirectWithoutEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = false')
            .andWhere('pa.is_found = false')
            .andWhere('pa.sectorId = 1')
            .getCount();
        const peopleWithWorkingCompleted = await basePeopleAssignmentQB()
            .andWhere('pa.is_working_completed = true')
            .getCount();
        const peopleWithoutWorkingCompleted = await basePeopleAssignmentQB()
            .andWhere('pa.is_working_completed = false')
            .andWhere('pa.is_business_email_added = false')
            .getCount();
        const peopleWithReplacementNeeded = await basePeopleAssignmentQB()
            .andWhere('pa.is_business_email_added = false')
            .andWhere('pa.is_replacement_needed = true')
            .getCount();
        const peopleWithBouncedEmailsCount = await basePeopleAssignmentQB()
            .andWhere('pa.is_bounce_back = true')
            .getCount();
        const peopleData = {
            allPeopleCount,
            allSnrPeopleCount,
            allDirectPeopleCount,
            UkPeopleCount,
            UkSnrPeopleCount,
            UkDirectPeopleCount,
            UsPeopleCount,
            UsSnrPeopleCount,
            UsDirectPeopleCount,
            peopleFromLeadCount,
            peopleSnrFromLeadCount,
            peopleDirectFromLeadCount,
            totalAssignedPeopleCount,
            totalSnrAssignedPeopleCount,
            totalDirectAssignedPeopleCount,
            peopleWithEmailsCount,
            peopleSnrWithEmailsCount,
            peopleDirectWithEmailsCount,
            peopleWithoutEmailsCount,
            peopleSnrWithoutEmailsCount,
            peopleDirectWithoutEmailsCount,
            peopleWithWorkingCompleted,
            peopleWithoutWorkingCompleted,
            peopleWithReplacementNeeded,
            peopleWithBouncedEmailsCount,
        };
        const baseEmailQB = () => {
            const qb = this.personEmailRepository.createQueryBuilder('email');
            applyDateClause(qb, 'email.');
            return qb;
        };
        const allEmailsCount = await baseEmailQB().getCount();
        const allPersonalEmailsCount = await baseEmailQB()
            .andWhere('email.email_type = :et', { et: person_email_dto_1.PersonEmailType.PERSONAL })
            .getCount();
        const allBusinessEmailsCount = await baseEmailQB()
            .andWhere('email.email_type = :et', { et: person_email_dto_1.PersonEmailType.BUSINESS })
            .getCount();
        const allDefaultEmailsCount = await baseEmailQB()
            .andWhere('email.is_default = true')
            .getCount();
        const allReplacedEmailsCount = await baseEmailQB()
            .andWhere('email.is_replaced = true')
            .getCount();
        const emailsData = {
            allEmailsCount,
            allPersonalEmailsCount,
            allBusinessEmailsCount,
            allDefaultEmailsCount,
            allReplacedEmailsCount,
        };
        const baseJobsQB = () => {
            const qb = this.jobsRepository.createQueryBuilder('jobs');
            applyDateClause(qb, 'jobs.');
            return qb;
        };
        const allJobPostsCount = await baseJobsQB().getCount();
        const allUkJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 1')
            .getCount();
        const allUsJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 2')
            .getCount();
        const allSnrJobPostsCount = await baseJobsQB()
            .andWhere('jobs.sectorId = 2')
            .getCount();
        const allDirectJobPostsCount = await baseJobsQB()
            .andWhere('jobs.sectorId = 1')
            .getCount();
        const allUkSnrJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 1')
            .andWhere('jobs.sectorId = 2')
            .getCount();
        const allUkDirectJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 1')
            .andWhere('jobs.sectorId = 1')
            .getCount();
        const allUsSnrJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 2')
            .andWhere('jobs.sectorId = 2')
            .getCount();
        const allUsDirectJobPostsCount = await baseJobsQB()
            .andWhere('jobs.countryId = 2')
            .andWhere('jobs.sectorId = 1')
            .getCount();
        const startOfDay = new Date();
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date();
        endOfDay.setHours(23, 59, 59, 999);
        const allJobsPostedToday = await this.jobsRepository
            .createQueryBuilder('jobs')
            .where('jobs.job_posting_date BETWEEN :start AND :end', {
            start: startOfDay,
            end: endOfDay,
        })
            .getCount();
        const jobsData = {
            allJobPostsCount,
            allUkJobPostsCount,
            allUsJobPostsCount,
            allSnrJobPostsCount,
            allDirectJobPostsCount,
            allUkSnrJobPostsCount,
            allUkDirectJobPostsCount,
            allUsSnrJobPostsCount,
            allUsDirectJobPostsCount,
            allJobsPostedToday,
        };
        const baseMailBoxQB = () => {
            const qb = this.mailBoxRepository.createQueryBuilder('mail');
            applyDateClause(qb, 'mail.');
            return qb;
        };
        const allMarketingEmailsCount = await baseMailBoxQB().getCount();
        const allInboxMarketingEmailsCount = await baseMailBoxQB()
            .andWhere("mail.type = 'INBOX'")
            .getCount();
        const allSentMarketingEmailsCount = await baseMailBoxQB()
            .andWhere("mail.type = 'SENT'")
            .getCount();
        const allBouncedMarketingEmailsCount = await baseMailBoxQB()
            .andWhere("mail.type = 'BOUNCE'")
            .getCount();
        const allDraftMarketingEmailsCount = await baseMailBoxQB()
            .andWhere("mail.type = 'DRAFT'")
            .getCount();
        const marketingEmailsData = {
            allMarketingEmailsCount,
            allInboxMarketingEmailsCount,
            allSentMarketingEmailsCount,
            allBouncedMarketingEmailsCount,
            allDraftMarketingEmailsCount,
        };
        const scrapperDailyReportsData = await this.scrapperStatsRepository.find();
        return {
            companyData,
            peopleData,
            emailsData,
            jobsData,
            marketingEmailsData,
            scrapperDailyReportsData,
        };
    }
};
exports.LeadsService = LeadsService;
exports.LeadsService = LeadsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(1, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(2, (0, typeorm_1.InjectRepository)(people_assignment_entity_1.PeopleAssignment)),
    __param(3, (0, typeorm_1.InjectRepository)(users_entity_1.Users)),
    __param(4, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(5, (0, typeorm_1.InjectRepository)(country_entity_1.Country)),
    __param(6, (0, typeorm_1.InjectRepository)(sector_entity_1.Sector)),
    __param(7, (0, typeorm_1.InjectRepository)(jobs_entity_1.Jobs)),
    __param(8, (0, typeorm_1.InjectRepository)(mailBox_entity_1.MailBox)),
    __param(9, (0, typeorm_1.InjectRepository)(scrapperStats_entity_1.ScrapperStats)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], LeadsService);
//# sourceMappingURL=leads.service.js.map