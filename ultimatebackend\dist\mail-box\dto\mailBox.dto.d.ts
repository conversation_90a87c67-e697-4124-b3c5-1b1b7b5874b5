import { ProspectStatus } from 'src/people/dto/people.enums';
export declare class MailBoxDto {
    name: string;
    email: string;
    type: string;
    subject?: string;
    message?: string;
    status?: ProspectStatus;
    attachment?: string;
    date?: string;
    sender?: string;
    recipient?: string;
    cc?: string;
    bcc?: string;
    reply_to?: string;
    reason?: string;
    category?: string;
    replacement_email?: string;
    read_by_user?: boolean;
    read_by_system?: boolean;
}
export declare class UpdateMailBoxDto extends MailBoxDto {
    id: number;
}
