"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateMailBoxDto = exports.MailBoxDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const people_enums_1 = require("../../people/dto/people.enums");
class MailBoxDto {
}
exports.MailBoxDto = MailBoxDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the mailbox', example: 'John Doe' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email address of the mailbox',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the mailbox',
        example: 'INBOX',
        enum: [
            'INBOX',
            'SENT',
            'DRAFT',
            'TRASH',
            'SPAM',
            'ARCHIVED',
            'IMPORTANT',
            'STARRED',
            'UNREAD',
            'READ',
            'BOUNCE',
            'REPLIED',
            'FORWARDED',
            'DELETED',
            'BLOCKED',
            'UNSUBSCRIBED',
            'SPAM_REPORT',
            'PHISHING_REPORT',
        ],
    }),
    (0, class_validator_1.IsEnum)([
        'INBOX',
        'SENT',
        'DRAFT',
        'TRASH',
        'SPAM',
        'ARCHIVED',
        'IMPORTANT',
        'STARRED',
        'UNREAD',
        'READ',
        'BOUNCE',
        'REPLIED',
        'FORWARDED',
        'DELETED',
        'BLOCKED',
        'UNSUBSCRIBED',
        'SPAM_REPORT',
        'PHISHING_REPORT',
    ]),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Subject of the email', example: 'Hello World' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message of the email',
        example: 'This is a test message',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the email',
        example: 'sent',
        enum: people_enums_1.ProspectStatus,
        default: people_enums_1.ProspectStatus.CONTACTED,
    }),
    (0, class_validator_1.IsEnum)(people_enums_1.ProspectStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Attachment of the email',
        example: 'attachment.txt',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "attachment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date of the email',
        example: '2023-10-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Sender of the email', example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "sender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient of the email',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "recipient", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'CC of the email', example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "cc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'BCC of the email', example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "bcc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reply to of the email',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "reply_to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for status',
        example: 'User marked as spam',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Category of the email',
        example: 'Auto-Reply',
        enum: ['Auto-Reply', 'Potential Reply', 'Bounce Back', 'Unsubscribe'],
    }),
    (0, class_validator_1.IsEnum)(['Auto-Reply', 'Potential Reply', 'Bounce Back', 'Unsubscribe']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Replacement email address',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], MailBoxDto.prototype, "replacement_email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Read status by user',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], MailBoxDto.prototype, "read_by_user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Read status by system',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], MailBoxDto.prototype, "read_by_system", void 0);
class UpdateMailBoxDto extends MailBoxDto {
}
exports.UpdateMailBoxDto = UpdateMailBoxDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the mailbox', example: 1 }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateMailBoxDto.prototype, "id", void 0);
//# sourceMappingURL=mailBox.dto.js.map