{"version": 3, "file": "mailBox.dto.js", "sourceRoot": "", "sources": ["../../../src/mail-box/dto/mailBox.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAMyB;AACzB,6CAA8C;AAC9C,gEAA6D;AAE7D,MAAa,UAAU;CAyKtB;AAzKD,gCAyKC;AArKC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACxE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wCACA;AAQb;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,iBAAiB;KAC3B,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;yCACC;AA+Cd;IA7CC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE;YACJ,OAAO;YACP,MAAM;YACN,OAAO;YACP,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,SAAS;YACT,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,cAAc;YACd,aAAa;YACb,iBAAiB;SAClB;KACF,CAAC;IACD,IAAA,wBAAM,EAAC;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW;QACX,SAAS;QACT,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,cAAc;QACd,aAAa;QACb,iBAAiB;KAClB,CAAC;IACD,IAAA,4BAAU,GAAE;;wCACA;AAKb;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;IAC5E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACI;AAQjB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,wBAAwB;KAClC,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACI;AAUjB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,MAAM;QACf,IAAI,EAAE,6BAAc;QACpB,OAAO,EAAE,6BAAc,CAAC,SAAS;KAClC,CAAC;IACD,IAAA,wBAAM,EAAC,6BAAc,CAAC;IACtB,IAAA,4BAAU,GAAE;;0CACW;AAQxB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,yBAAyB;QACtC,OAAO,EAAE,gBAAgB;KAC1B,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACO;AAQpB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,0BAA0B;KACpC,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wCACC;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;IAC7E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACG;AAQhB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,iBAAiB;KAC3B,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6CACM;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC1E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;sCACD;AAKZ;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;IAC5E,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;uCACA;AAQb;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4CACK;AAQlB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,qBAAqB;KAC/B,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACG;AAShB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,CAAC;KACtE,CAAC;IACD,IAAA,wBAAM,EAAC,CAAC,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IACvE,IAAA,4BAAU,GAAE;;4CACK;AAQlB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2BAA2B;QACxC,OAAO,EAAE,yBAAyB;KACnC,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;qDACc;AAO3B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,KAAK;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;;gDACU;AAOvB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE,KAAK;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;;kDACY;AAG3B,MAAa,gBAAiB,SAAQ,UAAU;CAI/C;AAJD,4CAIC;AADC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC7D,IAAA,4BAAU,GAAE;;4CACF"}