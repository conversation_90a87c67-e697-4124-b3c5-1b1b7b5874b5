import { MailBoxService } from './mail-box.service';
import { MailBoxDto } from './dto/mailBox.dto';
export declare class MailBoxController {
    private readonly mailBoxService;
    constructor(mailBoxService: MailBoxService);
    create(mailBoxDto: MailBoxDto): Promise<import("./mailBox.entity").MailBox>;
    findAll(page: number, pageSize: number, searchString: string): Promise<import("./mailBox.entity").MailBox[]>;
    updateAssignEmailToBd(id: number, assigneeId: string): Promise<import("./mailBox.entity").MailBox>;
    findByEmail(email: string, type: string, category: string, pageSize: string): Promise<import("./mailBox.entity").MailBox[]>;
    updateProspectStatus(email: string, status: string): Promise<import("../people/people.entity").People>;
    findById(id: number): Promise<import("./mailBox.entity").MailBox>;
    getMyTrialLeads(assigneeId: string, pageSize: string): Promise<import("./mailBox.entity").MailBox[]>;
    update(id: number, mailBoxDto: MailBoxDto): Promise<import("./mailBox.entity").MailBox>;
    updateReadStatus(id: number): Promise<import("./mailBox.entity").MailBox>;
    delete(id: number): Promise<void>;
    findByType(type: string): Promise<import("./mailBox.entity").MailBox[]>;
    deleteAll(): Promise<void>;
    saveAsDraft(email: string, type: string, category: string, sender: string, recipient: string, subject: string, body: string, attachments: string[], cc: string[], bcc: string[], reply_to: string): Promise<any>;
    scheduleEmail(email: string, type: string, sender: string, recipient: string, subject?: string, body?: string, attachments?: string[], cc?: string[], bcc?: string[], reply_to?: string, schedule_date?: Date): Promise<any>;
}
