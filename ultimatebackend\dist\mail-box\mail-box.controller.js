"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailBoxController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const mail_box_service_1 = require("./mail-box.service");
const mailBox_dto_1 = require("./dto/mailBox.dto");
let MailBoxController = class MailBoxController {
    constructor(mailBoxService) {
        this.mailBoxService = mailBoxService;
    }
    async create(mailBoxDto) {
        return await this.mailBoxService.create(mailBoxDto);
    }
    async findAll(page, pageSize, searchString) {
        return await this.mailBoxService.findAll(page, pageSize, searchString);
    }
    async updateAssignEmailToBd(id, assigneeId) {
        if (!id || !assigneeId) {
            throw new Error('id and assigneeId are required');
        }
        return await this.mailBoxService.updateAssignEmailToBd(id, assigneeId);
    }
    async findByEmail(email, type, category, pageSize) {
        return await this.mailBoxService.findByEmail(email, type, category, pageSize);
    }
    async updateProspectStatus(email, status) {
        if (!email || !status) {
            throw new Error('email and status are required');
        }
        return await this.mailBoxService.updateProspectStatus(email, status);
    }
    async findById(id) {
        return await this.mailBoxService.findById(id);
    }
    async getMyTrialLeads(assigneeId, pageSize) {
        return await this.mailBoxService.getMyTrialLeads(assigneeId, pageSize);
    }
    async update(id, mailBoxDto) {
        return await this.mailBoxService.update(id, mailBoxDto);
    }
    async updateReadStatus(id) {
        return await this.mailBoxService.updateReadStatusById(id);
    }
    async delete(id) {
        return await this.mailBoxService.delete(id);
    }
    async findByType(type) {
        return await this.mailBoxService.findByType(type);
    }
    async deleteAll() {
        return await this.mailBoxService.deleteAll();
    }
    async saveAsDraft(email, type, category, sender, recipient, subject, body, attachments, cc, bcc, reply_to) {
        if (!email || !subject || !body) {
            throw new Error('email, subject and body are required');
        }
        return await this.mailBoxService.saveAsDraft(email, type, sender, recipient, subject, body, attachments, cc, bcc, reply_to);
    }
    async scheduleEmail(email, type, sender, recipient, subject, body, attachments, cc, bcc, reply_to, schedule_date) {
        if (!email || !type || !sender || !recipient || !schedule_date) {
            throw new Error('email, type, sender, recipient, and schedule_date are required');
        }
        return await this.mailBoxService.scheduleEmail(email, type, sender, recipient, subject, body, attachments, cc, bcc, reply_to, schedule_date);
    }
};
exports.MailBoxController = MailBoxController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Get all mailboxes',
    }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [mailBox_dto_1.MailBoxDto]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Get all mailboxes',
    }),
    (0, common_1.Get)('all'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('searchString')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "findAll", null);
__decorate([
    (0, common_1.Put)('update-assign-email-to-bd'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update assign email to bd',
    }),
    __param(0, (0, common_1.Body)('id')),
    __param(1, (0, common_1.Body)('assigneeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "updateAssignEmailToBd", null);
__decorate([
    (0, common_1.Get)('email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get mailbox by email and type',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'email',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'type',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'category',
        required: false,
        type: String,
    }),
    __param(0, (0, common_1.Query)('email')),
    __param(1, (0, common_1.Query)('type')),
    __param(2, (0, common_1.Query)('category')),
    __param(3, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "findByEmail", null);
__decorate([
    (0, common_1.Put)('update-prospect-status'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update prospect status',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Update prospect status',
        schema: {
            type: 'object',
            properties: {
                email: { type: 'string', description: 'Email address' },
                status: { type: 'string', description: 'New status' },
            },
            required: ['email', 'status'],
        },
    }),
    __param(0, (0, common_1.Body)('email')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "updateProspectStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Get mailbox by id',
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "findById", null);
__decorate([
    (0, common_1.Get)('my-trial-leads/:assigneeId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get my trial leads',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'assigneeId',
        required: false,
        type: String,
    }),
    __param(0, (0, common_1.Param)('assigneeId')),
    __param(1, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "getMyTrialLeads", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Update mailbox by id',
    }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, mailBox_dto_1.MailBoxDto]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Update ReadStatus by id',
    }),
    (0, common_1.Put)('update-read-status/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "updateReadStatus", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Delete mailbox by id',
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "delete", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Get mailbox by type',
    }),
    (0, common_1.Get)('type/:type'),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "findByType", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Delete all mailboxes',
    }),
    (0, common_1.Delete)('delete-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "deleteAll", null);
__decorate([
    (0, common_1.Post)('save-as-draft'),
    (0, swagger_1.ApiOperation)({
        summary: 'Save email as draft',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Save email as draft',
        schema: {
            type: 'object',
            properties: {
                email: { type: 'string', description: 'Email address' },
                type: { type: 'string', description: 'Type of email' },
                category: { type: 'string', description: 'Category of email' },
                sender: { type: 'string', description: 'Sender email address' },
                recipient: { type: 'string', description: 'Recipient email address' },
                subject: { type: 'string', description: 'Email subject' },
                body: { type: 'string', description: 'Email body' },
                attachments: {
                    type: 'array',
                    items: { type: 'string', description: 'Attachment URL' },
                },
                cc: {
                    type: 'array',
                    items: { type: 'string', description: 'CC email addresses' },
                },
                bcc: {
                    type: 'array',
                    items: { type: 'string', description: 'BCC email addresses' },
                },
                reply_to: {
                    type: 'array',
                    items: { type: 'string', description: 'Reply-to email addresses' },
                },
            },
            required: ['email', 'subject', 'body'],
        },
    }),
    __param(0, (0, common_1.Body)('email')),
    __param(1, (0, common_1.Body)('type')),
    __param(2, (0, common_1.Body)('category')),
    __param(3, (0, common_1.Body)('sender')),
    __param(4, (0, common_1.Body)('recipient')),
    __param(5, (0, common_1.Body)('subject')),
    __param(6, (0, common_1.Body)('body')),
    __param(7, (0, common_1.Body)('attachments')),
    __param(8, (0, common_1.Body)('cc')),
    __param(9, (0, common_1.Body)('bcc')),
    __param(10, (0, common_1.Body)('reply_to')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, Array, Array, Array, String]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "saveAsDraft", null);
__decorate([
    (0, common_1.Post)('schedule-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Schedule an email',
    }),
    (0, swagger_1.ApiBody)({
        description: 'Schedule an email',
        schema: {
            type: 'object',
            properties: {
                email: { type: 'string', description: 'Email address' },
                type: { type: 'string', description: 'Type of email' },
                sender: { type: 'string', description: 'Sender email address' },
                recipient: { type: 'string', description: 'Recipient email address' },
                subject: { type: 'string', description: 'Email subject' },
                body: { type: 'string', description: 'Email body' },
                attachments: {
                    type: 'array',
                    items: { type: 'string', description: 'Attachment URL' },
                },
                cc: {
                    type: 'array',
                    items: { type: 'string', description: 'CC email addresses' },
                },
                bcc: {
                    type: 'array',
                    items: { type: 'string', description: 'BCC email addresses' },
                },
                reply_to: {
                    type: 'array',
                    items: { type: 'string', description: 'Reply-to email addresses' },
                },
                schedule_date: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Scheduled date and time',
                },
            },
            required: ['email', 'type', 'sender', 'recipient', 'schedule_date'],
        },
    }),
    __param(0, (0, common_1.Body)('email')),
    __param(1, (0, common_1.Body)('type')),
    __param(2, (0, common_1.Body)('sender')),
    __param(3, (0, common_1.Body)('recipient')),
    __param(4, (0, common_1.Body)('subject')),
    __param(5, (0, common_1.Body)('body')),
    __param(6, (0, common_1.Body)('attachments')),
    __param(7, (0, common_1.Body)('cc')),
    __param(8, (0, common_1.Body)('bcc')),
    __param(9, (0, common_1.Body)('reply_to')),
    __param(10, (0, common_1.Body)('schedule_date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, Array, Array, Array, String, Date]),
    __metadata("design:returntype", Promise)
], MailBoxController.prototype, "scheduleEmail", null);
exports.MailBoxController = MailBoxController = __decorate([
    (0, swagger_1.ApiTags)('mail-box'),
    (0, common_1.Controller)('mail-box'),
    __metadata("design:paramtypes", [mail_box_service_1.MailBoxService])
], MailBoxController);
//# sourceMappingURL=mail-box.controller.js.map