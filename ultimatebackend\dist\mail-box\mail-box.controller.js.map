{"version": 3, "file": "mail-box.controller.js", "sourceRoot": "", "sources": ["../../src/mail-box/mail-box.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CASwB;AACxB,6CAA2E;AAC3E,yDAAoD;AACpD,mDAA+C;AAIxC,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAA6B,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;IAAG,CAAC;IAMzD,AAAN,KAAK,CAAC,MAAM,CAAS,UAAsB;QACzC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACtD,CAAC;IAMK,AAAN,KAAK,CAAC,OAAO,CACI,IAAY,EACR,QAAgB,EACZ,YAAoB;QAE3C,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAOK,AAAN,KAAK,CAAC,qBAAqB,CACb,EAAU,EACF,UAAkB;QAEtC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACzE,CAAC;IAqBK,AAAN,KAAK,CAAC,WAAW,CACC,KAAa,EACd,IAAY,EACR,QAAgB,EAChB,QAAgB;QAEnC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAC1C,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,QAAQ,CACT,CAAC;IACJ,CAAC;IAkBK,AAAN,KAAK,CAAC,oBAAoB,CACT,KAAa,EACZ,MAAc;QAE9B,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvE,CAAC;IAMK,AAAN,KAAK,CAAC,QAAQ,CAAc,EAAU;QACpC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;IAYK,AAAN,KAAK,CAAC,eAAe,CACE,UAAkB,EACpB,QAAgB;QAEnC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;IAKK,AAAN,KAAK,CAAC,MAAM,CAAc,EAAU,EAAU,UAAsB;QAClE,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAMK,AAAN,KAAK,CAAC,gBAAgB,CAAc,EAAU;QAC5C,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC;IAMK,AAAN,KAAK,CAAC,MAAM,CAAc,EAAU;QAClC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9C,CAAC;IAMK,AAAN,KAAK,CAAC,UAAU,CAAgB,IAAY;QAC1C,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAMK,AAAN,KAAK,CAAC,SAAS;QACb,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;IAC/C,CAAC;IAuCK,AAAN,KAAK,CAAC,WAAW,CACA,KAAa,EACd,IAAY,EACR,QAAgB,EAClB,MAAc,EACX,SAAiB,EACnB,OAAe,EAClB,IAAY,EACL,WAAqB,EAC9B,EAAY,EACX,GAAa,EACR,QAAgB;QAElC,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAC1C,KAAK,EACL,IAAI,EACJ,MAAM,EACN,SAAS,EACT,OAAO,EACP,IAAI,EACJ,WAAW,EACX,EAAE,EACF,GAAG,EACH,QAAQ,CACT,CAAC;IACJ,CAAC;IA2CK,AAAN,KAAK,CAAC,aAAa,CACF,KAAa,EACd,IAAY,EACV,MAAc,EACX,SAAiB,EACnB,OAAgB,EACnB,IAAa,EACN,WAAsB,EAC/B,EAAa,EACZ,GAAc,EACT,QAAiB,EACZ,aAAoB;QAE3C,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CACb,gEAAgE,CACjE,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAC5C,KAAK,EACL,IAAI,EACJ,MAAM,EACN,SAAS,EACT,OAAO,EACP,IAAI,EACJ,WAAW,EACX,EAAE,EACF,GAAG,EACH,QAAQ,EACR,aAAa,CACd,CAAC;IACJ,CAAC;CACF,CAAA;AA7SY,8CAAiB;AAOtB;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mBAAmB;KAC7B,CAAC;IACD,IAAA,aAAI,EAAC,QAAQ,CAAC;IACD,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAa,wBAAU;;+CAE1C;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mBAAmB;KAC7B,CAAC;IACD,IAAA,YAAG,EAAC,KAAK,CAAC;IAER,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,cAAc,CAAC,CAAA;;;;gDAGvB;AAOK;IAJL,IAAA,YAAG,EAAC,2BAA2B,CAAC;IAChC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,2BAA2B;KACrC,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,IAAI,CAAC,CAAA;IACV,WAAA,IAAA,aAAI,EAAC,YAAY,CAAC,CAAA;;;;8DAMpB;AAqBK;IAnBL,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,+BAA+B;KACzC,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;KACb,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;KACb,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;IACd,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;IACb,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;oDAQnB;AAkBK;IAfL,IAAA,YAAG,EAAC,wBAAwB,CAAC;IAC7B,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,wBAAwB;KAClC,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,WAAW,EAAE,wBAAwB;QACrC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACvD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;aACtD;YACD,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC9B;KACF,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;IACb,WAAA,IAAA,aAAI,EAAC,QAAQ,CAAC,CAAA;;;;6DAMhB;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mBAAmB;KAC7B,CAAC;IACD,IAAA,YAAG,EAAC,KAAK,CAAC;IACK,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;iDAE1B;AAYK;IATL,IAAA,YAAG,EAAC,4BAA4B,CAAC;IACjC,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,oBAAoB;KAC9B,CAAC;IACD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;KACb,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;IACnB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;;;;wDAGnB;AAKK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,YAAG,EAAC,KAAK,CAAC;IACG,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IAAc,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAa,wBAAU;;+CAEnE;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,yBAAyB;KACnC,CAAC;IACD,IAAA,YAAG,EAAC,wBAAwB,CAAC;IACN,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;yDAElC;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,eAAM,EAAC,KAAK,CAAC;IACA,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;+CAExB;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,qBAAqB;KAC/B,CAAC;IACD,IAAA,YAAG,EAAC,YAAY,CAAC;IACA,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;;;;mDAE9B;AAMK;IAJL,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,eAAM,EAAC,YAAY,CAAC;;;;kDAGpB;AAuCK;IApCL,IAAA,aAAI,EAAC,eAAe,CAAC;IACrB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,qBAAqB;KAC/B,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,WAAW,EAAE,qBAAqB;QAClC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACvD,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACtD,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE;gBAC9D,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAE;gBAC/D,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;gBACrE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACzD,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;gBACnD,WAAW,EAAE;oBACX,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE;iBACzD;gBACD,EAAE,EAAE;oBACF,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oBAAoB,EAAE;iBAC7D;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,qBAAqB,EAAE;iBAC9D;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBACnE;aACF;YACD,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;SACvC;KACF,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;IACb,WAAA,IAAA,aAAI,EAAC,MAAM,CAAC,CAAA;IACZ,WAAA,IAAA,aAAI,EAAC,UAAU,CAAC,CAAA;IAChB,WAAA,IAAA,aAAI,EAAC,QAAQ,CAAC,CAAA;IACd,WAAA,IAAA,aAAI,EAAC,WAAW,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,EAAC,SAAS,CAAC,CAAA;IACf,WAAA,IAAA,aAAI,EAAC,MAAM,CAAC,CAAA;IACZ,WAAA,IAAA,aAAI,EAAC,aAAa,CAAC,CAAA;IACnB,WAAA,IAAA,aAAI,EAAC,IAAI,CAAC,CAAA;IACV,WAAA,IAAA,aAAI,EAAC,KAAK,CAAC,CAAA;IACX,YAAA,IAAA,aAAI,EAAC,UAAU,CAAC,CAAA;;;;oDAiBlB;AA2CK;IAxCL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IACtB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mBAAmB;KAC7B,CAAC;IACD,IAAA,iBAAO,EAAC;QACP,WAAW,EAAE,mBAAmB;QAChC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACvD,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACtD,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,EAAE;gBAC/D,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,yBAAyB,EAAE;gBACrE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE;gBACzD,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;gBACnD,WAAW,EAAE;oBACX,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE;iBACzD;gBACD,EAAE,EAAE;oBACF,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oBAAoB,EAAE;iBAC7D;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,qBAAqB,EAAE;iBAC9D;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0BAA0B,EAAE;iBACnE;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,WAAW;oBACnB,WAAW,EAAE,yBAAyB;iBACvC;aACF;YACD,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,CAAC;SACpE;KACF,CAAC;IAEC,WAAA,IAAA,aAAI,EAAC,OAAO,CAAC,CAAA;IACb,WAAA,IAAA,aAAI,EAAC,MAAM,CAAC,CAAA;IACZ,WAAA,IAAA,aAAI,EAAC,QAAQ,CAAC,CAAA;IACd,WAAA,IAAA,aAAI,EAAC,WAAW,CAAC,CAAA;IACjB,WAAA,IAAA,aAAI,EAAC,SAAS,CAAC,CAAA;IACf,WAAA,IAAA,aAAI,EAAC,MAAM,CAAC,CAAA;IACZ,WAAA,IAAA,aAAI,EAAC,aAAa,CAAC,CAAA;IACnB,WAAA,IAAA,aAAI,EAAC,IAAI,CAAC,CAAA;IACV,WAAA,IAAA,aAAI,EAAC,KAAK,CAAC,CAAA;IACX,WAAA,IAAA,aAAI,EAAC,UAAU,CAAC,CAAA;IAChB,YAAA,IAAA,aAAI,EAAC,eAAe,CAAC,CAAA;;kHAAiB,IAAI;;sDAoB5C;4BA5SU,iBAAiB;IAF7B,IAAA,iBAAO,EAAC,UAAU,CAAC;IACnB,IAAA,mBAAU,EAAC,UAAU,CAAC;qCAEwB,iCAAc;GADhD,iBAAiB,CA6S7B"}