"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailBoxModule = void 0;
const common_1 = require("@nestjs/common");
const mail_box_service_1 = require("./mail-box.service");
const mail_box_controller_1 = require("./mail-box.controller");
const typeorm_1 = require("@nestjs/typeorm");
const mailBox_entity_1 = require("./mailBox.entity");
const users_entity_1 = require("../users/users.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
let MailBoxModule = class MailBoxModule {
};
exports.MailBoxModule = MailBoxModule;
exports.MailBoxModule = MailBoxModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([mailBox_entity_1.MailBox, users_entity_1.Users, people_entity_1.People, emails_entity_1.PersonEmail])],
        providers: [mail_box_service_1.MailBoxService],
        controllers: [mail_box_controller_1.MailBoxController],
    })
], MailBoxModule);
//# sourceMappingURL=mail-box.module.js.map