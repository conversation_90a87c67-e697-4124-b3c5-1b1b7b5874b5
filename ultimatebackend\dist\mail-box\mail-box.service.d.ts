import { MailBox } from './mailBox.entity';
import { Repository } from 'typeorm';
import { MailBoxDto } from './dto/mailBox.dto';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
export declare class MailBoxService {
    private readonly mailBoxRepository;
    private readonly userRepository;
    private readonly peopleRepository;
    private readonly personEmailRepository;
    constructor(mailBoxRepository: Repository<MailBox>, userRepository: Repository<Users>, peopleRepository: Repository<People>, personEmailRepository: Repository<PersonEmail>);
    create(mailBox: MailBoxDto): Promise<MailBox>;
    findAll(page: number, pageSize: number, searchString: string): Promise<MailBox[]>;
    findById(id: number): Promise<MailBox>;
    update(id: number, mailBox: MailBoxDto): Promise<MailBox>;
    delete(id: number): Promise<void>;
    deleteAll(): Promise<void>;
    findByType(type: string): Promise<MailBox[]>;
    findByEmail(email?: string, type?: string, category?: string, pageSize?: string): Promise<MailBox[]>;
    findByDate(date: string): Promise<MailBox[]>;
    findBySender(sender: string): Promise<MailBox[]>;
    findByRecipient(recipient: string): Promise<MailBox[]>;
    findBySenders(senders: string[]): Promise<MailBox[]>;
    updateReadStatusById(id: number): Promise<MailBox>;
    updateAssignEmailToBd(id: number, assigneeId: string): Promise<MailBox>;
    getMyTrialLeads(userId: string, pageSize?: string): Promise<MailBox[]>;
    updateProspectStatus(email: string, trialStatus: string): Promise<People>;
    saveAsDraft(email: string, type: string, sender: string, recipient: string, subject?: string, body?: string, attachments?: string[], cc?: string[], bcc?: string[], reply_to?: string): Promise<MailBox>;
    findDraftsByEmail(email: string): Promise<MailBox[]>;
    scheduleEmail(email: string, type: string, sender: string, recipient: string, subject?: string, body?: string, attachments?: string[], cc?: string[], bcc?: string[], reply_to?: string, schedule_date?: Date): Promise<MailBox>;
    findScheduledEmailsByEmail(email: string): Promise<MailBox[]>;
}
