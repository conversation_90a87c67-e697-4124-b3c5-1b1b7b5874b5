"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailBoxService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const mailBox_entity_1 = require("./mailBox.entity");
const typeorm_2 = require("typeorm");
const users_entity_1 = require("../users/users.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
let MailBoxService = class MailBoxService {
    constructor(mailBoxRepository, userRepository, peopleRepository, personEmailRepository) {
        this.mailBoxRepository = mailBoxRepository;
        this.userRepository = userRepository;
        this.peopleRepository = peopleRepository;
        this.personEmailRepository = personEmailRepository;
    }
    async create(mailBox) {
        try {
            const newMailBox = this.mailBoxRepository.create({
                ...mailBox,
                type: mailBox.type,
            });
            return await this.mailBoxRepository.save(newMailBox);
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error creating mailbox');
        }
    }
    async findAll(page, pageSize, searchString) {
        const query = this.mailBoxRepository.createQueryBuilder('mailbox');
        if (searchString) {
            query.where('mailbox.name LIKE :name', { name: `%${searchString}%` });
        }
        query.skip((page - 1) * pageSize).take(pageSize);
        return await query.getMany();
    }
    async findById(id) {
        const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
        if (!mailBox) {
            throw new common_1.NotFoundException('Mailbox not found');
        }
        return mailBox;
    }
    async update(id, mailBox) {
        const existingMailBox = await this.findById(id);
        if (!existingMailBox) {
            throw new common_1.NotFoundException('Mailbox not found');
        }
        const updatedMailBox = {
            ...existingMailBox,
            ...mailBox,
            type: mailBox.type,
        };
        return await this.mailBoxRepository.save(updatedMailBox);
    }
    async delete(id) {
        const mailBox = await this.findById(id);
        if (!mailBox) {
            throw new common_1.NotFoundException('Mailbox not found');
        }
        await this.mailBoxRepository.remove(mailBox);
    }
    async deleteAll() {
        const mailBoxes = await this.mailBoxRepository.find();
        if (mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found');
        }
        await this.mailBoxRepository.remove(mailBoxes);
    }
    async findByType(type) {
        const mailBoxes = await this.mailBoxRepository.find({
            where: { type: type },
        });
        if (!mailBoxes || mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found for this type');
        }
        return mailBoxes;
    }
    async findByEmail(email, type, category, pageSize) {
        const whereCondition = {};
        if (type) {
            whereCondition.type = type;
        }
        if (email) {
            const searchField = type?.toUpperCase() === 'INBOX'
                ? 'recipient'
                : type?.toUpperCase() === 'SENT'
                    ? 'sender'
                    : 'email';
            whereCondition[searchField] = email;
        }
        if (category) {
            whereCondition.category = category;
        }
        const findOptions = {
            where: whereCondition,
            order: { created_at: 'DESC' },
        };
        if (pageSize) {
            findOptions.take = parseInt(pageSize);
            findOptions.skip = 0;
        }
        const mailBoxes = await this.mailBoxRepository.find(findOptions);
        if (!mailBoxes || mailBoxes.length === 0) {
            return [];
        }
        return mailBoxes;
    }
    async findByDate(date) {
        const mailBoxes = await this.mailBoxRepository.find({ where: { date } });
        if (!mailBoxes || mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found for this date');
        }
        return mailBoxes;
    }
    async findBySender(sender) {
        const mailBoxes = await this.mailBoxRepository.find({ where: { sender } });
        if (!mailBoxes || mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found for this sender');
        }
        return mailBoxes;
    }
    async findByRecipient(recipient) {
        const mailBoxes = await this.mailBoxRepository.find({
            where: { recipient },
        });
        if (!mailBoxes || mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found for this recipient');
        }
        return mailBoxes;
    }
    async findBySenders(senders) {
        const mailBoxes = await this.mailBoxRepository.find({
            where: { sender: (0, typeorm_2.In)(senders) },
        });
        if (!mailBoxes || mailBoxes.length === 0) {
            throw new common_1.NotFoundException('No mailboxes found for these senders');
        }
        return mailBoxes;
    }
    async updateReadStatusById(id) {
        const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
        if (!mailBox) {
            throw new common_1.NotFoundException('Mailbox not found');
        }
        mailBox.read_by_user = true;
        return await this.mailBoxRepository.save(mailBox);
    }
    async updateAssignEmailToBd(id, assigneeId) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: assigneeId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
            if (!mailBox) {
                throw new common_1.NotFoundException('Mailbox not found');
            }
            mailBox.assigneeId = assigneeId;
            mailBox.assignee = user;
            const updatedMailBox = await this.mailBoxRepository.save(mailBox);
            return updatedMailBox;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error assigning email to user');
        }
    }
    async getMyTrialLeads(userId, pageSize) {
        try {
            const findOptions = {
                where: { assigneeId: userId },
                order: { created_at: 'DESC' },
            };
            if (pageSize) {
                findOptions.take = parseInt(pageSize);
                findOptions.skip = 0;
            }
            const mailBoxes = await this.mailBoxRepository.find(findOptions);
            if (!mailBoxes || mailBoxes.length === 0) {
                return [];
            }
            return mailBoxes;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error fetching trial leads');
        }
    }
    async updateProspectStatus(email, trialStatus) {
        try {
            const personEmail = await this.personEmailRepository.findOne({
                where: { email },
            });
            if (!personEmail) {
                throw new common_1.NotFoundException('Email not found');
            }
            const person = await this.peopleRepository.findOne({
                where: { id: personEmail.personId },
            });
            if (!person) {
                throw new common_1.NotFoundException('Person not found');
            }
            person.prospect_status = trialStatus;
            const updatedPerson = await this.peopleRepository.save(person);
            return updatedPerson;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error updating trial status');
        }
    }
    async saveAsDraft(email, type, sender, recipient, subject, body, attachments, cc, bcc, reply_to) {
        try {
            const mailBox = this.mailBoxRepository.create({
                email,
                type: type,
                sender,
                recipient,
                subject,
                message: body,
                attachment: attachments ? attachments.join(',') : null,
                cc: cc ? cc.join(',') : null,
                bcc: bcc ? bcc.join(',') : null,
                reply_to,
            });
            return await this.mailBoxRepository.save(mailBox);
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error saving draft email');
        }
    }
    async findDraftsByEmail(email) {
        try {
            const mailBoxes = await this.mailBoxRepository.find({
                where: { email, type: 'DRAFT' },
            });
            if (!mailBoxes || mailBoxes.length === 0) {
                return [];
            }
            return mailBoxes;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error fetching draft emails');
        }
    }
    async scheduleEmail(email, type, sender, recipient, subject, body, attachments, cc, bcc, reply_to, schedule_date) {
        try {
            const mailBox = this.mailBoxRepository.create({
                email,
                type: type,
                sender,
                recipient,
                subject,
                message: body,
                attachment: attachments ? attachments.join(',') : null,
                cc: cc ? cc.join(',') : null,
                bcc: bcc ? bcc.join(',') : null,
                reply_to,
                schedule_date,
            });
            return await this.mailBoxRepository.save(mailBox);
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error scheduling email');
        }
    }
    async findScheduledEmailsByEmail(email) {
        try {
            const mailBoxes = await this.mailBoxRepository.find({
                where: { email, type: 'SCHEDULED' },
            });
            if (!mailBoxes || mailBoxes.length === 0) {
                return [];
            }
            return mailBoxes;
        }
        catch (error) {
            console.log(error);
            throw new common_1.InternalServerErrorException('Error fetching scheduled emails');
        }
    }
};
exports.MailBoxService = MailBoxService;
exports.MailBoxService = MailBoxService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(mailBox_entity_1.MailBox)),
    __param(1, (0, typeorm_1.InjectRepository)(users_entity_1.Users)),
    __param(2, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(3, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], MailBoxService);
//# sourceMappingURL=mail-box.service.js.map