export declare class MessagesService {
    constructor();
    getOrCreateChat(from: string, to: string): Promise<{
        lastInboundTimestamp: any;
    }>;
    saveMessage(from: string, to: string, message: any): Promise<void>;
    getMessageStatus(messageSid: string): Promise<string>;
    saveSentWhatsAppMessage(result: any, to: string, body: string, mediaUrl?: string, mediaContentType?: string): Promise<{
        messageSid: any;
        status: any;
    }>;
    saveReceivedWhatsAppMessage(data: any): Promise<void>;
    getMediaContent(mediaSid: string): Promise<string>;
    getWhatsAppMessageBySid(messageSid: string): Promise<{
        messageSid: string;
        from: string;
        to: string;
        body: string;
        status: string;
    }>;
    getWhatsAppMessagesByPhoneNumber(phoneNumber: string): Promise<{
        messageSid: string;
        from: string;
        to: string;
        body: string;
        status: string;
    }[]>;
}
