"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesService = void 0;
const common_1 = require("@nestjs/common");
let MessagesService = class MessagesService {
    constructor() { }
    async getOrCreateChat(from, to) {
        return { lastInboundTimestamp: null };
    }
    async saveMessage(from, to, message) {
        console.log(`Message saved from ${from} to ${to}:`, message);
    }
    async getMessageStatus(messageSid) {
        return 'delivered';
    }
    async saveSentWhatsAppMessage(result, to, body, mediaUrl, mediaContentType) {
        console.log(`WhatsApp message sent to ${to}:`, { result, body, mediaUrl, mediaContentType });
        return { messageSid: result.sid, status: result.status };
    }
    async saveReceivedWhatsAppMessage(data) {
        console.log('WhatsApp message received:', data);
    }
    async getMediaContent(mediaSid) {
        console.log(`Fetching media content for SID: ${mediaSid}`);
        return 'https://example.com/media';
    }
    async getWhatsAppMessageBySid(messageSid) {
        console.log(`Fetching WhatsApp message by SID: ${messageSid}`);
        return { messageSid, from: '+1234567890', to: '+0987654321', body: 'Hello!', status: 'delivered' };
    }
    async getWhatsAppMessagesByPhoneNumber(phoneNumber) {
        console.log(`Fetching WhatsApp messages for phone number: ${phoneNumber}`);
        return [{ messageSid: 'SM1234567890', from: phoneNumber, to: '+0987654321', body: 'Hello!', status: 'delivered' }];
    }
};
exports.MessagesService = MessagesService;
exports.MessagesService = MessagesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], MessagesService);
//# sourceMappingURL=messages.service.js.map