export declare class AddEmailFromSpreadsheetDto {
    userId: string;
    businessEmails: Array<{
        id: number;
        email: string;
        is_default: boolean;
        is_replaced: boolean;
        isReplacedBy: number;
        person_id: number;
        websiteLink: string;
    }>;
}
export declare class AddReplacementEmailsDto {
    userId: string;
    person_id: string;
    replaced_emails: Array<{
        id: number;
        email: string;
        is_default: boolean;
        is_replaced: boolean;
        isReplacedBy: number;
        personId: number;
        websiteLink: string;
    }>;
}
export declare class MarkAsEmailNotFoundDto {
    userId: string;
    persons: number[];
}
