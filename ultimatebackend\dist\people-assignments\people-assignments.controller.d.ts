import { PeopleAssignmentsService } from './people-assignments.service';
import { CreatePeopleAssignmentDto } from './dto/create-people-assignment.dto';
import { UpdatePeopleAssignmentDto } from './dto/update-people-assignment.dto';
import { AssignPersonsToUserDto, GetAssignedPersonsDto } from './dto/get-assigned-persons.dto';
import { AddEmailFromSpreadsheetDto, AddReplacementEmailsDto, MarkAsEmailNotFoundDto } from './dto/add-email-from-spreadsheet.dto';
export declare class PeopleAssignmentsController {
    private readonly peopleAssignmentsService;
    constructor(peopleAssignmentsService: PeopleAssignmentsService);
    create(createPeopleAssignmentDto: CreatePeopleAssignmentDto): string;
    addReplacementEmails(data: AddReplacementEmailsDto): Promise<void>;
    addEmailsFromSpreadSheet(data: AddEmailFromSpreadsheetDto): Promise<{
        message: string;
        data: any[];
        addedEmails: number;
        existingEmails: number;
        invalidEmails: number;
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    } | {
        message: string;
        data: any[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
        invalidEmails?: undefined;
        existingEmails?: undefined;
    } | {
        message: string;
        data: any[];
        invalidEmails: {
            id: number;
            email: string;
            is_default: boolean;
            is_replaced: boolean;
            isReplacedBy: number;
            person_id: number;
            websiteLink: string;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
        existingEmails?: undefined;
    } | {
        message: string;
        data: any[];
        invalidEmails: {
            id: number;
            email: string;
            is_default: boolean;
            is_replaced: boolean;
            isReplacedBy: number;
            person_id: number;
            websiteLink: string;
        }[];
        existingEmails: number;
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    markAsEmailNotFound(data: MarkAsEmailNotFoundDto): Promise<{
        message: string;
    }>;
    getAssignedPersons(query: GetAssignedPersonsDto): Promise<{
        message: string;
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            company: import("../company/company.entity").Company;
            current_title: string;
            avator: string;
            is_business_email_added: boolean;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: import("./entities/people-assignment.entity").AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: import("./entities/people-assignment.entity").AssignmentType;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: import("./entities/people-assignment.entity").AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: import("./entities/people-assignment.entity").AssignmentType;
            }[];
            businessPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            personalPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            claimed_by: {
                id: string;
                full_name: string;
                email: string;
            };
            replacement_claim: {
                is_replacement: true;
                replacement_date: Date;
            };
            latestJobPost: import("../jobs/jobs.entity").Jobs;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    assignPersons(queryParams: AssignPersonsToUserDto): Promise<{
        message: string;
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            company: import("../company/company.entity").Company;
            current_title: string;
            avator: string;
            is_business_email_added: boolean;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: import("./entities/people-assignment.entity").AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: import("./entities/people-assignment.entity").AssignmentType;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: import("./entities/people-assignment.entity").AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: import("./entities/people-assignment.entity").AssignmentType;
            }[];
            businessPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            personalPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            claimed_by: {
                id: string;
                full_name: string;
                email: string;
            };
            replacement_claim: {
                is_replacement: true;
                replacement_date: Date;
            };
            latestJobPost: import("../jobs/jobs.entity").Jobs;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findAll(): string;
    findOne(id: string): string;
    update(id: string, updatePeopleAssignmentDto: UpdatePeopleAssignmentDto): string;
    remove(id: string): string;
}
