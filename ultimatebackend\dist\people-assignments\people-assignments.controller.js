"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeopleAssignmentsController = void 0;
const common_1 = require("@nestjs/common");
const people_assignments_service_1 = require("./people-assignments.service");
const create_people_assignment_dto_1 = require("./dto/create-people-assignment.dto");
const update_people_assignment_dto_1 = require("./dto/update-people-assignment.dto");
const get_assigned_persons_dto_1 = require("./dto/get-assigned-persons.dto");
const add_email_from_spreadsheet_dto_1 = require("./dto/add-email-from-spreadsheet.dto");
let PeopleAssignmentsController = class PeopleAssignmentsController {
    constructor(peopleAssignmentsService) {
        this.peopleAssignmentsService = peopleAssignmentsService;
    }
    create(createPeopleAssignmentDto) {
        return this.peopleAssignmentsService.create(createPeopleAssignmentDto);
    }
    addReplacementEmails(data) {
        return this.peopleAssignmentsService.addReplacementEmails(data);
    }
    addEmailsFromSpreadSheet(data) {
        return this.peopleAssignmentsService.addEmailsFromSpreadSheet(data);
    }
    markAsEmailNotFound(data) {
        return this.peopleAssignmentsService.markAsEmailNotFound(data);
    }
    async getAssignedPersons(query) {
        return this.peopleAssignmentsService.getAssignedPersonsByUserId(query);
    }
    async assignPersons(queryParams) {
        return this.peopleAssignmentsService.assignPersonsByUserId(queryParams);
    }
    findAll() {
        console.log('I am called in controller in find all');
        return this.peopleAssignmentsService.findAll();
    }
    findOne(id) {
        return this.peopleAssignmentsService.findOne(+id);
    }
    update(id, updatePeopleAssignmentDto) {
        return this.peopleAssignmentsService.update(+id, updatePeopleAssignmentDto);
    }
    remove(id) {
        return this.peopleAssignmentsService.remove(+id);
    }
};
exports.PeopleAssignmentsController = PeopleAssignmentsController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_people_assignment_dto_1.CreatePeopleAssignmentDto]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('addReplacementEmails'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_email_from_spreadsheet_dto_1.AddReplacementEmailsDto]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "addReplacementEmails", null);
__decorate([
    (0, common_1.Post)('addEmailsFromSpreadSheet'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_email_from_spreadsheet_dto_1.AddEmailFromSpreadsheetDto]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "addEmailsFromSpreadSheet", null);
__decorate([
    (0, common_1.Post)('markAsEmailNotFound'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [add_email_from_spreadsheet_dto_1.MarkAsEmailNotFoundDto]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "markAsEmailNotFound", null);
__decorate([
    (0, common_1.Get)('getAssignedPersonsByUserId'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_assigned_persons_dto_1.GetAssignedPersonsDto]),
    __metadata("design:returntype", Promise)
], PeopleAssignmentsController.prototype, "getAssignedPersons", null);
__decorate([
    (0, common_1.Get)('assignPersonsToUser'),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true, whitelist: true })),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_assigned_persons_dto_1.AssignPersonsToUserDto]),
    __metadata("design:returntype", Promise)
], PeopleAssignmentsController.prototype, "assignPersons", null);
__decorate([
    (0, common_1.Get)('all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)('id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_people_assignment_dto_1.UpdatePeopleAssignmentDto]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PeopleAssignmentsController.prototype, "remove", null);
exports.PeopleAssignmentsController = PeopleAssignmentsController = __decorate([
    (0, common_1.Controller)('people-assignments'),
    __metadata("design:paramtypes", [people_assignments_service_1.PeopleAssignmentsService])
], PeopleAssignmentsController);
//# sourceMappingURL=people-assignments.controller.js.map