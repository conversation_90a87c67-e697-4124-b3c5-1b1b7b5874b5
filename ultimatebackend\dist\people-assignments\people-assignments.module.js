"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeopleAssignmentsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const people_assignments_service_1 = require("./people-assignments.service");
const people_assignments_controller_1 = require("./people-assignments.controller");
const people_entity_1 = require("../people/people.entity");
const company_entity_1 = require("../company/company.entity");
const emails_entity_1 = require("../emails/emails.entity");
const phone_entity_1 = require("../phone/phone.entity");
const people_assignment_entity_1 = require("./entities/people-assignment.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const users_entity_1 = require("../users/users.entity");
let PeopleAssignmentsModule = class PeopleAssignmentsModule {
};
exports.PeopleAssignmentsModule = PeopleAssignmentsModule;
exports.PeopleAssignmentsModule = PeopleAssignmentsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                people_entity_1.People,
                company_entity_1.Company,
                emails_entity_1.PersonEmail,
                phone_entity_1.PersonPhone,
                people_assignment_entity_1.PeopleAssignment,
                jobs_entity_1.Jobs,
                users_entity_1.Users,
            ]),
        ],
        controllers: [people_assignments_controller_1.PeopleAssignmentsController],
        providers: [people_assignments_service_1.PeopleAssignmentsService],
    })
], PeopleAssignmentsModule);
//# sourceMappingURL=people-assignments.module.js.map