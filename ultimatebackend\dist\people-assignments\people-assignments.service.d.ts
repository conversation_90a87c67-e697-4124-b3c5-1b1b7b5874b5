import { CreatePeopleAssignmentDto } from './dto/create-people-assignment.dto';
import { UpdatePeopleAssignmentDto } from './dto/update-people-assignment.dto';
import { People } from 'src/people/people.entity';
import { DataSource, Repository } from 'typeorm';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { AssignmentStatus, AssignmentType, PeopleAssignment } from './entities/people-assignment.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { AddEmailFromSpreadsheetDto, AddReplacementEmailsDto, MarkAsEmailNotFoundDto } from './dto/add-email-from-spreadsheet.dto';
import { AssignPersonsToUserDto } from './dto/get-assigned-persons.dto';
export interface GetAssignedPersonsQuery {
    userId: string;
    havingNoContactInfo?: boolean;
    needReplacement?: boolean;
    noEmailFound?: boolean;
    editEmailInfo?: boolean;
    findHiringPersons?: boolean;
    findBounceBacks?: boolean;
    searchString?: string;
    page?: number;
    pageSize?: number;
}
export declare class PeopleAssignmentsService {
    private peopleRepository;
    private companyRepository;
    private peopleAssignmentRepository;
    private personPhoneRepository;
    private usersRepository;
    private jobsRepository;
    private personEmailRepository;
    private dataSource;
    constructor(peopleRepository: Repository<People>, companyRepository: Repository<Company>, peopleAssignmentRepository: Repository<PeopleAssignment>, personPhoneRepository: Repository<PersonPhone>, usersRepository: Repository<Users>, jobsRepository: Repository<Jobs>, personEmailRepository: Repository<PersonEmail>, dataSource: DataSource);
    create(createPeopleAssignmentDto: CreatePeopleAssignmentDto): string;
    findAll(): string;
    findOne(id: number): string;
    update(id: number, updatePeopleAssignmentDto: UpdatePeopleAssignmentDto): string;
    remove(id: number): string;
    getAssignedPersonsByUserId(query: GetAssignedPersonsQuery): Promise<{
        message: string;
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            company: Company;
            current_title: string;
            avator: string;
            is_business_email_added: boolean;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: AssignmentType;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: AssignmentType;
            }[];
            businessPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            personalPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            claimed_by: {
                id: string;
                full_name: string;
                email: string;
            };
            replacement_claim: {
                is_replacement: true;
                replacement_date: Date;
            };
            latestJobPost: Jobs;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    private applyFilters;
    private formatAndPaginateResults;
    private formatEmailData;
    private formatPhoneData;
    assignPersonsByUserId(queryParams: AssignPersonsToUserDto): Promise<{
        message: string;
        data: {
            id: number;
            profile_url: string;
            full_name: string;
            company: Company;
            current_title: string;
            avator: string;
            is_business_email_added: boolean;
            businessEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: AssignmentType;
            }[];
            personalEmails: {
                id: number;
                email_id: string;
                user_id: string;
                status: AssignmentStatus;
                is_verified_by_amazon: boolean;
                is_default_email: boolean;
                type: AssignmentType;
            }[];
            businessPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            personalPhones: {
                id: any;
                phone_number: any;
                phone_type: any;
                is_default: any;
            }[];
            claimed_by: {
                id: string;
                full_name: string;
                email: string;
            };
            replacement_claim: {
                is_replacement: true;
                replacement_date: Date;
            };
            latestJobPost: Jobs;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    addEmailsFromSpreadSheet(data: AddEmailFromSpreadsheetDto): Promise<{
        message: string;
        data: any[];
        addedEmails: number;
        existingEmails: number;
        invalidEmails: number;
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    } | {
        message: string;
        data: any[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
        invalidEmails?: undefined;
        existingEmails?: undefined;
    } | {
        message: string;
        data: any[];
        invalidEmails: {
            id: number;
            email: string;
            is_default: boolean;
            is_replaced: boolean;
            isReplacedBy: number;
            person_id: number;
            websiteLink: string;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
        existingEmails?: undefined;
    } | {
        message: string;
        data: any[];
        invalidEmails: {
            id: number;
            email: string;
            is_default: boolean;
            is_replaced: boolean;
            isReplacedBy: number;
            person_id: number;
            websiteLink: string;
        }[];
        existingEmails: number;
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    addReplacementEmails(data: AddReplacementEmailsDto): Promise<void>;
    markAsEmailNotFound(data: MarkAsEmailNotFoundDto): Promise<{
        message: string;
    }>;
}
