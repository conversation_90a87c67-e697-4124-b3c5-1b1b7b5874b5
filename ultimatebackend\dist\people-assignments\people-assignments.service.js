"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeopleAssignmentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const people_entity_1 = require("../people/people.entity");
const typeorm_2 = require("typeorm");
const company_entity_1 = require("../company/company.entity");
const emails_entity_1 = require("../emails/emails.entity");
const people_assignment_entity_1 = require("./entities/people-assignment.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const users_entity_1 = require("../users/users.entity");
const phone_entity_1 = require("../phone/phone.entity");
const EmailValidator_1 = require("../helper/EmailValidator");
const people_enums_1 = require("../people/dto/people.enums");
let PeopleAssignmentsService = class PeopleAssignmentsService {
    constructor(peopleRepository, companyRepository, peopleAssignmentRepository, personPhoneRepository, usersRepository, jobsRepository, personEmailRepository, dataSource) {
        this.peopleRepository = peopleRepository;
        this.companyRepository = companyRepository;
        this.peopleAssignmentRepository = peopleAssignmentRepository;
        this.personPhoneRepository = personPhoneRepository;
        this.usersRepository = usersRepository;
        this.jobsRepository = jobsRepository;
        this.personEmailRepository = personEmailRepository;
        this.dataSource = dataSource;
    }
    create(createPeopleAssignmentDto) {
        return 'This action adds a new peopleAssignment';
    }
    findAll() {
        return `This action returns all peopleAssignments`;
    }
    findOne(id) {
        return `This action returns a #${id} peopleAssignment`;
    }
    update(id, updatePeopleAssignmentDto) {
        return `This action updates a #${id} peopleAssignment`;
    }
    remove(id) {
        return `This action removes a #${id} peopleAssignment`;
    }
    async getAssignedPersonsByUserId(query) {
        const { userId, havingNoContactInfo, needReplacement, noEmailFound, editEmailInfo, findHiringPersons, findBounceBacks, searchString, page = 0, pageSize = 10, } = query;
        const limit = pageSize;
        const skip = page * limit;
        const findPersonsByUserId = await this.peopleAssignmentRepository.find({
            where: {
                leadUserId: userId,
                is_working_completed: false,
                is_business_email_added: false,
                is_found: false,
            },
            order: {
                created_at: 'DESC',
                id: 'ASC',
            },
            take: limit,
            skip: skip,
            relations: [
                'person',
                'person.company',
                'person.emails',
                'person.phones',
                'leadUser',
                'company',
            ],
        });
        const filteredAssignments = this.applyFilters(findPersonsByUserId, {
            havingNoContactInfo,
            needReplacement,
            noEmailFound,
            editEmailInfo,
            findHiringPersons,
            findBounceBacks,
            searchString,
        });
        return this.formatAndPaginateResults(filteredAssignments, page, pageSize);
    }
    applyFilters(assignments, filters) {
        const { havingNoContactInfo, needReplacement, noEmailFound, editEmailInfo, findHiringPersons, findBounceBacks, searchString, } = filters;
        let filtered = assignments;
        if (havingNoContactInfo === 'true') {
            filtered = filtered.filter((a) => !a.is_business_email_added);
        }
        if (needReplacement === 'true') {
            filtered = filtered.filter((a) => a.is_replacement_needed);
        }
        if (noEmailFound === 'true') {
            filtered = filtered.filter((a) => a.is_email_not_found);
        }
        if (editEmailInfo === 'true') {
            filtered = filtered.filter((a) => a.is_email_info_added);
        }
        if (findHiringPersons === 'true') {
            filtered = filtered.filter((a) => a.is_hiring_person);
        }
        if (findBounceBacks === 'true') {
            filtered = filtered.filter((a) => a.is_bounce_back);
        }
        if (searchString) {
            filtered = filtered.filter((a) => a.person?.first_name
                ?.toLowerCase()
                .includes(searchString.toLowerCase()) ||
                a.person?.last_name
                    ?.toLowerCase()
                    .includes(searchString.toLowerCase()) ||
                a.person?.current_title
                    ?.toLowerCase()
                    .includes(searchString.toLowerCase()) ||
                a.person?.company?.name
                    ?.toLowerCase()
                    .includes(searchString.toLowerCase()) ||
                a.company?.name?.toLowerCase().includes(searchString.toLowerCase()));
        }
        return filtered;
    }
    async formatAndPaginateResults(assignments, page, pageSize, message) {
        const startIndex = page * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedAssignments = assignments.slice(startIndex, endIndex);
        const formattedResults = await Promise.all(paginatedAssignments.map(async (assignment) => {
            const person = assignment.person;
            const businessEmails = person?.emails?.filter((email) => email.email_type === 'BUSINESS') ||
                [];
            const personalEmails = person?.emails?.filter((email) => email.email_type === 'PERSONAL') ||
                [];
            const businessPhones = person?.phones?.filter((phone) => phone.phone_type === 'BUSINESS') ||
                [];
            const personalPhones = person?.phones?.filter((phone) => phone.phone_type === 'PERSONAL') ||
                [];
            const claimed_by = assignment.leadUser
                ? {
                    id: assignment.leadUser.id,
                    full_name: assignment.leadUser.full_name,
                    email: assignment.leadUser.email,
                }
                : null;
            const replacement_claim = assignment.is_replacement
                ? {
                    is_replacement: assignment.is_replacement,
                    replacement_date: assignment.updated_at,
                }
                : null;
            const latestJobPost = person?.jobs?.[0] || null;
            return {
                id: person?.id || assignment.id,
                profile_url: person?.profile_url,
                full_name: person
                    ? `${person.first_name || ''} ${person.last_name || ''}`.trim()
                    : '',
                company: person?.company || assignment.company,
                current_title: person?.current_title,
                avator: person?.profile_img,
                is_business_email_added: assignment.is_business_email_added,
                businessEmails: this.formatEmailData(businessEmails, assignment),
                personalEmails: this.formatEmailData(personalEmails, assignment),
                businessPhones: this.formatPhoneData(businessPhones),
                personalPhones: this.formatPhoneData(personalPhones),
                claimed_by,
                replacement_claim,
                latestJobPost,
            };
        }));
        return {
            message: message ? message : 'People fetched successfully',
            data: formattedResults,
            total: assignments.length,
            page,
            pageSize,
            totalPages: Math.ceil(assignments.length / pageSize),
        };
    }
    formatEmailData(emails, assignment) {
        return emails.map((e) => ({
            id: e.id,
            email_id: e.email,
            user_id: e.isReplacedBy,
            status: assignment.status,
            is_verified_by_amazon: assignment.is_verified_by_amazon,
            is_default_email: e.is_default,
            type: assignment.type,
        }));
    }
    formatPhoneData(phones) {
        return phones.map((phone) => ({
            id: phone.id,
            phone_number: phone.phone_number,
            phone_type: phone.phone_type,
            is_default: phone.is_default || false,
        }));
    }
    async assignPersonsByUserId(queryParams) {
        const { userId, page, pageSize } = queryParams;
        try {
            const pageInt = parseInt(page) || 0;
            const pageSizeInt = parseInt(pageSize) || 10;
            const alreadyAssignedToUser = await this.peopleAssignmentRepository.find({
                where: {
                    leadUserId: userId,
                    is_working_completed: false,
                    is_business_email_added: false,
                    is_found: false,
                },
                relations: [
                    'person',
                    'person.company',
                    'person.emails',
                    'person.phones',
                    'leadUser',
                    'company',
                ],
            });
            if (alreadyAssignedToUser.length > 0) {
                const message = `Please complete work on your already assigned ${alreadyAssignedToUser.length} persons.`;
                return this.formatAndPaginateResults(alreadyAssignedToUser, pageInt, pageSizeInt, message);
            }
            const step1Assignments = await this.peopleAssignmentRepository.find({
                where: {
                    leadUserId: (0, typeorm_2.IsNull)(),
                    is_working_completed: false,
                    is_business_email_added: false,
                    is_found: false,
                    is_email_not_found: false,
                },
                take: 50,
                relations: [
                    'person',
                    'person.company',
                    'person.emails',
                    'person.phones',
                    'leadUser',
                    'company',
                ],
            });
            const step2Assignments = await this.peopleAssignmentRepository.find({
                where: {
                    leadUserId: (0, typeorm_2.IsNull)(),
                    is_replacement_needed: true,
                    is_email_not_found: true,
                },
                take: 25,
                relations: [
                    'person',
                    'person.company',
                    'person.emails',
                    'person.phones',
                    'leadUser',
                    'company',
                ],
            });
            const step3Assignments = await this.peopleAssignmentRepository.find({
                where: {
                    leadUserId: null,
                    is_bounce_back: true,
                    is_business_email_added: true,
                    is_found: true,
                },
                take: 25,
                relations: [
                    'person',
                    'person.company',
                    'person.emails',
                    'person.phones',
                    'leadUser',
                    'company',
                ],
            });
            let allAssignments = [
                ...step1Assignments,
                ...step2Assignments,
                ...step3Assignments,
            ];
            if (allAssignments.length === 100) {
                return this.formatAndPaginateResults(allAssignments, pageInt, pageSizeInt);
            }
            const fallbackAssignments = await this.peopleAssignmentRepository.find({
                where: {
                    leadUserId: userId,
                    is_working_completed: false,
                    is_business_email_added: false,
                    is_found: false,
                },
                take: 100,
                relations: [
                    'person',
                    'person.company',
                    'person.emails',
                    'person.phones',
                    'leadUser',
                    'company',
                ],
            });
            if (fallbackAssignments.length === 100) {
                return this.formatAndPaginateResults(fallbackAssignments, pageInt, pageSizeInt);
            }
            const existingPersonIds = await this.peopleAssignmentRepository.find({
                select: ['personId'],
                where: {
                    personId: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
                },
            });
            const existingPersonIdsArray = existingPersonIds
                .map((item) => item.personId)
                .filter((id) => id !== null);
            const whereCondition = existingPersonIdsArray.length > 0
                ? { id: (0, typeorm_2.Not)((0, typeorm_2.In)(existingPersonIdsArray)) }
                : {};
            const availablePeople = await this.peopleRepository.find({
                where: {
                    id: (0, typeorm_2.Not)((0, typeorm_2.In)(existingPersonIdsArray)),
                    person_type: people_enums_1.PersonType.JOB_POST_LEAD,
                },
                take: 100,
                relations: ['company', 'emails', 'phones', 'sector', 'country'],
            });
            const newAssignments = [];
            for (const person of availablePeople) {
                const assignment = this.peopleAssignmentRepository.create({
                    personId: person.id,
                    email: null,
                    leadUserId: userId,
                    assignment_date: new Date(),
                    person: person,
                    companyId: person.companyId,
                    countryId: person.countryId,
                    company: person.company,
                    sectorId: person.sectorId,
                    sector: person.sector,
                });
                newAssignments.push(assignment);
            }
            if (newAssignments.length > 0) {
                await this.peopleAssignmentRepository.save(newAssignments);
            }
            return this.formatAndPaginateResults(newAssignments, pageInt, pageSizeInt);
        }
        catch (error) {
            console.log('Error in getAssignments:', error);
            throw new common_1.InternalServerErrorException('Error fetching assignments');
        }
    }
    async addEmailsFromSpreadSheet(data) {
        const { userId, businessEmails } = data;
        if (businessEmails.length === 0) {
            return {
                message: 'No emails found in the spreadsheet',
                data: [],
                total: 0,
                page: 0,
                pageSize: 0,
                totalPages: 0,
            };
        }
        const validationResults = await Promise.all(businessEmails.map(async (emailObj) => {
            const result = await (0, EmailValidator_1.validateEmail)(emailObj.email);
            const isBusinessDomain = (0, EmailValidator_1.isBusinessEmail)(emailObj.email, emailObj.websiteLink);
            const isValid = result.validators.regex.valid &&
                result.validators.disposable.valid &&
                isBusinessDomain;
            return {
                emailObj,
                isValid: isValid,
            };
        }));
        const validEmails = validationResults
            .filter((result) => result.isValid)
            .map((result) => result.emailObj);
        const invalidEmails = validationResults
            .filter((result) => !result.isValid)
            .map((result) => result.emailObj);
        const uniqueEmailIds = [
            ...new Set(validEmails.map((email) => email.email)),
        ];
        if (validEmails.length === 0) {
            return {
                message: `All ${businessEmails.length} emails are invalid or not business emails!`,
                data: [],
                invalidEmails: invalidEmails,
                total: 0,
                page: 0,
                pageSize: 0,
                totalPages: 0,
            };
        }
        const existingEmails = await this.personEmailRepository.find({
            where: {
                email: (0, typeorm_2.In)(uniqueEmailIds),
            },
            relations: ['person'],
        });
        const newEmails = validEmails.filter((email) => !existingEmails.some((existing) => existing.email === email.email));
        if (newEmails.length === 0) {
            return {
                message: `All ${validEmails.length} valid emails already exist in system!`,
                data: [],
                invalidEmails: invalidEmails,
                existingEmails: existingEmails.length,
                total: 0,
                page: 0,
                pageSize: 0,
                totalPages: 0,
            };
        }
        const emailsToAdd = newEmails.map((e) => ({
            email: e.email,
            is_default: e.is_default,
            personId: e.person_id,
            email_type: 'BUSINESS',
            is_replaced: false,
            isReplacedBy: null,
            isAddedBy: userId,
        }));
        return await this.dataSource.transaction(async (manager) => {
            try {
                const savedEmails = await manager
                    .getRepository(emails_entity_1.PersonEmail)
                    .insert(emailsToAdd);
                const uniquePersonIds = [
                    ...new Set(newEmails.map((email) => email.person_id)),
                ];
                await manager.getRepository(people_assignment_entity_1.PeopleAssignment).update({ personId: (0, typeorm_2.In)(uniquePersonIds) }, {
                    is_business_email_added: true,
                    is_found: true,
                    is_verified: true,
                    is_default_email: true,
                    is_email_info_added: true,
                    is_verified_by_lead_expert: true,
                    is_working_completed: true,
                    leadUserId: userId,
                });
                const insertedIds = savedEmails.identifiers.map((i) => i.id);
                await Promise.all(insertedIds.map((emailId, idx) => {
                    const personId = newEmails[idx].person_id;
                    return manager
                        .getRepository(people_entity_1.People)
                        .update({ id: personId }, { emailId });
                }));
                return {
                    message: `${newEmails.length} emails added successfully! ${existingEmails.length} emails already exist and ${invalidEmails.length} emails were invalid.`,
                    data: [],
                    addedEmails: newEmails.length,
                    existingEmails: existingEmails.length,
                    invalidEmails: invalidEmails.length,
                    total: newEmails.length,
                    page: 0,
                    pageSize: 0,
                    totalPages: 0,
                };
            }
            catch (error) {
                console.log('Failed to add emails:', error);
                throw new Error(`Failed to add emails: ${error.message}`);
            }
        });
    }
    async addReplacementEmails(data) { }
    async markAsEmailNotFound(data) {
        const { userId, persons } = data;
        try {
            await this.peopleAssignmentRepository.update({ personId: (0, typeorm_2.In)(persons) }, {
                is_found: false,
                is_email_not_found: true,
                is_replacement_needed: true,
                is_working_completed: false,
                leadUserId: null,
                not_found_by: userId,
            });
            return { message: 'Emails marked as not found successfully!' };
        }
        catch (error) {
            console.log('Failed to mark emails as not found:', error);
            throw new Error(`Failed to mark emails as not found: ${error.message}`);
        }
    }
};
exports.PeopleAssignmentsService = PeopleAssignmentsService;
exports.PeopleAssignmentsService = PeopleAssignmentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(1, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(2, (0, typeorm_1.InjectRepository)(people_assignment_entity_1.PeopleAssignment)),
    __param(3, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __param(4, (0, typeorm_1.InjectRepository)(users_entity_1.Users)),
    __param(5, (0, typeorm_1.InjectRepository)(jobs_entity_1.Jobs)),
    __param(6, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], PeopleAssignmentsService);
//# sourceMappingURL=people-assignments.service.js.map