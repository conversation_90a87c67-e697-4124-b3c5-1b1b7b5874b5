import { PersonSource, PersonStatusType, PersonType, ProspectStatus, SubscriptionType } from './people.enums';
export declare class PeopleDto {
    id: number;
    first_name: string;
    last_name: string;
    full_name: string;
    current_title: string;
    profile_img?: string;
    headline?: string;
    profile_url?: string;
    location?: string;
    SR_specied_industry?: string;
    summary?: string;
    person_type: PersonType;
    is_hiring: boolean;
    profile_source: PersonSource;
    profile_source_link?: string;
    cv_text?: string;
    client_number?: string;
    subscription_type: SubscriptionType;
    subscription_start_date?: Date;
    subscription_end_date?: Date;
    reminder_date?: Date;
    amount_paid?: number;
    payment_date?: Date;
    credits?: number;
    credits_per_day?: number;
    credits_used?: number;
    client_status: PersonStatusType;
    prospect_status?: ProspectStatus;
    prospect_status_date?: Date;
    prospect_status_comment?: string;
    companyId?: number;
    countryId?: number;
    sectorId?: number;
    serviceId?: number;
    role_candidate_id?: number;
    created_at: Date;
    updated_at: Date;
    userId?: string;
    acmUserId?: string;
    bdUserId?: string;
    skills?: any[];
    languages?: any[];
    certifications?: any[];
    qualifications?: any[];
    experience?: any[];
    emails?: any[];
    phones?: any[];
    roles?: any[];
    job_applications?: any[];
    job_alerts?: any[];
    projects?: any[];
}
export declare class GetAllPersonsDto {
    page: string;
    pageSize: string;
    search: string;
    country_id: string;
    sector_id: string;
    findHiringPersons: string;
    findWithEmails: string;
    findWithoutEmails: string;
    selectedCountry: string;
    selectedSector: string;
    industries: string[];
    startDate: string;
    endDate: string;
}
