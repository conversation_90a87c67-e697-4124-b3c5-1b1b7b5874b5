"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAllPersonsDto = exports.PeopleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const people_enums_1 = require("./people.enums");
const class_validator_1 = require("class-validator");
class PeopleDto {
}
exports.PeopleDto = PeopleDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], PeopleDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PeopleDto.prototype, "first_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PeopleDto.prototype, "last_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PeopleDto.prototype, "full_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", String)
], PeopleDto.prototype, "current_title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "profile_img", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "headline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "profile_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "SR_specied_industry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: people_enums_1.PersonType, default: people_enums_1.PersonType.OTHER }),
    __metadata("design:type", String)
], PeopleDto.prototype, "person_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Boolean)
], PeopleDto.prototype, "is_hiring", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: people_enums_1.PersonSource, default: people_enums_1.PersonSource.OTHER }),
    __metadata("design:type", String)
], PeopleDto.prototype, "profile_source", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "profile_source_link", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "cv_text", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "client_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: people_enums_1.SubscriptionType, default: people_enums_1.SubscriptionType.FREE }),
    __metadata("design:type", String)
], PeopleDto.prototype, "subscription_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Date)
], PeopleDto.prototype, "subscription_start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Date)
], PeopleDto.prototype, "subscription_end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Date)
], PeopleDto.prototype, "reminder_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "amount_paid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Date)
], PeopleDto.prototype, "payment_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "credits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "credits_per_day", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "credits_used", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: people_enums_1.PersonStatusType, default: people_enums_1.PersonStatusType.ACTIVE }),
    __metadata("design:type", String)
], PeopleDto.prototype, "client_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: people_enums_1.ProspectStatus, required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "prospect_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Date)
], PeopleDto.prototype, "prospect_status_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "prospect_status_comment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "countryId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "sectorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "serviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        required: false,
        example: 'Only needed from scrappers getting linkedin profiles data. Not needed in CRM or Website. Just leave it empty if you are on CRM or Website.',
    }),
    __metadata("design:type", Number)
], PeopleDto.prototype, "role_candidate_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PeopleDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], PeopleDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "acmUserId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], PeopleDto.prototype, "bdUserId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "skills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "languages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "certifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "qualifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "experience", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "emails", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "phones", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "roles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "job_applications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "job_alerts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Array)
], PeopleDto.prototype, "projects", void 0);
class GetAllPersonsDto {
}
exports.GetAllPersonsDto = GetAllPersonsDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "pageSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "country_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "sector_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "findHiringPersons", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "findWithEmails", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "findWithoutEmails", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "selectedCountry", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "selectedSector", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], GetAllPersonsDto.prototype, "industries", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAllPersonsDto.prototype, "endDate", void 0);
//# sourceMappingURL=createPeople.dto.js.map