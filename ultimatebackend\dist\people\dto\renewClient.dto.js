"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RenewClientDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RenewClientDto {
}
exports.RenewClientDto = RenewClientDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RenewClientDto.prototype, "client_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'enum',
        enum: ['FREE', 'MONTHLY', 'ANNUALLY', 'ADHOC'],
        default: 'FREE',
    }),
    (0, class_validator_1.IsEnum)(['FREE', 'MONTHLY', 'ANNUALLY', 'ADHOC']),
    __metadata("design:type", String)
], RenewClientDto.prototype, "subscription_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'date',
        description: 'Date when the subscription starts',
    }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RenewClientDto.prototype, "subscription_start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'date',
        description: 'Date when the subscription ends',
    }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RenewClientDto.prototype, "subscription_end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'date',
        description: 'Date when the reminder is set',
    }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RenewClientDto.prototype, "reminder_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'Amount paid for the subscription',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RenewClientDto.prototype, "amount_paid", void 0);
__decorate([
    (0, class_validator_1.IsDate)(),
    (0, swagger_1.ApiProperty)({
        type: 'date',
        description: 'Date when the payment was made',
    }),
    __metadata("design:type", Date)
], RenewClientDto.prototype, "payment_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'Number of credits available',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RenewClientDto.prototype, "credits", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'Number of credits per day',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RenewClientDto.prototype, "credits_per_day", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: 'number',
        description: 'Number of credits used',
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RenewClientDto.prototype, "credits_used", void 0);
//# sourceMappingURL=renewClient.dto.js.map