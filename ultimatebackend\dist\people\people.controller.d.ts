import { PeopleService } from './people.service';
import { UpdatePeopleDTO } from './dto/updatePeople.dto';
import { GetAllPersonsDto, PeopleDto } from './dto/createPeople.dto';
import { RenewClientDto } from './dto/renewClient.dto';
import { PersonType } from './dto/people.enums';
import { People } from './people.entity';
export declare class PeopleController {
    private readonly peopleService;
    constructor(peopleService: PeopleService);
    createPerson(body: any): Promise<People>;
    addPeopleInBulk(body: PeopleDto[]): Promise<{
        message: string;
    }>;
    uploadCsv(file: Express.Multer.File): Promise<{
        message: string;
    }>;
    updateLiCandidate(body: PeopleDto): Promise<{
        message: string;
    }>;
    updatePerson(id: number, body: UpdatePeopleDTO): Promise<People>;
    findAll(page: number, pageSize: number, search: string, person_type: PersonType): Promise<People[]>;
    getAllPersons(queryParams: GetAllPersonsDto): Promise<{
        persons: People[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
    }>;
    findPersonByCompanyId(company_id: string): Promise<People[]>;
    findUniquePersonTitles(): Promise<any[]>;
    findSearchPersons(search: string): Promise<People[]>;
    findPersonsOfCompany(company_id: string, search: string): Promise<People[]>;
    findOne(id: number): Promise<People>;
    deletePerson(id: number): Promise<void>;
    findAllClients(page: number, pageSize: number, search: string, acmUserId: string, bdUserId: string, start_date: Date, end_date: Date, serviceId: number): Promise<People[]>;
    findClientById(id: number): Promise<People>;
    findClientByServiceId(serviceId: number): Promise<People[]>;
    findClientServiceById(client_number: string): Promise<People>;
    renewClient(id: number, body: RenewClientDto): Promise<People>;
    updateClientStatus(id: number, status: string): Promise<People>;
    getClientServiceStats(start_date: Date, end_date: Date, service_id: number, bdUserId: string, acmUserId: string): Promise<{
        service_name: string;
        total: number;
    }[]>;
    findAllProspects(page: number, pageSize: number, search: string, acmUserId: string, bdUserId: string, start_date: Date, end_date: Date, serviceId: number): Promise<People[]>;
    findProspectById(id: number): Promise<People>;
    findProspectByServiceId(serviceId: number): Promise<People[]>;
    findProspectServiceById(client_number: string): Promise<People>;
    renewProspect(id: number, body: RenewClientDto): Promise<People>;
    updateProspectStatus(id: number, status: string): Promise<People>;
    getProspectServiceStats(start_date: Date, end_date: Date, service_id: number, bdUserId: string, acmUserId: string): Promise<{
        service_name: string;
        total: number;
    }[]>;
    getProspectSubscriptionStatus(prospect_status: string): Promise<People[]>;
    updateProspectSubscriptionStatus(id: number, prospect_status: string): Promise<People>;
    getAllProspectStatuses(prospect_status: string): Promise<People>;
    updateProspectStatusByClientNumberAndServiceId(client_number: string, service_id: number, prospect_status: string): Promise<People>;
    getUserPerson(userId: string): Promise<People>;
    upsertPerson(body: PeopleDto): Promise<People>;
    getPersonByPersonType(person_type: PersonType): Promise<People[]>;
    getPartiallyIntrestedProspects(): Promise<{
        [key: string]: People[];
    }>;
    getTrialProspects(): Promise<{
        [key: string]: People[];
    }>;
    getInFutureProspects(page: number, pageSize: number, search: string): Promise<{
        [key: string]: People[];
    }>;
    getConvertedProspects(page: number, pageSize: number, search: string): Promise<{
        [key: string]: People[];
    }>;
    candidateStatsBySource(roleId: number): Promise<{
        source: string;
        total: number;
    }[]>;
    bdDashboard(): Promise<{
        sectorWiseProspects: {
            total: number;
            direct: number;
            sr: number;
        };
        prospectsOverview: {
            trialRecieved: number;
            trialSent: number;
            trialInProcess: number;
            trialResultsRecieved: number;
            trialPending: number;
            trialSuccessfull: number;
            trialFailed: number;
        };
        emailsData: {
            totalEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            autoRepliesEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            bouncedEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            potentialReplies: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
        };
    }>;
    getCandidates(query?: string, titles?: string, locations?: string, skills?: string, companies?: string, industries?: string, keywords?: string, seniority?: string, hideViewed?: boolean, page?: number, pageSize?: number): Promise<{
        message: string;
        results: People[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
}
