"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeopleController = void 0;
const common_1 = require("@nestjs/common");
const people_service_1 = require("./people.service");
const swagger_1 = require("@nestjs/swagger");
const updatePeople_dto_1 = require("./dto/updatePeople.dto");
const createPeople_dto_1 = require("./dto/createPeople.dto");
const renewClient_dto_1 = require("./dto/renewClient.dto");
const people_enums_1 = require("./dto/people.enums");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const people_entity_1 = require("./people.entity");
let PeopleController = class PeopleController {
    constructor(peopleService) {
        this.peopleService = peopleService;
    }
    async createPerson(body) {
        const { company_name, company_link, email, phone_number, company_size, ...peopleFields } = body;
        return this.peopleService.create(peopleFields, company_name, company_link, email, phone_number, company_size);
    }
    async addPeopleInBulk(body) {
        return this.peopleService.addPeopleInBulk(body);
    }
    async uploadCsv(file) {
        if (!file) {
            throw new Error('No file uploaded');
        }
        return this.peopleService.importPeopleFromCsv(file.path);
    }
    async updateLiCandidate(body) {
        const id = body.id;
        return this.peopleService.updateLiCandidate(id, body);
    }
    async updatePerson(id, body) {
        return this.peopleService.update(id, body);
    }
    async findAll(page, pageSize, search, person_type) {
        return this.peopleService.findAll(page, pageSize, search, person_type);
    }
    async getAllPersons(queryParams) {
        return this.peopleService.getAllPersons(queryParams);
    }
    async findPersonByCompanyId(company_id) {
        console.log('I am called: ' + company_id);
        return this.peopleService.findPersonByCompanyId(company_id);
    }
    async findUniquePersonTitles() {
        return this.peopleService.findUniquePersonTitles();
    }
    async findSearchPersons(search) {
        return this.peopleService.findPersons(search);
    }
    async findPersonsOfCompany(company_id, search) {
        return this.peopleService.findPersonsByCompany(company_id, search);
    }
    async findOne(id) {
        return this.peopleService.findOne(id);
    }
    async deletePerson(id) {
        return this.peopleService.delete(id);
    }
    async findAllClients(page, pageSize, search, acmUserId, bdUserId, start_date, end_date, serviceId) {
        return this.peopleService.findAllClients(page, pageSize, search, acmUserId, bdUserId, start_date, end_date, serviceId);
    }
    async findClientById(id) {
        return this.peopleService.findClientById(id);
    }
    async findClientByServiceId(serviceId) {
        return this.peopleService.findClientByServiceId(serviceId);
    }
    async findClientServiceById(client_number) {
        return this.peopleService.findClientByClientNumber(client_number);
    }
    async renewClient(id, body) {
        return this.peopleService.renewClient(id, body);
    }
    async updateClientStatus(id, status) {
        return this.peopleService.updateClientStatus(id, status);
    }
    async getClientServiceStats(start_date, end_date, service_id, bdUserId, acmUserId) {
        return this.peopleService.getClientServiceStats(start_date, end_date, service_id, bdUserId, acmUserId);
    }
    async findAllProspects(page, pageSize, search, acmUserId, bdUserId, start_date, end_date, serviceId) {
        return this.peopleService.findAllProspects(page, pageSize, search, acmUserId, bdUserId, start_date, end_date, serviceId);
    }
    async findProspectById(id) {
        return this.peopleService.findProspectById(id);
    }
    async findProspectByServiceId(serviceId) {
        return this.peopleService.findProspectByServiceId(serviceId);
    }
    async findProspectServiceById(client_number) {
        return this.peopleService.findProspectByClientNumber(client_number);
    }
    async renewProspect(id, body) {
        return this.peopleService.renewProspect(id, body);
    }
    async updateProspectStatus(id, status) {
        return this.peopleService.updateProspectStatus(id, status);
    }
    async getProspectServiceStats(start_date, end_date, service_id, bdUserId, acmUserId) {
        return this.peopleService.getProspectServiceStats(start_date, end_date, service_id, bdUserId, acmUserId);
    }
    async getProspectSubscriptionStatus(prospect_status) {
        return this.peopleService.getProspectSubscriptionStatus(prospect_status);
    }
    async updateProspectSubscriptionStatus(id, prospect_status) {
        return this.peopleService.updateProspectStatusById(id, prospect_status);
    }
    async getAllProspectStatuses(prospect_status) {
        return this.peopleService.getProspectByProspectStatus(prospect_status);
    }
    async updateProspectStatusByClientNumberAndServiceId(client_number, service_id, prospect_status) {
        return this.peopleService.updateProspectStatusByClientNumberAndServiceId(client_number, service_id, prospect_status);
    }
    async getUserPerson(userId) {
        return this.peopleService.getPersonByUserId(userId);
    }
    async upsertPerson(body) {
        return this.peopleService.upsertPerson(body);
    }
    async getPersonByPersonType(person_type) {
        return this.peopleService.getPersonByPersonType(person_type);
    }
    async getPartiallyIntrestedProspects() {
        return this.peopleService.getPartiallyIntrestedPeople();
    }
    async getTrialProspects() {
        return this.peopleService.getTrialProspects();
    }
    async getInFutureProspects(page, pageSize, search) {
        return this.peopleService.getInFutureProspects(page, pageSize, search);
    }
    async getConvertedProspects(page, pageSize, search) {
        return this.peopleService.getConvertedProspects(page, pageSize, search);
    }
    async candidateStatsBySource(roleId) {
        return this.peopleService.candidateStatsBySource(roleId);
    }
    async bdDashboard() {
        return this.peopleService.bdDashboard();
    }
    async getCandidates(query, titles, locations, skills, companies, industries, keywords, seniority, hideViewed, page = 1, pageSize = 20) {
        try {
            const filters = {
                titles: titles ? titles.split(',').map(t => t.trim()) : [],
                locations: locations ? locations.split(',').map(l => l.trim()) : [],
                skills: skills ? skills.split(',').map(s => s.trim()) : [],
                companies: companies ? companies.split(',').map(c => c.trim()) : [],
                industries: industries ? industries.split(',').map(i => i.trim()) : [],
                keywords: keywords ? keywords.split(',').map(k => k.trim()) : [],
                seniority: seniority ? seniority.split(',').map(s => s.trim()) : [],
                hideViewed: hideViewed || false,
            };
            const result = await this.peopleService.searchCandidatesWithAdvancedFilters(query, filters, page, pageSize);
            return result;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Error processing search: ${error.message}`);
        }
    }
};
exports.PeopleController = PeopleController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new person' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "createPerson", null);
__decorate([
    (0, common_1.Post)('addPeopleInBulk'),
    (0, swagger_1.ApiOperation)({ summary: 'Add people in bulk' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "addPeopleInBulk", null);
__decorate([
    (0, common_1.Post)('upload-csv'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        storage: (0, multer_1.diskStorage)({
            destination: './csv-uploads',
            filename: (req, file, cb) => {
                const originalName = file.originalname;
                const extension = (0, path_1.extname)(originalName);
                const fileName = Date.now() + extension;
                cb(null, fileName);
            },
        }),
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "uploadCsv", null);
__decorate([
    (0, common_1.Post)('updatePerson'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a person' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createPeople_dto_1.PeopleDto]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updateLiCandidate", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a person' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updatePeople_dto_1.UpdatePeopleDTO]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updatePerson", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all people' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Page size',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'person_type',
        required: false,
        description: 'Person type',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of people',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('person_type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('getAllPersons'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createPeople_dto_1.GetAllPersonsDto]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getAllPersons", null);
__decorate([
    (0, common_1.Get)('getPersonByCompanyId'),
    __param(0, (0, common_1.Query)('company_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findPersonByCompanyId", null);
__decorate([
    (0, common_1.Get)('getUniquePersonTitles'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findUniquePersonTitles", null);
__decorate([
    (0, common_1.Get)('searchPersons'),
    __param(0, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findSearchPersons", null);
__decorate([
    (0, common_1.Get)('searchPersonsOfCompany'),
    __param(0, (0, common_1.Query)('company_id')),
    __param(1, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findPersonsOfCompany", null);
__decorate([
    (0, common_1.Get)('find/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a person by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a person by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "deletePerson", null);
__decorate([
    (0, common_1.Get)('client/all-user-added'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all clients' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Page size',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'acmUserId',
        required: false,
        description: 'ACM user ID',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'bdUserId',
        required: false,
        description: 'BD user ID',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'start_date',
        required: false,
        description: 'Start date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'end_date',
        required: false,
        description: 'End date',
        type: Date,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of clients',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'serviceId',
        required: false,
        description: 'Service Id',
        type: Number,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No clients found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('acmUserId')),
    __param(4, (0, common_1.Query)('bdUserId')),
    __param(5, (0, common_1.Query)('start_date')),
    __param(6, (0, common_1.Query)('end_date')),
    __param(7, (0, common_1.Query)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, Date,
        Date, Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findAllClients", null);
__decorate([
    (0, common_1.Get)('client/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a client by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client details',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Client not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findClientById", null);
__decorate([
    (0, common_1.Get)('client/service/:serviceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all clients by service ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of clients by service ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No clients found for the given service ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findClientByServiceId", null);
__decorate([
    (0, common_1.Get)('client/:client_number'),
    (0, swagger_1.ApiOperation)({ summary: 'Get client service by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client service details',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Client service not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('client_number')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findClientServiceById", null);
__decorate([
    (0, common_1.Put)('client/renew/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Renew client service' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client service renewed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Client service not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, renewClient_dto_1.RenewClientDto]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "renewClient", null);
__decorate([
    (0, common_1.Put)('client/update-status/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update client status' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updateClientStatus", null);
__decorate([
    (0, common_1.Get)('client/stats/service'),
    (0, swagger_1.ApiOperation)({ summary: 'Get client service stats' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client service stats',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No client service stats found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'start_date',
        required: false,
        description: 'Start date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'end_date',
        required: false,
        description: 'End date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'service_id',
        required: false,
        description: 'Service ID',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'bdUserId',
        required: false,
        description: 'BD user ID',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'acmUserId',
        required: false,
        description: 'ACM user ID',
        type: String,
    }),
    __param(0, (0, common_1.Query)('start_date')),
    __param(1, (0, common_1.Query)('end_date')),
    __param(2, (0, common_1.Query)('service_id')),
    __param(3, (0, common_1.Query)('bdUserId')),
    __param(4, (0, common_1.Query)('acmUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, Number, String, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getClientServiceStats", null);
__decorate([
    (0, common_1.Get)('prospect/all-user-added'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Page size',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'acmUserId',
        required: false,
        description: 'ACM user ID',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'bdUserId',
        required: false,
        description: 'BD user ID',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'start_date',
        required: false,
        description: 'Start date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'end_date',
        required: false,
        description: 'End date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'serviceId',
        required: false,
        description: 'Service Id',
        type: Number,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('acmUserId')),
    __param(4, (0, common_1.Query)('bdUserId')),
    __param(5, (0, common_1.Query)('start_date')),
    __param(6, (0, common_1.Query)('end_date')),
    __param(7, (0, common_1.Query)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, Date,
        Date, Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findAllProspects", null);
__decorate([
    (0, common_1.Get)('prospect/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a prospect by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect details',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Prospect not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findProspectById", null);
__decorate([
    (0, common_1.Get)('prospect/service/:serviceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects by service ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects by service ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found for the given service ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findProspectByServiceId", null);
__decorate([
    (0, common_1.Get)('prospect/:client_number'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prospect service by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect service details',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Prospect service not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('client_number')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "findProspectServiceById", null);
__decorate([
    (0, common_1.Put)('prospect/renew/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Renew prospect service' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect service renewed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Prospect service not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, renewClient_dto_1.RenewClientDto]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "renewProspect", null);
__decorate([
    (0, common_1.Put)('prospect/update-status/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update prospect status' }),
    (0, swagger_1.ApiBody)({
        description: 'Status to update',
        type: String,
        required: true,
        schema: {
            type: 'object',
            properties: {
                status: {
                    type: 'string',
                    description: 'New status',
                    example: 'active',
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updateProspectStatus", null);
__decorate([
    (0, common_1.Get)('prospect/stats/service'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prospect service stats' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect service stats',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospect service stats found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'start_date',
        required: false,
        description: 'Start date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'end_date',
        required: false,
        description: 'End date',
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'service_id',
        required: false,
        description: 'Service ID',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'bdUserId',
        required: false,
        description: 'BD user ID',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'acmUserId',
        required: false,
        description: 'ACM user ID',
        type: String,
    }),
    __param(0, (0, common_1.Query)('start_date')),
    __param(1, (0, common_1.Query)('end_date')),
    __param(2, (0, common_1.Query)('service_id')),
    __param(3, (0, common_1.Query)('bdUserId')),
    __param(4, (0, common_1.Query)('acmUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, Number, String, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getProspectServiceStats", null);
__decorate([
    (0, common_1.Get)('prospect/subscriptionStatus/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get prospect subscription status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect subscription status',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Prospect subscription status not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('prospect_status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getProspectSubscriptionStatus", null);
__decorate([
    (0, common_1.Put)('prospect/update-subscription-status/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update prospect subscription status' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('prospect_status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updateProspectSubscriptionStatus", null);
__decorate([
    (0, common_1.Get)('prospect/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospect statuses' }),
    (0, swagger_1.ApiQuery)({
        name: 'prospect_status',
        required: false,
        description: 'Prospect status',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospect statuses',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospect statuses found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Query)('prospect_status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getAllProspectStatuses", null);
__decorate([
    (0, common_1.Put)('prospect/update-status-by-client-number-and-service-id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update prospect status by client number and service ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prospect status updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Prospect status not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'client_number',
        required: true,
        description: 'Client number',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'service_id',
        required: true,
        description: 'Service ID',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'prospect_status',
        required: true,
        description: 'Prospect status',
        type: String,
    }),
    __param(0, (0, common_1.Body)('client_number')),
    __param(1, (0, common_1.Body)('service_id')),
    __param(2, (0, common_1.Body)('prospect_status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "updateProspectStatusByClientNumberAndServiceId", null);
__decorate([
    (0, common_1.Get)('person/:userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get client by user ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client details',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Client not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getUserPerson", null);
__decorate([
    (0, common_1.Post)('upsert_person'),
    (0, swagger_1.ApiOperation)({ summary: 'Insert or update person' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Person inserted or updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Person not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createPeople_dto_1.PeopleDto]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "upsertPerson", null);
__decorate([
    (0, common_1.Get)('person-by-person-type'),
    (0, swagger_1.ApiOperation)({ summary: 'Get person by person type' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Person details',
    }),
    __param(0, (0, common_1.Query)('person_type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getPersonByPersonType", null);
__decorate([
    (0, common_1.Get)('get-partially-intrested-prospects'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getPartiallyIntrestedProspects", null);
__decorate([
    (0, common_1.Get)('getTrialProspects'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getTrialProspects", null);
__decorate([
    (0, common_1.Get)('getInFutureProspects'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Page size',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getInFutureProspects", null);
__decorate([
    (0, common_1.Get)('getConvertedProspects'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all prospects' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Page size',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        description: 'Search term',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of prospects',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'No prospects found',
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getConvertedProspects", null);
__decorate([
    (0, common_1.Get)('getCandidateStatsBySource'),
    (0, swagger_1.ApiOperation)({ summary: 'Get candidate stats by source' }),
    (0, swagger_1.ApiQuery)({
        name: 'roleId',
        required: true,
        type: Number,
    }),
    __param(0, (0, common_1.Query)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "candidateStatsBySource", null);
__decorate([
    (0, common_1.Get)('getBdDashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "bdDashboard", null);
__decorate([
    (0, common_1.Get)('getCandidateBySearchString'),
    (0, swagger_1.ApiOperation)({ summary: 'Search candidates using Boolean query with advanced filters' }),
    (0, swagger_1.ApiQuery)({
        name: 'q',
        required: false,
        description: 'Boolean string query for candidate search',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'titles',
        required: false,
        description: 'Comma-separated list of job titles',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'locations',
        required: false,
        description: 'Comma-separated list of locations',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'skills',
        required: false,
        description: 'Comma-separated list of skills',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'companies',
        required: false,
        description: 'Comma-separated list of companies',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'industries',
        required: false,
        description: 'Comma-separated list of industries',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'keywords',
        required: false,
        description: 'Comma-separated list of keywords',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'seniority',
        required: false,
        description: 'Comma-separated list of seniority levels',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'hideViewed',
        required: false,
        description: 'Hide previously viewed candidates',
        type: Boolean,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number for pagination',
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        description: 'Number of results per page',
        type: Number,
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Search results', type: [people_entity_1.People] }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Query)('q')),
    __param(1, (0, common_1.Query)('titles')),
    __param(2, (0, common_1.Query)('locations')),
    __param(3, (0, common_1.Query)('skills')),
    __param(4, (0, common_1.Query)('companies')),
    __param(5, (0, common_1.Query)('industries')),
    __param(6, (0, common_1.Query)('keywords')),
    __param(7, (0, common_1.Query)('seniority')),
    __param(8, (0, common_1.Query)('hideViewed')),
    __param(9, (0, common_1.Query)('page')),
    __param(10, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String, Boolean, Number, Number]),
    __metadata("design:returntype", Promise)
], PeopleController.prototype, "getCandidates", null);
exports.PeopleController = PeopleController = __decorate([
    (0, common_1.Controller)('people'),
    (0, swagger_1.ApiTags)('people'),
    __metadata("design:paramtypes", [people_service_1.PeopleService])
], PeopleController);
//# sourceMappingURL=people.controller.js.map