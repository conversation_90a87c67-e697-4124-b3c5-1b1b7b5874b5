"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PeopleModule = void 0;
const common_1 = require("@nestjs/common");
const people_service_1 = require("./people.service");
const people_controller_1 = require("./people.controller");
const typeorm_1 = require("@nestjs/typeorm");
const people_entity_1 = require("./people.entity");
const service_entity_1 = require("../service/service.entity");
const skills_entity_1 = require("../skills/skills.entity");
const qualifications_entity_1 = require("../qualifications/qualifications.entity");
const experience_entity_1 = require("../experience/experience.entity");
const langauges_entity_1 = require("../languages/langauges.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
const company_entity_1 = require("../company/company.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
const mailBox_entity_1 = require("../mail-box/mailBox.entity");
let PeopleModule = class PeopleModule {
};
exports.PeopleModule = PeopleModule;
exports.PeopleModule = PeopleModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                people_entity_1.People,
                service_entity_1.Service,
                skills_entity_1.PersonSkill,
                qualifications_entity_1.Qualifications,
                experience_entity_1.Experience,
                langauges_entity_1.Languages,
                role_candidates_entity_1.RoleCandidate,
                company_entity_1.Company,
                people_assignment_entity_1.PeopleAssignment,
                mailBox_entity_1.MailBox,
            ]),
        ],
        providers: [people_service_1.PeopleService],
        controllers: [people_controller_1.PeopleController],
    })
], PeopleModule);
//# sourceMappingURL=people.module.js.map