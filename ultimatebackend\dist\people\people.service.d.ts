import { DataSource, Repository } from 'typeorm';
import { People } from './people.entity';
import { GetAllPersonsDto, PeopleDto } from './dto/createPeople.dto';
import { UpdatePeopleDTO } from './dto/updatePeople.dto';
import { RenewClientDto } from './dto/renewClient.dto';
import { Service } from 'src/service/service.entity';
import { PersonType } from './dto/people.enums';
import { PersonSkill } from 'src/skills/skills.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Experience } from 'src/experience/experience.entity';
import { Languages } from 'src/languages/langauges.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { Company } from 'src/company/company.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';
export declare class PeopleService {
    private readonly peopleRepository;
    private readonly serviceRepository;
    private readonly personSkillRepository;
    private readonly qualificationsRepository;
    private readonly experienceRepository;
    private readonly languagesRepository;
    private readonly dataSource;
    private readonly roleCandidateRepository;
    private readonly companyRepository;
    private readonly mailBoxRepository;
    constructor(peopleRepository: Repository<People>, serviceRepository: Repository<Service>, personSkillRepository: Repository<PersonSkill>, qualificationsRepository: Repository<Qualifications>, experienceRepository: Repository<Experience>, languagesRepository: Repository<Languages>, dataSource: DataSource, roleCandidateRepository: Repository<RoleCandidate>, companyRepository: Repository<Company>, mailBoxRepository: Repository<MailBox>);
    create(PeopleDto: PeopleDto, company_name: string, company_link: string, email: string, phone_number: string, company_size: string): Promise<People>;
    findAll(page?: number, pageSize?: number, search?: string, person_type?: PersonType): Promise<People[]>;
    getAllPersons(queryParams: GetAllPersonsDto): Promise<{
        persons: People[];
        totalCount: number;
        sr_count: number;
        direct_count: number;
        unknown_count: number;
    }>;
    findPersonByCompanyId(company_id: string): Promise<People[]>;
    findUniquePersonTitles(): Promise<any[]>;
    findPersons(searchTerm: string): Promise<People[]>;
    findPersonsByCompany(company_id: string, searchTerm?: string): Promise<People[]>;
    findOne(id: number): Promise<People>;
    parseLinkedInDate: (str: string) => Date | null;
    updateLiCandidate(id: number, candidate: PeopleDto): Promise<{
        message: string;
    }>;
    update(id: number, updatePeopleDto: UpdatePeopleDTO): Promise<People>;
    delete(id: number): Promise<void>;
    findClientByServiceId(serviceId: number): Promise<People[]>;
    findAllClients(page?: number, pageSize?: number, search?: string, acmUserId?: string, bdUserId?: string, start_date?: Date, end_date?: Date, serviceId?: number): Promise<People[]>;
    renewClient(id: number, renewClientDto: RenewClientDto): Promise<People>;
    updateClientStatus(id: number, client_status: string): Promise<People>;
    findClientById(clientId: number): Promise<People>;
    findClientByClientNumber(client_number: string): Promise<People>;
    getClientServiceStats(start_date?: Date, end_date?: Date, serviceId?: number, bdUserId?: string, acmUserId?: string): Promise<{
        service_name: string;
        total: number;
    }[]>;
    findProspectByServiceId(serviceId: number): Promise<People[]>;
    findAllProspects(page?: number, pageSize?: number, search?: string, acmUserId?: string, bdUserId?: string, start_date?: Date, end_date?: Date, serviceId?: number): Promise<People[]>;
    renewProspect(id: number, renewClientDto: RenewClientDto): Promise<People>;
    updateProspectStatus(id: number, client_status: string): Promise<People>;
    findProspectById(clientId: number): Promise<People>;
    findProspectByClientNumber(client_number: string): Promise<People>;
    getProspectServiceStats(start_date?: Date, end_date?: Date, serviceId?: number, bdUserId?: string, acmUserId?: string): Promise<{
        service_name: string;
        total: number;
    }[]>;
    getProspectSubscriptionStatus(client_status: string): Promise<People[]>;
    updateProspectSubscriptionStatusById(id: number, client_status: string): Promise<People>;
    updateProspectSubscriptionStatusByClientNumber(client_number: string, client_status: string): Promise<People>;
    findProspectByClientNumberAndServiceId(client_number: string, serviceId: number): Promise<People>;
    updateProspectStatusById(id: number, prospect_status: string): Promise<People>;
    getProspectByProspectStatus(prospect_status: string): Promise<People>;
    updateProspectStatusByClientNumberAndServiceId(client_number: string, serviceId: number, prospect_status: string): Promise<People>;
    getPersonByUserId(userId: string): Promise<People>;
    upsertPerson(PeopleDto: PeopleDto): Promise<People>;
    getPersonByPersonType(person_type: PersonType): Promise<People[]>;
    getPartiallyIntrestedPeople(): Promise<{
        [key: string]: People[];
    }>;
    getTrialProspects(): Promise<{
        [key: string]: People[];
    }>;
    getInFutureProspects(page?: number, pageSize?: number, search?: string): Promise<{
        [key: string]: People[];
    }>;
    getConvertedProspects(page?: number, pageSize?: number, search?: string): Promise<{
        [key: string]: People[];
    }>;
    candidateStatsBySource(roleId: number): Promise<{
        source: string;
        total: number;
    }[]>;
    addPeopleInBulk(peopleDtos: PeopleDto[]): Promise<{
        message: string;
    }>;
    importPeopleFromCsv(filePath: string): Promise<{
        message: string;
    }>;
    private parseCsv;
    deleteAll(): Promise<void>;
    bdDashboard(): Promise<{
        sectorWiseProspects: {
            total: number;
            direct: number;
            sr: number;
        };
        prospectsOverview: {
            trialRecieved: number;
            trialSent: number;
            trialInProcess: number;
            trialResultsRecieved: number;
            trialPending: number;
            trialSuccessfull: number;
            trialFailed: number;
        };
        emailsData: {
            totalEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            autoRepliesEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            bouncedEmails: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
            potentialReplies: {
                current: number;
                lastWeek: number;
                percentChange: number;
            };
        };
    }>;
    searchCandidatesWithBooleanQuery(query: string): Promise<{
        message: string;
        results: People[];
    }>;
    searchCandidatesWithAdvancedFilters(query?: string, filters?: {
        titles: string[];
        locations: string[];
        skills: string[];
        companies: string[];
        industries: string[];
        keywords: string[];
        seniority: string[];
        hideViewed: boolean;
    }, page?: number, pageSize?: number): Promise<{
        message: string;
        results: People[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
}
