"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonPhoneDto = exports.PersonPhoneType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var PersonPhoneType;
(function (PersonPhoneType) {
    PersonPhoneType["PERSONAL"] = "PERSONAL";
    PersonPhoneType["BUSINESS"] = "BUSINESS";
})(PersonPhoneType || (exports.PersonPhoneType = PersonPhoneType = {}));
class PersonPhoneDto {
}
exports.PersonPhoneDto = PersonPhoneDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '+1234567890',
        description: 'The phone number of the person',
    }),
    (0, class_validator_1.IsPhoneNumber)(null),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PersonPhoneDto.prototype, "phone_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'PERSONAL',
        description: 'Type of phone number (PERSONAL or BUSINESS)',
        enum: PersonPhoneType,
    }),
    (0, class_validator_1.IsEnum)(PersonPhoneType),
    __metadata("design:type", String)
], PersonPhoneDto.prototype, "phone_type", void 0);
//# sourceMappingURL=person-phone.dto.js.map