import { PersonPhoneService } from './phone.service';
import { PersonPhone } from './phone.entity';
import { PersonPhoneDto } from './dto/person-phone.dto';
export declare class PersonPhoneController {
    private readonly personPhoneService;
    constructor(personPhoneService: PersonPhoneService);
    create(personId: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone>;
    findAll(): Promise<PersonPhone[]>;
    findOne(id: number): Promise<PersonPhone>;
    update(id: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone>;
    remove(id: number): Promise<void>;
}
