"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonPhoneController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const phone_service_1 = require("./phone.service");
const phone_entity_1 = require("./phone.entity");
const person_phone_dto_1 = require("./dto/person-phone.dto");
let PersonPhoneController = class PersonPhoneController {
    constructor(personPhoneService) {
        this.personPhoneService = personPhoneService;
    }
    async create(personId, personPhoneDto) {
        return this.personPhoneService.create(personId, personPhoneDto);
    }
    async findAll() {
        return this.personPhoneService.findAll();
    }
    async findOne(id) {
        return this.personPhoneService.findOne(id);
    }
    async update(id, personPhoneDto) {
        return this.personPhoneService.update(id, personPhoneDto);
    }
    async remove(id) {
        return this.personPhoneService.remove(id);
    }
};
exports.PersonPhoneController = PersonPhoneController;
__decorate([
    (0, common_1.Post)(':personId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a phone record for a person' }),
    (0, swagger_1.ApiParam)({ name: 'personId', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Phone record created',
        type: phone_entity_1.PersonPhone,
    }),
    __param(0, (0, common_1.Param)('personId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, person_phone_dto_1.PersonPhoneDto]),
    __metadata("design:returntype", Promise)
], PersonPhoneController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all phone records' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of phone records',
        type: [phone_entity_1.PersonPhone],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PersonPhoneController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a phone record by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Phone record found',
        type: phone_entity_1.PersonPhone,
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PersonPhoneController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a phone record' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Phone record updated',
        type: phone_entity_1.PersonPhone,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, person_phone_dto_1.PersonPhoneDto]),
    __metadata("design:returntype", Promise)
], PersonPhoneController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a phone record' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Phone record deleted' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PersonPhoneController.prototype, "remove", null);
exports.PersonPhoneController = PersonPhoneController = __decorate([
    (0, swagger_1.ApiTags)('Person Phone'),
    (0, common_1.Controller)('person-phone'),
    __metadata("design:paramtypes", [phone_service_1.PersonPhoneService])
], PersonPhoneController);
//# sourceMappingURL=phone.controller.js.map