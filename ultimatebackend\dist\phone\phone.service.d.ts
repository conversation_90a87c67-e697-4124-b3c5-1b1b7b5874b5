import { Repository } from 'typeorm';
import { People } from 'src/people/people.entity';
import { PersonPhone } from './phone.entity';
import { PersonPhoneDto } from './dto/person-phone.dto';
export declare class PersonPhoneService {
    private readonly personPhoneRepository;
    private readonly peopleRepository;
    constructor(personPhoneRepository: Repository<PersonPhone>, peopleRepository: Repository<People>);
    create(personId: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone>;
    findAll(): Promise<PersonPhone[]>;
    findOne(id: number): Promise<PersonPhone>;
    update(id: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone>;
    remove(id: number): Promise<void>;
}
