"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonPhoneService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const people_entity_1 = require("../people/people.entity");
const phone_entity_1 = require("./phone.entity");
let PersonPhoneService = class PersonPhoneService {
    constructor(personPhoneRepository, peopleRepository) {
        this.personPhoneRepository = personPhoneRepository;
        this.peopleRepository = peopleRepository;
    }
    async create(personId, personPhoneDto) {
        const person = await this.peopleRepository.findOne({ where: { id: personId } });
        if (!person) {
            throw new Error('Person not found');
        }
        const personPhone = this.personPhoneRepository.create({
            ...personPhoneDto,
            person,
        });
        return this.personPhoneRepository.save(personPhone);
    }
    async findAll() {
        return this.personPhoneRepository.find({ relations: ['person'] });
    }
    async findOne(id) {
        return this.personPhoneRepository.findOne({ where: { id }, relations: ['person'] });
    }
    async update(id, personPhoneDto) {
        await this.personPhoneRepository.update(id, personPhoneDto);
        return this.findOne(id);
    }
    async remove(id) {
        await this.personPhoneRepository.delete(id);
    }
};
exports.PersonPhoneService = PersonPhoneService;
exports.PersonPhoneService = PersonPhoneService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __param(1, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PersonPhoneService);
//# sourceMappingURL=phone.service.js.map