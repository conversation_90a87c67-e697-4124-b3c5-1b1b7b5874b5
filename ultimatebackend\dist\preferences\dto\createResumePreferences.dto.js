"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobPreferencesDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class JobPreferencesDTO {
}
exports.JobPreferencesDTO = JobPreferencesDTO;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Software Engineer',
        description: 'Preferred job title',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'New York, USA',
        description: 'Preferred job location',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Full-Time',
        description: 'Preferred job type',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'IT',
        description: 'Preferred industry',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_industry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '3-5 years',
        description: 'Preferred job experience',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_experience", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '$80,000',
        description: 'Preferred job salary',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_salary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Java, React, AWS',
        description: 'Preferred job skills (comma-separated)',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_skills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 60000,
        description: 'Salary range start',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], JobPreferencesDTO.prototype, "salary_range_start", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 100000,
        description: 'Salary range end',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], JobPreferencesDTO.prototype, "salary_range_end", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'USD',
        description: 'Salary currency',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "salary_currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'ANNUALLY',
        description: 'Salary period',
        enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'ANNUALLY'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['DAILY', 'WEEKLY', 'MONTHLY', 'ANNUALLY']),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "salary_period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '10001',
        description: 'Job location ZIP code',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location_zip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'USA',
        description: 'Job location country',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location_country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'New York',
        description: 'Job location city',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location_city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'NY',
        description: 'Job location state',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location_state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '1234 Elm Street',
        description: 'Job location address',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "job_location_address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'IMMEDIATE',
        description: 'Availability',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'RELOCATE',
        description: 'Reason for leaving',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], JobPreferencesDTO.prototype, "reason_for_leaving", void 0);
//# sourceMappingURL=createResumePreferences.dto.js.map