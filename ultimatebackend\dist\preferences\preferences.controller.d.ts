import { PreferencesService } from './preferences.service';
import { JobPreferencesDTO } from './dto/createResumePreferences.dto';
export declare class PreferencesController {
    private readonly preferencesService;
    constructor(preferencesService: PreferencesService);
    createPreference(body: JobPreferencesDTO): Promise<import("./prefrences.entity").JobPreferences>;
    updatePreference(id: number, body: JobPreferencesDTO): Promise<import("./prefrences.entity").JobPreferences>;
    deletePreference(id: number): Promise<import("./prefrences.entity").JobPreferences>;
    findAll(page?: number, pageSize?: number, searchString?: string): Promise<import("./prefrences.entity").JobPreferences[]>;
    findOne(id: number): Promise<import("./prefrences.entity").JobPreferences>;
}
