"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferencesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const preferences_service_1 = require("./preferences.service");
const createResumePreferences_dto_1 = require("./dto/createResumePreferences.dto");
let PreferencesController = class PreferencesController {
    constructor(preferencesService) {
        this.preferencesService = preferencesService;
    }
    async createPreference(body) {
        return this.preferencesService.createPreferences(body);
    }
    async updatePreference(id, body) {
        return this.preferencesService.updatePreferences(id, body);
    }
    async deletePreference(id) {
        return this.preferencesService.deletePreferences(id);
    }
    async findAll(page, pageSize, searchString) {
        return this.preferencesService.getAllPreferences(page, pageSize, searchString);
    }
    async findOne(id) {
        return this.preferencesService.getPreferencesById(id);
    }
};
exports.PreferencesController = PreferencesController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new preference' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createResumePreferences_dto_1.JobPreferencesDTO]),
    __metadata("design:returntype", Promise)
], PreferencesController.prototype, "createPreference", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a preference' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, createResumePreferences_dto_1.JobPreferencesDTO]),
    __metadata("design:returntype", Promise)
], PreferencesController.prototype, "updatePreference", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a preference' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PreferencesController.prototype, "deletePreference", null);
__decorate([
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all preferences' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'pageSize',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchString',
        required: false,
        type: String,
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('searchString')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], PreferencesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('find/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a preference by id' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PreferencesController.prototype, "findOne", null);
exports.PreferencesController = PreferencesController = __decorate([
    (0, common_1.Controller)('preferences'),
    (0, swagger_1.ApiTags)('preferences'),
    __metadata("design:paramtypes", [preferences_service_1.PreferencesService])
], PreferencesController);
//# sourceMappingURL=preferences.controller.js.map