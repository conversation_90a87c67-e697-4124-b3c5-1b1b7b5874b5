import { JobPreferences } from './prefrences.entity';
import { Repository } from 'typeorm';
import { JobPreferencesDTO } from './dto/createResumePreferences.dto';
export declare class PreferencesService {
    private preferencesRepository;
    constructor(preferencesRepository: Repository<JobPreferences>);
    createPreferences(data: JobPreferencesDTO): Promise<JobPreferences>;
    updatePreferences(id: number, data: JobPreferencesDTO): Promise<JobPreferences>;
    deletePreferences(id: number): Promise<JobPreferences>;
    getPreferencesById(id: number): Promise<JobPreferences>;
    getAllPreferences(page?: number, pageSize?: number, searchString?: string): Promise<JobPreferences[]>;
}
