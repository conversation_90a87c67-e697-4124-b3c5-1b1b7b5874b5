"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferencesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const prefrences_entity_1 = require("./prefrences.entity");
const typeorm_2 = require("typeorm");
let PreferencesService = class PreferencesService {
    constructor(preferencesRepository) {
        this.preferencesRepository = preferencesRepository;
    }
    async createPreferences(data) {
        try {
            const preferences = this.preferencesRepository.create(data);
            await this.preferencesRepository.save(preferences);
            return preferences;
        }
        catch (error) {
            console.error('Error creating preferences:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to create preferences: ${error.message}`);
        }
    }
    async updatePreferences(id, data) {
        try {
            const preferences = await this.preferencesRepository.findOne({
                where: { id },
            });
            if (!preferences) {
                throw new common_1.NotFoundException(`Preferences with ID ${id} not found`);
            }
            await this.preferencesRepository.update({ id }, data);
            return { ...preferences, ...data };
        }
        catch (error) {
            console.error('Error updating preferences:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to update preferences: ${error.message}`);
        }
    }
    async deletePreferences(id) {
        try {
            const preferences = await this.preferencesRepository.findOne({
                where: { id },
            });
            if (!preferences) {
                throw new common_1.NotFoundException(`Preferences with ID ${id} not found`);
            }
            await this.preferencesRepository.delete(id);
            return preferences;
        }
        catch (error) {
            console.error('Error deleting preferences:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to delete preferences: ${error.message}`);
        }
    }
    async getPreferencesById(id) {
        try {
            const preferences = await this.preferencesRepository.findOne({
                where: { id },
            });
            if (!preferences) {
                throw new common_1.NotFoundException(`Preferences with ID ${id} not found`);
            }
            return preferences;
        }
        catch (error) {
            console.error('Error getting preferences:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to get preferences: ${error.message}`);
        }
    }
    async getAllPreferences(page = 0, pageSize = 10, searchString = '') {
        try {
            const preferences = await this.preferencesRepository.find({
                where: { job_title: searchString },
                take: pageSize,
                skip: page * pageSize,
            });
            if (!preferences.length) {
                throw new common_1.NotFoundException('No preferences found');
            }
            return preferences;
        }
        catch (error) {
            console.error('Error getting preferences:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to get preferences: ${error.message}`);
        }
    }
};
exports.PreferencesService = PreferencesService;
exports.PreferencesService = PreferencesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(prefrences_entity_1.JobPreferences)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PreferencesService);
//# sourceMappingURL=preferences.service.js.map