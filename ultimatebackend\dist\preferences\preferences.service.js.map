{"version": 3, "file": "preferences.service.js", "sourceRoot": "", "sources": ["../../src/preferences/preferences.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,6CAAmD;AACnD,2DAAqD;AACrD,qCAAqC;AAI9B,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,YAEU,qBAAiD;QAAjD,0BAAqB,GAArB,qBAAqB,CAA4B;IACxD,CAAC;IAEJ,KAAK,CAAC,iBAAiB,CAAC,IAAuB;QAC7C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CACpC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,EAAU,EACV,IAAuB;QAEvB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YACtD,OAAO,EAAE,GAAG,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CACpC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EAAU;QAChC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;YACrE,CAAC;YACD,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5C,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,IAAI,qCAA4B,CACpC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACjC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;YACrE,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,IAAI,qCAA4B,CACpC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,eAAuB,EAAE;QAEzB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;gBACxD,KAAK,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE;gBAClC,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,IAAI,GAAG,QAAQ;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBACxB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;YACtD,CAAC;YACD,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,IAAI,qCAA4B,CACpC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAC9C,CAAC;QACJ,CAAC;IACH,CAAC;CAoBF,CAAA;AArHY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kCAAc,CAAC,CAAA;qCACF,oBAAU;GAHhC,kBAAkB,CAqH9B"}