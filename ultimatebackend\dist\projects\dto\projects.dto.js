"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class ProjectDto {
}
exports.ProjectDto = ProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique identifier of the project',
        example: 1
    }),
    __metadata("design:type", Number)
], ProjectDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the project',
        example: 'E-commerce Website Development',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ProjectDto.prototype, "project_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The duration of the project',
        example: '6 months',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ProjectDto.prototype, "project_duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The associated entity or organization for the project',
        example: 'ABC Company',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ProjectDto.prototype, "project_associated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of skills required for the project',
        example: ['React', 'Node.js', 'TypeScript'],
        required: false,
        type: [String]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], ProjectDto.prototype, "project_skill", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detailed description of the project',
        example: 'A full-stack e-commerce platform with payment integration and inventory management',
        required: false
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ProjectDto.prototype, "project_description", void 0);
//# sourceMappingURL=projects.dto.js.map