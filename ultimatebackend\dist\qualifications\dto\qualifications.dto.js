"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QualificationsDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class QualificationsDto {
}
exports.QualificationsDto = QualificationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Bachelor of Science in Computer Science',
        description: 'The name of the title or qualification',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2023-01-01',
        description: 'The date the qualification was issued',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2023-01-01',
        description: 'The date the qualification was issued',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Harvard University',
        description: 'The institution that issued the qualification',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "institution", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'CERTIFICATE',
        description: 'The type of qualification (e.g., CERTIFICATE, DIPLOMA, DEGREE)',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "qualification_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'A brief description of the qualification',
        description: 'A brief description of the qualification',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: 'Whether the qualification is verified',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QualificationsDto.prototype, "is_verified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: 'Whether the qualification is current',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QualificationsDto.prototype, "is_current", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2025-01-01',
        description: 'The date the qualification expires',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "date_expiry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Harvard University',
        description: 'The entity that issued the qualification',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], QualificationsDto.prototype, "issued_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'The user id of the user that owns the qualification',
        type: Number,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], QualificationsDto.prototype, "personId", void 0);
//# sourceMappingURL=qualifications.dto.js.map