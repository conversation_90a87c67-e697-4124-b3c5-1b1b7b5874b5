import { QualificationsService } from './qualifications.service';
import { QualificationsDto } from './dto/qualifications.dto';
export declare class QualificationsController {
    private readonly qualificationsService;
    constructor(qualificationsService: QualificationsService);
    create(qualifications: QualificationsDto): Promise<any>;
    getAll(page: number, pageSize: number, searchString: string): Promise<any>;
    getOne(id: number): Promise<any>;
    update(id: number, qualifications: QualificationsDto): Promise<any>;
    delete(id: number): Promise<any>;
}
