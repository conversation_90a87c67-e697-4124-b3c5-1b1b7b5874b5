import { Qualifications } from './qualifications.entity';
import { QualificationsDto } from './dto/qualifications.dto';
export declare class QualificationsService {
    private qualificationsRepository;
    create(qualifications: QualificationsDto): Promise<Qualifications>;
    findAll(page?: number, pageSize?: number, searchString?: string): Promise<Qualifications[]>;
    findOne(id: number): Promise<Qualifications>;
    update(id: number, qualifications: QualificationsDto): Promise<Qualifications>;
    remove(id: number): Promise<void>;
    findByCandidateId(candidateId: number): Promise<Qualifications[]>;
}
