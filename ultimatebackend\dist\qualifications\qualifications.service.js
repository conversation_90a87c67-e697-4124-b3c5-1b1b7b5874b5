"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QualificationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const qualifications_entity_1 = require("./qualifications.entity");
const typeorm_2 = require("typeorm");
let QualificationsService = class QualificationsService {
    async create(qualifications) {
        try {
            const newQualifications = this.qualificationsRepository.create(qualifications);
            return await this.qualificationsRepository.save(newQualifications);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to create qualification.',
                error: error.message,
            });
        }
    }
    async findAll(page = 0, pageSize = 10, searchString = '') {
        try {
            if (searchString === '') {
                return await this.qualificationsRepository.find({
                    order: { title: 'ASC' },
                    skip: page * pageSize,
                    take: pageSize,
                });
            }
            else {
                return await this.qualificationsRepository.find({
                    where: { title: (0, typeorm_2.Like)(`%${searchString}%`) },
                    order: { title: 'ASC' },
                    skip: page * pageSize,
                    take: pageSize,
                });
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to retrieve qualifications.',
                error: error.message,
            });
        }
    }
    async findOne(id) {
        try {
            const qualification = await this.qualificationsRepository.findOne({
                where: { id },
            });
            if (!qualification) {
                throw new common_1.NotFoundException('Qualification not found.');
            }
            return qualification;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException({
                message: 'Failed to retrieve qualification.',
                error: error.message,
            });
        }
    }
    async update(id, qualifications) {
        try {
            const result = await this.qualificationsRepository.update({ id }, qualifications);
            if (result.affected === 0) {
                throw new common_1.NotFoundException('Qualification not found.');
            }
            return await this.findOne(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException({
                message: 'Failed to update qualification.',
                error: error.message,
            });
        }
    }
    async remove(id) {
        try {
            const result = await this.qualificationsRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.NotFoundException('Qualification not found.');
            }
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException({
                message: 'Failed to delete qualification.',
                error: error.message,
            });
        }
    }
    async findByCandidateId(candidateId) {
        try {
            return await this.qualificationsRepository.find({
                where: { personId: candidateId },
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Failed to retrieve qualifications by candidate ID.',
                error: error.message,
            });
        }
    }
};
exports.QualificationsService = QualificationsService;
__decorate([
    (0, typeorm_1.InjectRepository)(qualifications_entity_1.Qualifications),
    __metadata("design:type", typeorm_2.Repository)
], QualificationsService.prototype, "qualificationsRepository", void 0);
exports.QualificationsService = QualificationsService = __decorate([
    (0, common_1.Injectable)()
], QualificationsService);
//# sourceMappingURL=qualifications.service.js.map