import { Job } from 'bull';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
export declare class CallQueueProcessor {
    private readonly candidateSequenceStatusService;
    private readonly logger;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService);
    handleMakeCall(job: Job<QueueJobData>): Promise<void>;
    private makeCall;
}
