"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CallQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallQueueProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const queue_constants_1 = require("../queue.constants");
const candidate_sequence_status_service_1 = require("../../candidate-sequence-status/candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("../../candidate-sequence-status/candidate-sequence-status.entity");
let CallQueueProcessor = CallQueueProcessor_1 = class CallQueueProcessor {
    constructor(candidateSequenceStatusService) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.logger = new common_1.Logger(CallQueueProcessor_1.name);
    }
    async handleMakeCall(job) {
        const { candidateSequenceStatusId, candidateId, stepId, recipientPhone, body } = job.data;
        this.logger.log(`Processing call job for candidate ${candidateId}, step ${stepId}`);
        try {
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED);
            await this.makeCall(recipientPhone, body);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.SENT, {
                calledNumber: recipientPhone,
                initiatedAt: new Date().toISOString(),
            });
            this.logger.log(`Call initiated successfully for candidate ${candidateId}, step ${stepId}`);
            setTimeout(async () => {
                try {
                    const callOutcome = Math.random() > 0.3 ? 'answered' : 'no_answer';
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, callOutcome === 'answered' ? candidate_sequence_status_entity_1.SequenceStepStatus.COMPLETED : candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED, {
                        callOutcome,
                        callDuration: callOutcome === 'answered' ? Math.floor(Math.random() * 300) + 30 : 0,
                    });
                    this.logger.log(`Call ${callOutcome} for candidate ${candidateId}, step ${stepId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update call status: ${error.message}`);
                }
            }, 10000);
        }
        catch (error) {
            this.logger.error(`Failed to make call for candidate ${candidateId}: ${error.message}`);
            await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.FAILED, {
                error: error.message,
                failedAt: new Date().toISOString(),
            });
            throw error;
        }
    }
    async makeCall(to, script) {
        this.logger.log(`Making call to: ${to}`);
        this.logger.log(`Call script: ${script?.substring(0, 100)}...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        if (Math.random() < 0.1) {
            throw new Error('Simulated call service failure');
        }
        this.logger.log('Call initiated successfully (mock)');
    }
};
exports.CallQueueProcessor = CallQueueProcessor;
__decorate([
    (0, bull_1.Process)('make-call'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CallQueueProcessor.prototype, "handleMakeCall", null);
exports.CallQueueProcessor = CallQueueProcessor = CallQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(queue_constants_1.QUEUE_NAMES.CALL),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService])
], CallQueueProcessor);
//# sourceMappingURL=call-queue.processor.js.map