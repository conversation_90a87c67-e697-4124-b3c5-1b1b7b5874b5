import { OnModuleInit } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { QueueJobData } from '../queue.service';
import { EmailService } from '../../email/email.service';
import { CandidateSequenceStatusService } from '../../candidate-sequence-status/candidate-sequence-status.service';
export declare class EmailQueueProcessor implements OnModuleInit {
    private readonly emailService;
    private readonly candidateSequenceStatusService;
    private readonly emailQueue;
    private readonly logger;
    constructor(emailService: EmailService, candidateSequenceStatusService: CandidateSequenceStatusService, emailQueue: Queue);
    onModuleInit(): Promise<void>;
    handleSendEmail(job: Job<QueueJobData>): Promise<{
        success: boolean;
        candidateId: number;
        stepId: number;
    }>;
}
