"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EmailQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const queue_constants_1 = require("../queue.constants");
const email_service_1 = require("../../email/email.service");
const candidate_sequence_status_service_1 = require("../../candidate-sequence-status/candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("../../candidate-sequence-status/candidate-sequence-status.entity");
let EmailQueueProcessor = EmailQueueProcessor_1 = class EmailQueueProcessor {
    constructor(emailService, candidateSequenceStatusService, emailQueue) {
        this.emailService = emailService;
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.emailQueue = emailQueue;
        this.logger = new common_1.Logger(EmailQueueProcessor_1.name);
    }
    async onModuleInit() {
        this.logger.log('EmailQueueProcessor initialized');
        console.log('🚀 EMAIL PROCESSOR: EmailQueueProcessor initialized');
        try {
            const waiting = await this.emailQueue.getWaiting();
            const active = await this.emailQueue.getActive();
            const completed = await this.emailQueue.getCompleted();
            const failed = await this.emailQueue.getFailed();
            console.log('🚀 EMAIL PROCESSOR: Queue stats:', {
                waiting: waiting.length,
                active: active.length,
                completed: completed.length,
                failed: failed.length
            });
            const isPaused = await this.emailQueue.isPaused();
            console.log('🚀 EMAIL PROCESSOR: Queue is paused:', isPaused);
            if (isPaused) {
                console.log('� EMAIL PROCESSOR: Resuming paused queue...');
                await this.emailQueue.resume();
            }
            const redisClient = this.emailQueue.client;
            if (redisClient) {
                console.log('� EMAIL PROCESSOR: Redis client status:', redisClient.status);
            }
            try {
                await this.emailQueue.clean(0, 'failed');
                await this.emailQueue.clean(0, 'completed');
                console.log('� EMAIL PROCESSOR: Cleaned old failed and completed jobs');
            }
            catch (cleanError) {
                console.log('🚀 EMAIL PROCESSOR: Error cleaning jobs:', cleanError.message);
            }
            console.log('� EMAIL PROCESSOR: Initialization completed successfully');
        }
        catch (error) {
            console.error('� EMAIL PROCESSOR: Error during initialization:', error);
            this.logger.error('Error during initialization:', error);
        }
    }
    async handleSendEmail(job) {
        const { candidateSequenceStatusId, candidateId, stepId, recipientEmail } = job.data;
        this.logger.log(`Processing email job ${job.id} for candidate ${candidateId}, step ${stepId}`);
        console.log(`🔥 EMAIL PROCESSOR: Processing job ${job.id} for candidate ${candidateId}, step ${stepId}`);
        if (!candidateSequenceStatusId) {
            const error = 'candidateSequenceStatusId is required';
            this.logger.error(error);
            throw new Error(error);
        }
        if (!recipientEmail) {
            const error = 'recipientEmail is required';
            this.logger.error(error);
            throw new Error(error);
        }
        if (!candidateId || !stepId) {
            const error = 'candidateId and stepId are required';
            this.logger.error(error);
            throw new Error(error);
        }
        try {
            console.log(`🔥 EMAIL PROCESSOR: Verifying candidate sequence status ${candidateSequenceStatusId} exists...`);
            try {
                await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED);
                console.log(`🔥 EMAIL PROCESSOR: ✅ Updated status to QUEUED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
            }
            catch (statusError) {
                this.logger.error(`Candidate sequence status ${candidateSequenceStatusId} not found: ${statusError.message}`);
                console.error(`🔥 EMAIL PROCESSOR: ❌ Candidate sequence status ${candidateSequenceStatusId} not found: ${statusError.message}`);
                console.log(`🔥 EMAIL PROCESSOR: Job data for debugging:`, {
                    candidateSequenceStatusId,
                    candidateId,
                    stepId,
                    recipientEmail,
                    jobId: job.id
                });
                throw new Error(`Candidate sequence status ${candidateSequenceStatusId} not found. This may indicate a data consistency issue.`);
            }
            const dummySubject = `Test Email - Sequence Step ${stepId}`;
            const dummyBody = `
        <h2>Test Email from Sequence Automation</h2>
        <p>Hello,</p>
        <p>This is a test email sent from our sequence automation system.</p>
        <p><strong>Details:</strong></p>
        <ul>
          <li>Candidate ID: ${candidateId}</li>
          <li>Step ID: ${stepId}</li>
          <li>Sent to: ${recipientEmail}</li>
          <li>Sent at: ${new Date().toISOString()}</li>
        </ul>
        <p>Best regards,<br>Ultimate Outsourcing Team</p>
      `;
            await this.emailService.sendSimpleEmail(recipientEmail, dummySubject, dummyBody);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.SENT, {
                sentTo: recipientEmail,
                sentAt: new Date().toISOString(),
            });
            this.logger.log(`✅ Email sent successfully for candidate ${candidateId}, step ${stepId}`);
            console.log(`✅ EMAIL PROCESSOR: Email sent successfully for candidate ${candidateId}, step ${stepId}`);
            setTimeout(async () => {
                try {
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED);
                    this.logger.log(`Email delivery confirmed for candidate ${candidateId}, step ${stepId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update delivery status: ${error.message}`);
                }
            }, 1000);
            return { success: true, candidateId, stepId };
        }
        catch (error) {
            const errorContext = {
                jobId: job.id,
                candidateId,
                stepId,
                candidateSequenceStatusId,
                recipientEmail,
                errorType: error.constructor.name,
                errorMessage: error.message,
                errorStack: error.stack,
                timestamp: new Date().toISOString(),
            };
            this.logger.error(`❌ Failed to send email for candidate ${candidateId}:`, errorContext);
            console.log(`❌ EMAIL PROCESSOR: Failed to send email for candidate ${candidateId}:`, {
                error: error.message,
                type: error.constructor.name,
                jobId: job.id,
            });
            let errorCategory = 'UNKNOWN';
            if (error.message.includes('Candidate sequence status') && error.message.includes('not found')) {
                errorCategory = 'DATA_INTEGRITY';
            }
            else if (error.message.includes('Email') || error.message.includes('SMTP')) {
                errorCategory = 'EMAIL_SERVICE';
            }
            else if (error.message.includes('required')) {
                errorCategory = 'VALIDATION';
            }
            if (candidateSequenceStatusId) {
                try {
                    await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.FAILED, {
                        error: error.message,
                        errorCategory,
                        errorType: error.constructor.name,
                        failedAt: new Date().toISOString(),
                        jobId: job.id,
                    });
                    this.logger.log(`Updated status to FAILED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
                }
                catch (statusError) {
                    this.logger.error(`Critical: Failed to update status for candidateSequenceStatusId ${candidateSequenceStatusId}:`, {
                        statusError: statusError.message,
                        originalError: error.message,
                        candidateId,
                        stepId,
                    });
                }
            }
            else {
                this.logger.warn(`Cannot update status - candidateSequenceStatusId is missing for candidate ${candidateId}, step ${stepId}`);
            }
            throw error;
        }
    }
};
exports.EmailQueueProcessor = EmailQueueProcessor;
__decorate([
    (0, bull_1.Process)('send-email'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailQueueProcessor.prototype, "handleSendEmail", null);
exports.EmailQueueProcessor = EmailQueueProcessor = EmailQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(queue_constants_1.QUEUE_NAMES.EMAIL),
    __param(2, (0, bull_1.InjectQueue)(queue_constants_1.QUEUE_NAMES.EMAIL)),
    __metadata("design:paramtypes", [email_service_1.EmailService,
        candidate_sequence_status_service_1.CandidateSequenceStatusService, Object])
], EmailQueueProcessor);
//# sourceMappingURL=email-queue.processor.js.map