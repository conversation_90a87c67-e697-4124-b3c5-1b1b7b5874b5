{"version": 3, "file": "email-queue.processor.js", "sourceRoot": "", "sources": ["../../../src/queue/processors/email-queue.processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,uCAA+D;AAC/D,2CAAkE;AAElE,wDAAiD;AAEjD,6DAAyD;AACzD,yHAAmH;AACnH,uHAAsG;AAI/F,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAG9B,YACmB,YAA0B,EAC1B,8BAA8D,EAC/C,UAAkC;QAFjD,iBAAY,GAAZ,YAAY,CAAc;QAC1B,mCAA8B,GAA9B,8BAA8B,CAAgC;QAC9B,eAAU,GAAV,UAAU,CAAO;QALnD,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAM5D,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACjD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAEjD,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE;gBAC9C,OAAO,EAAE,OAAO,CAAC,MAAM;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,SAAS,EAAE,SAAS,CAAC,MAAM;gBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,QAAQ,CAAC,CAAC;YAG9D,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAC3D,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACjC,CAAC;YAGD,MAAM,WAAW,GAAI,IAAI,CAAC,UAAkB,CAAC,MAAM,CAAC;YACpD,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7E,CAAC;YAGD,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACzC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;YAC1E,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YAC9E,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QAE1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACxE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe,CAAC,GAAsB;QAC1C,MAAM,EAAE,yBAAyB,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAEpF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,GAAG,CAAC,EAAE,kBAAkB,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;QAC/F,OAAO,CAAC,GAAG,CAAC,sCAAsC,GAAG,CAAC,EAAE,kBAAkB,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;QAGzG,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,uCAAuC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,4BAA4B,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,qCAAqC,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,2DAA2D,yBAAyB,YAAY,CAAC,CAAC;YAE9G,IAAI,CAAC;gBAEH,MAAM,IAAI,CAAC,8BAA8B,CAAC,YAAY,CACpD,yBAAyB,EACzB,qDAAkB,CAAC,MAAM,CAC1B,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,gFAAgF,yBAAyB,EAAE,CAAC,CAAC;YAC3H,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBAErB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,yBAAyB,eAAe,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9G,OAAO,CAAC,KAAK,CAAC,mDAAmD,yBAAyB,eAAe,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBAGhI,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE;oBACzD,yBAAyB;oBACzB,WAAW;oBACX,MAAM;oBACN,cAAc;oBACd,KAAK,EAAE,GAAG,CAAC,EAAE;iBACd,CAAC,CAAC;gBAEH,MAAM,IAAI,KAAK,CAAC,6BAA6B,yBAAyB,yDAAyD,CAAC,CAAC;YACnI,CAAC;YAGD,MAAM,YAAY,GAAG,8BAA8B,MAAM,EAAE,CAAC;YAC5D,MAAM,SAAS,GAAG;;;;;;8BAMM,WAAW;yBAChB,MAAM;yBACN,cAAc;yBACd,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;;;OAG1C,CAAC;YAEF,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CACrC,cAAc,EACd,YAAY,EACZ,SAAS,CACV,CAAC;YAGF,MAAM,IAAI,CAAC,8BAA8B,CAAC,YAAY,CACpD,yBAAyB,EACzB,qDAAkB,CAAC,IAAI,EACvB;gBACE,MAAM,EAAE,cAAc;gBACtB,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACjC,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;YAC1F,OAAO,CAAC,GAAG,CAAC,4DAA4D,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;YAGvG,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,8BAA8B,CAAC,YAAY,CACpD,yBAAyB,EACzB,qDAAkB,CAAC,SAAS,CAC7B,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;gBAC3F,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;QAEhD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,GAAG,CAAC,EAAE;gBACb,WAAW;gBACX,MAAM;gBACN,yBAAyB;gBACzB,cAAc;gBACd,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;gBACjC,YAAY,EAAE,KAAK,CAAC,OAAO;gBAC3B,UAAU,EAAE,KAAK,CAAC,KAAK;gBACvB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,WAAW,GAAG,EAAE,YAAY,CAAC,CAAC;YACxF,OAAO,CAAC,GAAG,CAAC,yDAAyD,WAAW,GAAG,EAAE;gBACnF,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;gBAC5B,KAAK,EAAE,GAAG,CAAC,EAAE;aACd,CAAC,CAAC;YAGH,IAAI,aAAa,GAAG,SAAS,CAAC;YAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,2BAA2B,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/F,aAAa,GAAG,gBAAgB,CAAC;YACnC,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7E,aAAa,GAAG,eAAe,CAAC;YAClC,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9C,aAAa,GAAG,YAAY,CAAC;YAC/B,CAAC;YAGD,IAAI,yBAAyB,EAAE,CAAC;gBAC9B,IAAI,CAAC;oBAEH,MAAM,IAAI,CAAC,8BAA8B,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,CAAC;oBAE3F,MAAM,IAAI,CAAC,8BAA8B,CAAC,YAAY,CACpD,yBAAyB,EACzB,qDAAkB,CAAC,MAAM,EACzB;wBACE,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,aAAa;wBACb,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI;wBACjC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBAClC,KAAK,EAAE,GAAG,CAAC,EAAE;qBACd,CACF,CAAC;oBAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0DAA0D,yBAAyB,EAAE,CAAC,CAAC;gBACzG,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mEAAmE,yBAAyB,GAAG,EAAE;wBACjH,WAAW,EAAE,WAAW,CAAC,OAAO;wBAChC,aAAa,EAAE,KAAK,CAAC,OAAO;wBAC5B,WAAW;wBACX,MAAM;qBACP,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6EAA6E,WAAW,UAAU,MAAM,EAAE,CAAC,CAAC;YAC/H,CAAC;YAGD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAGF,CAAA;AAxOY,kDAAmB;AA6DxB;IADL,IAAA,cAAO,EAAC,YAAY,CAAC;;;;0DAyKrB;8BArOU,mBAAmB;IAF/B,IAAA,mBAAU,GAAE;IACZ,IAAA,gBAAS,EAAC,6BAAW,CAAC,KAAK,CAAC;IAOxB,WAAA,IAAA,kBAAW,EAAC,6BAAW,CAAC,KAAK,CAAC,CAAA;qCAFA,4BAAY;QACM,kEAA8B;GALtE,mBAAmB,CAwO/B"}