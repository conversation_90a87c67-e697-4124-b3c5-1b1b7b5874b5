import { Job } from 'bull';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
export declare class LinkedInQueueProcessor {
    private readonly candidateSequenceStatusService;
    private readonly logger;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService);
    handleSendLinkedIn(job: Job<QueueJobData>): Promise<void>;
    private sendLinkedInMessage;
}
