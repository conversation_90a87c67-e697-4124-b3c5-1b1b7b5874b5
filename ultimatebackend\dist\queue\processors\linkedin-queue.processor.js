"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LinkedInQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkedInQueueProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const queue_constants_1 = require("../queue.constants");
const candidate_sequence_status_service_1 = require("../../candidate-sequence-status/candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("../../candidate-sequence-status/candidate-sequence-status.entity");
let LinkedInQueueProcessor = LinkedInQueueProcessor_1 = class LinkedInQueueProcessor {
    constructor(candidateSequenceStatusService) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.logger = new common_1.Logger(LinkedInQueueProcessor_1.name);
    }
    async handleSendLinkedIn(job) {
        const { candidateSequenceStatusId, candidateId, stepId, recipientLinkedIn, subject, body } = job.data;
        this.logger.log(`Processing LinkedIn job for candidate ${candidateId}, step ${stepId}`);
        try {
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED);
            await this.sendLinkedInMessage(recipientLinkedIn, subject, body);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.SENT, {
                sentTo: recipientLinkedIn,
                subject,
                sentAt: new Date().toISOString(),
            });
            this.logger.log(`LinkedIn message sent successfully for candidate ${candidateId}, step ${stepId}`);
            setTimeout(async () => {
                try {
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED);
                    this.logger.log(`LinkedIn delivery confirmed for candidate ${candidateId}, step ${stepId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update delivery status: ${error.message}`);
                }
            }, 4000);
        }
        catch (error) {
            this.logger.error(`Failed to send LinkedIn message for candidate ${candidateId}: ${error.message}`);
            await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.FAILED, {
                error: error.message,
                failedAt: new Date().toISOString(),
            });
            throw error;
        }
    }
    async sendLinkedInMessage(profileUrl, subject, message) {
        this.logger.log(`Sending LinkedIn message to: ${profileUrl}`);
        this.logger.log(`Subject: ${subject}`);
        this.logger.log(`Message: ${message?.substring(0, 100)}...`);
        await new Promise(resolve => setTimeout(resolve, 1500));
        if (Math.random() < 0.08) {
            throw new Error('Simulated LinkedIn service failure');
        }
        this.logger.log('LinkedIn message sent successfully (mock)');
    }
};
exports.LinkedInQueueProcessor = LinkedInQueueProcessor;
__decorate([
    (0, bull_1.Process)('send-linkedin'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LinkedInQueueProcessor.prototype, "handleSendLinkedIn", null);
exports.LinkedInQueueProcessor = LinkedInQueueProcessor = LinkedInQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(queue_constants_1.QUEUE_NAMES.LINKEDIN),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService])
], LinkedInQueueProcessor);
//# sourceMappingURL=linkedin-queue.processor.js.map