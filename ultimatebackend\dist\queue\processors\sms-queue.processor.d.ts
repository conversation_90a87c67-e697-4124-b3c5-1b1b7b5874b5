import { Job } from 'bull';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { TwillioService } from '../../twillio/twillio.service';
export declare class SmsQueueProcessor {
    private readonly candidateSequenceStatusService;
    private readonly twillioService;
    private readonly logger;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService, twillioService: TwillioService);
    handleSendSms(job: Job<QueueJobData>): Promise<void>;
}
