"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SmsQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsQueueProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const queue_constants_1 = require("../queue.constants");
const candidate_sequence_status_service_1 = require("../../candidate-sequence-status/candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("../../candidate-sequence-status/candidate-sequence-status.entity");
const twillio_service_1 = require("../../twillio/twillio.service");
let SmsQueueProcessor = SmsQueueProcessor_1 = class SmsQueueProcessor {
    constructor(candidateSequenceStatusService, twillioService) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.twillioService = twillioService;
        this.logger = new common_1.Logger(SmsQueueProcessor_1.name);
    }
    async handleSendSms(job) {
        const { candidateSequenceStatusId, candidateId, stepId, recipientPhone } = job.data;
        this.logger.log(`Processing SMS job for candidate ${candidateId}, step ${stepId}`);
        try {
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED);
            const dummyMessage = `Test SMS: Sequence automation for candidate ${candidateId}, step ${stepId}. Sent at ${new Date().toISOString()}`;
            const result = await this.twillioService.sendSMSMessage(recipientPhone, dummyMessage);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.SENT, {
                sentTo: recipientPhone,
                sentAt: new Date().toISOString(),
                messageSid: result.sid,
            });
            this.logger.log(`SMS sent successfully for candidate ${candidateId}, step ${stepId}`);
            setTimeout(async () => {
                try {
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED);
                    this.logger.log(`SMS delivery confirmed for candidate ${candidateId}, step ${stepId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update delivery status: ${error.message}`);
                }
            }, 2000);
        }
        catch (error) {
            this.logger.error(`Failed to send SMS for candidate ${candidateId}: ${error.message}`);
            await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.FAILED, {
                error: error.message,
                failedAt: new Date().toISOString(),
            });
            throw error;
        }
    }
};
exports.SmsQueueProcessor = SmsQueueProcessor;
__decorate([
    (0, bull_1.Process)('send-sms'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SmsQueueProcessor.prototype, "handleSendSms", null);
exports.SmsQueueProcessor = SmsQueueProcessor = SmsQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(queue_constants_1.QUEUE_NAMES.SMS),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService,
        twillio_service_1.TwillioService])
], SmsQueueProcessor);
//# sourceMappingURL=sms-queue.processor.js.map