import { OnModuleInit } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { TwillioService } from '../../twillio/twillio.service';
export declare class WhatsAppQueueProcessor implements OnModuleInit {
    private readonly candidateSequenceStatusService;
    private readonly twillioService;
    private readonly whatsappQueue;
    private readonly logger;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService, twillioService: TwillioService, whatsappQueue: Queue);
    onModuleInit(): Promise<void>;
    handleSendWhatsApp(job: Job<QueueJobData>): Promise<void>;
}
