"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WhatsAppQueueProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppQueueProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const queue_constants_1 = require("../queue.constants");
const candidate_sequence_status_service_1 = require("../../candidate-sequence-status/candidate-sequence-status.service");
const candidate_sequence_status_entity_1 = require("../../candidate-sequence-status/candidate-sequence-status.entity");
const twillio_service_1 = require("../../twillio/twillio.service");
let WhatsAppQueueProcessor = WhatsAppQueueProcessor_1 = class WhatsAppQueueProcessor {
    constructor(candidateSequenceStatusService, twillioService, whatsappQueue) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.twillioService = twillioService;
        this.whatsappQueue = whatsappQueue;
        this.logger = new common_1.Logger(WhatsAppQueueProcessor_1.name);
        console.log('🚀 WHATSAPP PROCESSOR: WhatsAppQueueProcessor instantiated');
    }
    async onModuleInit() {
        this.logger.log('WhatsAppQueueProcessor initialized');
        console.log('🚀 WHATSAPP PROCESSOR: WhatsAppQueueProcessor initialized');
        try {
            const waiting = await this.whatsappQueue.getWaiting();
            const active = await this.whatsappQueue.getActive();
            console.log('🚀 WHATSAPP PROCESSOR: Queue stats:', {
                waiting: waiting.length,
                active: active.length
            });
            console.log('🚀 WHATSAPP PROCESSOR: Initialization completed successfully');
        }
        catch (error) {
            console.error('🚀 WHATSAPP PROCESSOR: Error during initialization:', error);
            this.logger.error('Error during initialization:', error);
        }
    }
    async handleSendWhatsApp(job) {
        console.log('📱 WHATSAPP PROCESSOR: ==========================================');
        console.log('📱 WHATSAPP PROCESSOR: handleSendWhatsApp method called!');
        console.log('📱 WHATSAPP PROCESSOR: Job ID:', job.id);
        console.log('📱 WHATSAPP PROCESSOR: Job data:', JSON.stringify(job.data, null, 2));
        console.log('📱 WHATSAPP PROCESSOR: Job opts:', job.opts);
        console.log('📱 WHATSAPP PROCESSOR: ==========================================');
        const { candidateSequenceStatusId, candidateId, stepId, recipientPhone } = job.data;
        console.log('📱 WHATSAPP PROCESSOR: Extracted data:');
        console.log('📱 WHATSAPP PROCESSOR: - candidateSequenceStatusId:', candidateSequenceStatusId);
        console.log('📱 WHATSAPP PROCESSOR: - candidateId:', candidateId);
        console.log('📱 WHATSAPP PROCESSOR: - stepId:', stepId);
        console.log('📱 WHATSAPP PROCESSOR: - recipientPhone:', recipientPhone);
        if (!recipientPhone) {
            console.error('📱 WHATSAPP PROCESSOR: ❌ No recipient phone provided!');
            throw new Error('No recipient phone provided');
        }
        console.log('📱 WHATSAPP PROCESSOR: Processing WhatsApp job for candidate', candidateId, 'step', stepId);
        this.logger.log(`Processing WhatsApp job for candidate ${candidateId}, step ${stepId}`);
        try {
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.QUEUED);
            const dummyMessage = `Test WhatsApp message for candidate ${candidateId}, step ${stepId}`;
            const dummyTemplateData = {
                candidate_id: candidateId.toString(),
                step_id: stepId.toString(),
                sent_at: new Date().toISOString(),
            };
            const result = await this.twillioService.sendWhatsAppMessage(recipientPhone, dummyMessage, undefined, 'UK', true, dummyTemplateData);
            await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.SENT, {
                sentTo: recipientPhone,
                sentAt: new Date().toISOString(),
                messageSid: result.sid,
            });
            this.logger.log(`WhatsApp message sent successfully for candidate ${candidateId}, step ${stepId}`);
            setTimeout(async () => {
                try {
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED);
                    this.logger.log(`WhatsApp delivery confirmed for candidate ${candidateId}, step ${stepId}`);
                }
                catch (error) {
                    this.logger.error(`Failed to update delivery status: ${error.message}`);
                }
            }, 3000);
        }
        catch (error) {
            const errorContext = {
                jobId: job.id,
                candidateId,
                stepId,
                candidateSequenceStatusId,
                recipientPhone,
                errorType: error.constructor.name,
                errorMessage: error.message,
                errorStack: error.stack,
                timestamp: new Date().toISOString(),
            };
            this.logger.error(`❌ Failed to send WhatsApp message for candidate ${candidateId}:`, errorContext);
            console.log(`❌ WHATSAPP PROCESSOR: Failed to send WhatsApp message for candidate ${candidateId}:`, {
                error: error.message,
                type: error.constructor.name,
                jobId: job.id,
            });
            let errorCategory = 'UNKNOWN';
            if (error.message.includes('Candidate sequence status') && error.message.includes('not found')) {
                errorCategory = 'DATA_INTEGRITY';
            }
            else if (error.message.includes('Twilio') || error.message.includes('WhatsApp')) {
                errorCategory = 'WHATSAPP_SERVICE';
            }
            else if (error.message.includes('phone') || error.message.includes('required')) {
                errorCategory = 'VALIDATION';
            }
            if (candidateSequenceStatusId) {
                try {
                    await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
                    await this.candidateSequenceStatusService.updateStatus(candidateSequenceStatusId, candidate_sequence_status_entity_1.SequenceStepStatus.FAILED, {
                        error: error.message,
                        errorCategory,
                        errorType: error.constructor.name,
                        failedAt: new Date().toISOString(),
                        jobId: job.id,
                    });
                    this.logger.log(`Updated status to FAILED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
                }
                catch (statusError) {
                    this.logger.error(`Critical: Failed to update status for candidateSequenceStatusId ${candidateSequenceStatusId}:`, {
                        statusError: statusError.message,
                        originalError: error.message,
                        candidateId,
                        stepId,
                    });
                }
            }
            else {
                this.logger.warn(`Cannot update status - candidateSequenceStatusId is missing for candidate ${candidateId}, step ${stepId}`);
            }
            throw error;
        }
    }
};
exports.WhatsAppQueueProcessor = WhatsAppQueueProcessor;
__decorate([
    (0, bull_1.Process)('send-whatsapp'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WhatsAppQueueProcessor.prototype, "handleSendWhatsApp", null);
exports.WhatsAppQueueProcessor = WhatsAppQueueProcessor = WhatsAppQueueProcessor_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, bull_1.Processor)(queue_constants_1.QUEUE_NAMES.WHATSAPP),
    __param(2, (0, bull_1.InjectQueue)(queue_constants_1.QUEUE_NAMES.WHATSAPP)),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService,
        twillio_service_1.TwillioService, Object])
], WhatsAppQueueProcessor);
//# sourceMappingURL=whatsapp-queue.processor.js.map