import { ModuleRef } from '@nestjs/core';
import { QueueService } from './queue.service';
import { EmailQueueProcessor } from './processors/email-queue.processor';
import { WhatsAppQueueProcessor } from './processors/whatsapp-queue.processor';
import { Response } from 'express';
export declare class QueueController {
    private readonly queueService;
    private readonly moduleRef;
    private readonly emailProcessor;
    private readonly whatsappProcessor;
    constructor(queueService: QueueService, moduleRef: ModuleRef, emailProcessor: EmailQueueProcessor, whatsappProcessor: WhatsAppQueueProcessor);
    getQueueStats(queueName: string): Promise<any>;
    getAllQueueStats(): Promise<any[]>;
    pauseQueue(queueName: string): Promise<{
        success: boolean;
        message: string;
    }>;
    resumeQueue(queueName: string): Promise<{
        success: boolean;
        message: string;
    }>;
    clearQueue(queueName: string): Promise<{
        cleared: number;
        success: boolean;
        message: string;
    }>;
    clearAllQueues(): Promise<{
        success: boolean;
        message: string;
        results: {
            [queueName: string]: {
                cleared: number;
            };
        };
    }>;
    addTestJob(queueName: string, body: {
        candidateId: number;
        stepId: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    getQueueHealth(): Promise<any>;
    getDashboard(res: Response): Promise<void>;
    testProcessor(): Promise<{
        message: string;
        timestamp: string;
        status: string;
        processors: {
            email: boolean;
            whatsapp: boolean;
        };
        error?: undefined;
    } | {
        message: string;
        error: any;
        timestamp: string;
        status?: undefined;
        processors?: undefined;
    }>;
    testDirectQueue(): Promise<{
        success: boolean;
        message: string;
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        message?: undefined;
    }>;
    forceProcessJobs(): Promise<{
        success: boolean;
        message: string;
        jobId: import("bull").JobId;
        result: {
            success: boolean;
            candidateId: number;
            stepId: number;
        };
        timestamp: string;
        error?: undefined;
    } | {
        success: boolean;
        message: string;
        jobId: import("bull").JobId;
        error: any;
        timestamp: string;
        result?: undefined;
    } | {
        success: boolean;
        message: string;
        timestamp: string;
        jobId?: undefined;
        result?: undefined;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        timestamp: string;
        message?: undefined;
        jobId?: undefined;
        result?: undefined;
    }>;
}
