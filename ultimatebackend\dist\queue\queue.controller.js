"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueController = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const queue_service_1 = require("./queue.service");
const email_queue_processor_1 = require("./processors/email-queue.processor");
const whatsapp_queue_processor_1 = require("./processors/whatsapp-queue.processor");
const queue_constants_1 = require("./queue.constants");
const path = require("path");
let QueueController = class QueueController {
    constructor(queueService, moduleRef, emailProcessor, whatsappProcessor) {
        this.queueService = queueService;
        this.moduleRef = moduleRef;
        this.emailProcessor = emailProcessor;
        this.whatsappProcessor = whatsappProcessor;
    }
    async getQueueStats(queueName) {
        return await this.queueService.getQueueStats(queueName);
    }
    async getAllQueueStats() {
        const queueNames = Object.values(queue_constants_1.QUEUE_NAMES);
        const stats = [];
        for (const queueName of queueNames) {
            try {
                const queueStats = await this.queueService.getQueueStats(queueName);
                stats.push(queueStats);
            }
            catch (error) {
                stats.push({
                    error: error.message,
                    name: queueName,
                    status: 'unavailable',
                    waiting: 0,
                    active: 0,
                    completed: 0,
                    failed: 0,
                    delayed: 0,
                    total: 0
                });
            }
        }
        return stats;
    }
    async pauseQueue(queueName) {
        await this.queueService.pauseQueue(queueName);
        return { success: true, message: `Queue ${queueName} paused` };
    }
    async resumeQueue(queueName) {
        await this.queueService.resumeQueue(queueName);
        return { success: true, message: `Queue ${queueName} resumed` };
    }
    async clearQueue(queueName) {
        const result = await this.queueService.clearQueue(queueName);
        return {
            success: true,
            message: `Cleared ${result.cleared} jobs from queue ${queueName}`,
            ...result
        };
    }
    async clearAllQueues() {
        const results = await this.queueService.clearAllQueues();
        const totalCleared = Object.values(results).reduce((sum, result) => sum + (result.cleared || 0), 0);
        return {
            success: true,
            message: `Cleared ${totalCleared} jobs from all queues`,
            results
        };
    }
    async addTestJob(queueName, body) {
        const testJobData = {
            candidateSequenceStatusId: 1,
            candidateId: body.candidateId,
            stepId: body.stepId,
            sequenceId: 1,
            medium: queueName.replace('-queue', '').toUpperCase(),
            recipientEmail: '<EMAIL>',
            recipientPhone: '+923097442494',
            recipientLinkedIn: 'https://linkedin.com/in/test',
            subject: 'Test Subject',
            body: 'Test message body',
            metadata: { test: true },
        };
        await this.queueService.addJobToQueue(queueName.replace('-queue', ''), testJobData);
        return { success: true, message: `Test job added to ${queueName}` };
    }
    async getQueueHealth() {
        return await this.queueService.getSystemHealth();
    }
    async getDashboard(res) {
        const dashboardPath = path.join(process.cwd(), 'public', 'queue-dashboard.html');
        return res.sendFile(dashboardPath);
    }
    async testProcessor() {
        try {
            console.log('🧪 QUEUE CONTROLLER: Testing processor instantiation...');
            console.log('🧪 QUEUE CONTROLLER: EmailQueueProcessor injected:', !!this.emailProcessor);
            console.log('🧪 QUEUE CONTROLLER: WhatsAppQueueProcessor injected:', !!this.whatsappProcessor);
            if (this.emailProcessor) {
                console.log('🧪 QUEUE CONTROLLER: Manually calling email processor...');
                const testJob = {
                    id: 'test-manual-call',
                    data: {
                        candidateSequenceStatusId: 1,
                        candidateId: 1,
                        stepId: 1,
                        recipientEmail: '<EMAIL>',
                        subject: 'Test Email',
                        body: 'This is a test email from manual processor call',
                    },
                    opts: {},
                    attemptsMade: 0,
                    queue: null,
                    timestamp: Date.now(),
                    delay: 0,
                    progress: () => { },
                    log: () => { },
                    moveToCompleted: () => { },
                    moveToFailed: () => { },
                    remove: () => { },
                    retry: () => { },
                    discard: () => { },
                    promote: () => { },
                    finished: () => Promise.resolve(),
                    toJSON: () => ({}),
                };
                try {
                    const result = await this.emailProcessor.handleSendEmail(testJob);
                    console.log('🧪 QUEUE CONTROLLER: Manual processor call result:', result);
                }
                catch (error) {
                    console.error('🧪 QUEUE CONTROLLER: Manual processor call failed:', error);
                }
            }
            return {
                message: 'Queue processors are running via dependency injection',
                timestamp: new Date().toISOString(),
                status: 'active',
                processors: {
                    email: !!this.emailProcessor,
                    whatsapp: !!this.whatsappProcessor,
                },
            };
        }
        catch (error) {
            console.error('🧪 QUEUE CONTROLLER: Error testing processors:', error);
            return {
                message: 'Failed to check processor status',
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async testDirectQueue() {
        try {
            console.log('🧪 QUEUE CONTROLLER: Testing direct queue access...');
            const emailQueue = this.queueService['getQueueByName']('email-queue');
            console.log('🧪 QUEUE CONTROLLER: Email queue found:', !!emailQueue);
            if (emailQueue) {
                console.log('🧪 QUEUE CONTROLLER: Queue name:', emailQueue.name);
                console.log('🧪 QUEUE CONTROLLER: Queue waiting count:', await emailQueue.getWaiting().then(jobs => jobs.length));
                console.log('🧪 QUEUE CONTROLLER: Queue active count:', await emailQueue.getActive().then(jobs => jobs.length));
                console.log('🧪 QUEUE CONTROLLER: Queue completed count:', await emailQueue.getCompleted().then(jobs => jobs.length));
                console.log('🧪 QUEUE CONTROLLER: Adding job directly to queue...');
                const job = await emailQueue.add('send-email', {
                    candidateSequenceStatusId: 999,
                    candidateId: 999,
                    stepId: 999,
                    recipientEmail: '<EMAIL>',
                    subject: 'Direct Queue Test',
                    body: 'This is a direct queue test',
                });
                console.log('🧪 QUEUE CONTROLLER: Job added with ID:', job.id);
                setTimeout(async () => {
                    try {
                        const jobStatus = await job.getState();
                        console.log('🧪 QUEUE CONTROLLER: Job status after 2 seconds:', jobStatus);
                    }
                    catch (error) {
                        console.error('🧪 QUEUE CONTROLLER: Error checking job status:', error);
                    }
                }, 2000);
            }
            return {
                success: true,
                message: 'Direct queue test initiated',
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            console.error('🧪 QUEUE CONTROLLER: Error testing direct queue:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async forceProcessJobs() {
        try {
            console.log('🔧 QUEUE CONTROLLER: Attempting to force process jobs...');
            const emailQueue = this.queueService['getQueueByName']('email-queue');
            if (emailQueue) {
                const waitingJobs = await emailQueue.getWaiting();
                console.log('🔧 QUEUE CONTROLLER: Found', waitingJobs.length, 'waiting jobs');
                if (waitingJobs.length > 0) {
                    const job = waitingJobs[0];
                    console.log('🔧 QUEUE CONTROLLER: Attempting to manually process job:', job.id);
                    console.log('🔧 QUEUE CONTROLLER: Job data:', job.data);
                    try {
                        const result = await this.emailProcessor.handleSendEmail(job);
                        console.log('🔧 QUEUE CONTROLLER: Manual processing result:', result);
                        await job.moveToCompleted('Manual processing completed', true);
                        console.log('🔧 QUEUE CONTROLLER: Job marked as completed');
                        return {
                            success: true,
                            message: 'Job processed manually',
                            jobId: job.id,
                            result: result,
                            timestamp: new Date().toISOString(),
                        };
                    }
                    catch (processingError) {
                        console.error('🔧 QUEUE CONTROLLER: Error processing job:', processingError);
                        await job.moveToFailed(processingError, true);
                        return {
                            success: false,
                            message: 'Job processing failed',
                            jobId: job.id,
                            error: processingError.message,
                            timestamp: new Date().toISOString(),
                        };
                    }
                }
                else {
                    return {
                        success: false,
                        message: 'No waiting jobs found',
                        timestamp: new Date().toISOString(),
                    };
                }
            }
            else {
                return {
                    success: false,
                    message: 'Email queue not found',
                    timestamp: new Date().toISOString(),
                };
            }
        }
        catch (error) {
            console.error('🔧 QUEUE CONTROLLER: Error forcing job processing:', error);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
};
exports.QueueController = QueueController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get queue statistics' }),
    (0, common_1.Get)('stats/:queueName'),
    __param(0, (0, common_1.Param)('queueName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "getQueueStats", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get all queue statistics' }),
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "getAllQueueStats", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Pause a queue' }),
    (0, common_1.Post)(':queueName/pause'),
    __param(0, (0, common_1.Param)('queueName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "pauseQueue", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Resume a queue' }),
    (0, common_1.Post)(':queueName/resume'),
    __param(0, (0, common_1.Param)('queueName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "resumeQueue", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Clear all jobs from a specific queue' }),
    (0, common_1.Post)(':queueName/clear'),
    __param(0, (0, common_1.Param)('queueName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "clearQueue", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Clear all jobs from all queues' }),
    (0, common_1.Post)('clear-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "clearAllQueues", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Add a test job to queue' }),
    (0, common_1.Post)(':queueName/test'),
    __param(0, (0, common_1.Param)('queueName')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "addTestJob", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get queue health and available queues' }),
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "getQueueHealth", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Queue Dashboard' }),
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "getDashboard", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Test Processor Status' }),
    (0, common_1.Get)('test-processor'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "testProcessor", null);
__decorate([
    (0, common_1.Get)('test-direct-queue'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "testDirectQueue", null);
__decorate([
    (0, common_1.Get)('force-process-jobs'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QueueController.prototype, "forceProcessJobs", null);
exports.QueueController = QueueController = __decorate([
    (0, common_1.Controller)('queue'),
    (0, swagger_1.ApiTags)('Queue Management'),
    __metadata("design:paramtypes", [queue_service_1.QueueService,
        core_1.ModuleRef,
        email_queue_processor_1.EmailQueueProcessor,
        whatsapp_queue_processor_1.WhatsAppQueueProcessor])
], QueueController);
//# sourceMappingURL=queue.controller.js.map