"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueModule = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const config_1 = require("@nestjs/config");
const email_queue_processor_1 = require("./processors/email-queue.processor");
const whatsapp_queue_processor_1 = require("./processors/whatsapp-queue.processor");
const sms_queue_processor_1 = require("./processors/sms-queue.processor");
const call_queue_processor_1 = require("./processors/call-queue.processor");
const linkedin_queue_processor_1 = require("./processors/linkedin-queue.processor");
const queue_service_1 = require("./queue.service");
const queue_controller_1 = require("./queue.controller");
const typeorm_1 = require("@nestjs/typeorm");
const candidate_sequence_status_entity_1 = require("../candidate-sequence-status/candidate-sequence-status.entity");
const candidate_sequence_status_module_1 = require("../candidate-sequence-status/candidate-sequence-status.module");
const queue_constants_1 = require("./queue.constants");
const email_module_1 = require("../email/email.module");
const twillio_module_1 = require("../twillio/twillio.module");
let QueueModule = class QueueModule {
    constructor() {
        console.log('🔧 QUEUE MODULE: QueueModule instantiated');
        console.log('🔧 QUEUE MODULE: Processors should be registered:', {
            EmailQueueProcessor: email_queue_processor_1.EmailQueueProcessor.name,
            WhatsAppQueueProcessor: whatsapp_queue_processor_1.WhatsAppQueueProcessor.name,
        });
    }
};
exports.QueueModule = QueueModule;
exports.QueueModule = QueueModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            email_module_1.EmailModule,
            twillio_module_1.TwillioModule,
            bull_1.BullModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const redisConfig = {
                        host: configService.get('REDIS_HOST', 'localhost'),
                        port: parseInt(configService.get('REDIS_PORT', '6379')),
                        password: configService.get('REDIS_PASSWORD') || undefined,
                        db: parseInt(configService.get('REDIS_DB', '0')),
                        retryDelayOnFailover: 100,
                        enableReadyCheck: false,
                        maxRetriesPerRequest: null,
                        lazyConnect: true,
                        connectTimeout: 10000,
                        commandTimeout: 5000,
                    };
                    console.log('Redis configuration:', {
                        host: redisConfig.host,
                        port: redisConfig.port,
                        db: redisConfig.db,
                        hasPassword: !!redisConfig.password,
                    });
                    return {
                        redis: redisConfig,
                        defaultJobOptions: {
                            removeOnComplete: 10,
                            removeOnFail: 50,
                            attempts: 3,
                            backoff: {
                                type: 'exponential',
                                delay: 2000,
                            },
                        },
                    };
                },
                inject: [config_1.ConfigService],
            }),
            bull_1.BullModule.registerQueue({
                name: queue_constants_1.QUEUE_NAMES.EMAIL,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 10,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000,
                    maxStalledCount: 1,
                },
            }, {
                name: queue_constants_1.QUEUE_NAMES.WHATSAPP,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 10,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000,
                    maxStalledCount: 1,
                },
            }, {
                name: queue_constants_1.QUEUE_NAMES.SMS,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 10,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000,
                    maxStalledCount: 1,
                },
            }, {
                name: queue_constants_1.QUEUE_NAMES.CALL,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 10,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000,
                    maxStalledCount: 1,
                },
            }, {
                name: queue_constants_1.QUEUE_NAMES.LINKEDIN,
                defaultJobOptions: {
                    removeOnComplete: 10,
                    removeOnFail: 10,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
                settings: {
                    stalledInterval: 30 * 1000,
                    maxStalledCount: 1,
                },
            }),
            typeorm_1.TypeOrmModule.forFeature([candidate_sequence_status_entity_1.CandidateSequenceStatus]),
            candidate_sequence_status_module_1.CandidateSequenceStatusModule,
        ],
        controllers: [queue_controller_1.QueueController],
        providers: [
            queue_service_1.QueueService,
            email_queue_processor_1.EmailQueueProcessor,
            whatsapp_queue_processor_1.WhatsAppQueueProcessor,
            sms_queue_processor_1.SmsQueueProcessor,
            call_queue_processor_1.CallQueueProcessor,
            linkedin_queue_processor_1.LinkedInQueueProcessor,
        ],
        exports: [queue_service_1.QueueService, bull_1.BullModule],
    }),
    __metadata("design:paramtypes", [])
], QueueModule);
//# sourceMappingURL=queue.module.js.map