import { OnModuleInit } from '@nestjs/common';
import { Queue } from 'bull';
export interface QueueJobData {
    candidateSequenceStatusId: number;
    candidateId: number;
    stepId: number;
    sequenceId: number;
    medium: string;
    templateId?: number;
    recipientEmail?: string;
    recipientPhone?: string;
    recipientLinkedIn?: string;
    subject?: string;
    body?: string;
    metadata?: Record<string, any>;
}
export declare class QueueService implements OnModuleInit {
    private emailQueue;
    private whatsappQueue;
    private smsQueue;
    private callQueue;
    private linkedinQueue;
    private readonly logger;
    private redisConnectionStatus;
    constructor(emailQueue: Queue, whatsappQueue: Queue, smsQueue: Queue, callQueue: Queue, linkedinQueue: Queue);
    onModuleInit(): Promise<void>;
    private checkRedisConnection;
    addJobToQueue(medium: string, jobData: QueueJobData, delay?: number): Promise<void>;
    addBulkJobs(medium: string, jobs: QueueJobData[]): Promise<void>;
    getQueueStats(queueName: string): Promise<any>;
    pauseQueue(queueName: string): Promise<void>;
    resumeQueue(queueName: string): Promise<void>;
    clearQueue(queueName: string): Promise<{
        cleared: number;
    }>;
    clearAllQueues(): Promise<{
        [queueName: string]: {
            cleared: number;
        };
    }>;
    private getQueueByName;
    getAvailableQueues(): string[];
    isValidQueue(queueName: string): boolean;
    getRedisConnectionStatus(): string;
    getSystemHealth(): Promise<any>;
    getErrorStatistics(): Promise<any>;
    retryFailedJobs(queueName: string): Promise<{
        retried: number;
    }>;
}
