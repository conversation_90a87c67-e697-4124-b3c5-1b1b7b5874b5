"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResumeTemplateDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class ResumeTemplateDto {
}
exports.ResumeTemplateDto = ResumeTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the resume template',
        examples: ['Modern Resume', 'Creative Resume', 'Simple Resume']
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResumeTemplateDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'HTML structure of the template with placeholders',
        examples: [
            "<h1>{{name}}</h1><p>Email: {{email}}</p><p>Phone: {{phone}}</p>",
            "<div style='background-color: #f8f9fa;'><h1>{{name}}</h1><p>{{email}}</p></div>",
            "<header style='background: #ff6f61;'><h1>{{name}}</h1></header><section><p>{{summary}}</p></section>"
        ]
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ResumeTemplateDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional style information as a JSON object',
        examples: [
            { "fontFamily": "Arial, sans-serif", "color": "#333" },
            { "backgroundColor": "#f8f9fa", "fontSize": "16px" },
            { "headerBackground": "#ff6f61", "textColor": "#000" }
        ]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ResumeTemplateDto.prototype, "styles", void 0);
//# sourceMappingURL=resumeTemplates.dto.js.map