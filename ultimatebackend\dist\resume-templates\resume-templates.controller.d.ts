import { ResumeTemplate } from './resume-template.entity';
import { ResumeTemplateService } from './resume-templates.service';
import { ResumeTemplateDto } from './dto/resumeTemplates.dto';
export declare class ResumeTemplateController {
    private readonly resumeTemplateService;
    constructor(resumeTemplateService: ResumeTemplateService);
    create(dto: ResumeTemplateDto): Promise<ResumeTemplate>;
    findAll(): Promise<ResumeTemplate[]>;
    findOne(id: number): Promise<ResumeTemplate>;
    update(id: number, dto: ResumeTemplateDto): Promise<ResumeTemplate>;
    delete(id: number): Promise<void>;
}
