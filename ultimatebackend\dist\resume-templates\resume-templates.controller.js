"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResumeTemplateController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const resume_template_entity_1 = require("./resume-template.entity");
const resume_templates_service_1 = require("./resume-templates.service");
const resumeTemplates_dto_1 = require("./dto/resumeTemplates.dto");
let ResumeTemplateController = class ResumeTemplateController {
    constructor(resumeTemplateService) {
        this.resumeTemplateService = resumeTemplateService;
    }
    async create(dto) {
        return await this.resumeTemplateService.create(dto);
    }
    async findAll() {
        return await this.resumeTemplateService.findAll();
    }
    async findOne(id) {
        return await this.resumeTemplateService.findOne(id);
    }
    async update(id, dto) {
        return await this.resumeTemplateService.update(id, dto);
    }
    async delete(id) {
        return await this.resumeTemplateService.delete(id);
    }
};
exports.ResumeTemplateController = ResumeTemplateController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new resume template' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Resume template created successfully',
        type: resume_template_entity_1.ResumeTemplate,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [resumeTemplates_dto_1.ResumeTemplateDto]),
    __metadata("design:returntype", Promise)
], ResumeTemplateController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all resume templates' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'List of all resume templates',
        type: [resume_template_entity_1.ResumeTemplate],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResumeTemplateController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific resume template by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number, description: 'Resume template ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Resume template found',
        type: resume_template_entity_1.ResumeTemplate,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Resume template not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ResumeTemplateController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a resume template' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number, description: 'Resume template ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Resume template updated successfully',
        type: resume_template_entity_1.ResumeTemplate,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Resume template not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, resumeTemplates_dto_1.ResumeTemplateDto]),
    __metadata("design:returntype", Promise)
], ResumeTemplateController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a resume template' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: Number, description: 'Resume template ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Resume template deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Resume template not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ResumeTemplateController.prototype, "delete", null);
exports.ResumeTemplateController = ResumeTemplateController = __decorate([
    (0, swagger_1.ApiTags)('Resume Templates'),
    (0, common_1.Controller)('resume-templates'),
    __metadata("design:paramtypes", [resume_templates_service_1.ResumeTemplateService])
], ResumeTemplateController);
//# sourceMappingURL=resume-templates.controller.js.map