"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResumeTemplatesModule = void 0;
const common_1 = require("@nestjs/common");
const resume_templates_controller_1 = require("./resume-templates.controller");
const resume_templates_service_1 = require("./resume-templates.service");
const typeorm_1 = require("@nestjs/typeorm");
const resume_template_entity_1 = require("./resume-template.entity");
let ResumeTemplatesModule = class ResumeTemplatesModule {
};
exports.ResumeTemplatesModule = ResumeTemplatesModule;
exports.ResumeTemplatesModule = ResumeTemplatesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([resume_template_entity_1.ResumeTemplate])],
        providers: [resume_templates_service_1.ResumeTemplateService],
        controllers: [resume_templates_controller_1.ResumeTemplateController],
    })
], ResumeTemplatesModule);
//# sourceMappingURL=resume-templates.module.js.map