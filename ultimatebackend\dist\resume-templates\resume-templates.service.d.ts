import { Repository } from 'typeorm';
import { ResumeTemplate } from './resume-template.entity';
import { ResumeTemplateDto } from './dto/resumeTemplates.dto';
export declare class ResumeTemplateService {
    private readonly resumeTemplateRepository;
    constructor(resumeTemplateRepository: Repository<ResumeTemplate>);
    create(dto: ResumeTemplateDto): Promise<ResumeTemplate>;
    findAll(): Promise<ResumeTemplate[]>;
    findOne(id: number): Promise<ResumeTemplate>;
    update(id: number, dto: ResumeTemplateDto): Promise<ResumeTemplate>;
    delete(id: number): Promise<void>;
}
