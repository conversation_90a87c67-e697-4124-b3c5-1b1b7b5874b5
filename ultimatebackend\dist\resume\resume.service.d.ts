import { Repository } from 'typeorm';
import { Resume } from './resume.entity';
import { ResumeTemplate } from 'src/resume-templates/resume-template.entity';
export declare class ResumeService {
    private readonly resumeRepo;
    private readonly templateRepo;
    constructor(resumeRepo: Repository<Resume>, templateRepo: Repository<ResumeTemplate>);
    createResume(userId: string, templateId: number, data: Record<string, any>): Promise<Resume>;
    getResumesByUser(userId: string): Promise<Resume[]>;
    getResumeById(id: number): Promise<Resume>;
}
