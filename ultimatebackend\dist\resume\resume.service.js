"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResumeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const resume_entity_1 = require("./resume.entity");
const resume_template_entity_1 = require("../resume-templates/resume-template.entity");
let ResumeService = class ResumeService {
    constructor(resumeRepo, templateRepo) {
        this.resumeRepo = resumeRepo;
        this.templateRepo = templateRepo;
    }
    async createResume(userId, templateId, data) {
        const template = await this.templateRepo.findOne({ where: { id: templateId } });
        if (!template)
            throw new common_1.NotFoundException('Template not found');
        const resume = this.resumeRepo.create({ userId, template, data });
        return this.resumeRepo.save(resume);
    }
    async getResumesByUser(userId) {
        return this.resumeRepo.find({ where: { userId } });
    }
    async getResumeById(id) {
        return this.resumeRepo.findOne({ where: { id } });
    }
};
exports.ResumeService = ResumeService;
exports.ResumeService = ResumeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(resume_entity_1.Resume)),
    __param(1, (0, typeorm_1.InjectRepository)(resume_template_entity_1.ResumeTemplate)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ResumeService);
//# sourceMappingURL=resume.service.js.map