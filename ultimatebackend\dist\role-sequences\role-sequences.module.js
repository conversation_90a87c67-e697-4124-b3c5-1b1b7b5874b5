"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleSequencesModule = void 0;
const common_1 = require("@nestjs/common");
const role_sequences_service_1 = require("./role-sequences.service");
const role_sequences_controller_1 = require("./role-sequences.controller");
let RoleSequencesModule = class RoleSequencesModule {
};
exports.RoleSequencesModule = RoleSequencesModule;
exports.RoleSequencesModule = RoleSequencesModule = __decorate([
    (0, common_1.Module)({
        providers: [role_sequences_service_1.RoleSequencesService],
        controllers: [role_sequences_controller_1.RoleSequencesController]
    })
], RoleSequencesModule);
//# sourceMappingURL=role-sequences.module.js.map