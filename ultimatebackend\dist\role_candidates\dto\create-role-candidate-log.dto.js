"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRoleCandidateLogDto = void 0;
const role_candidate_log_entity_1 = require("../role_candidate_log.entity");
const class_validator_1 = require("class-validator");
class CreateRoleCandidateLogDto {
}
exports.CreateRoleCandidateLogDto = CreateRoleCandidateLogDto;
__decorate([
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateRoleCandidateLogDto.prototype, "roleCandidateId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(role_candidate_log_entity_1.CommunicationType),
    __metadata("design:type", String)
], CreateRoleCandidateLogDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRoleCandidateLogDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRoleCandidateLogDto.prototype, "messageId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateRoleCandidateLogDto.prototype, "details", void 0);
//# sourceMappingURL=create-role-candidate-log.dto.js.map