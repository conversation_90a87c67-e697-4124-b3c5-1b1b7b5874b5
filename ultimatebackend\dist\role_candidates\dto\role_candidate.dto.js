"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RoleCandidateDto {
}
exports.RoleCandidateDto = RoleCandidateDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The source type of the role candidate',
        enum: ['LINKEDIN', 'CV'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['LINKEDIN', 'CV']),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "source_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The profile link of the candidate',
        example: 'https://linkedin.com/in/example',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "profile_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The source of the candidate profile (e.g., LinkedIn)',
        example: 'LinkedIn',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "profile_source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The LinkedIn type of the candidate',
        example: ['OTW', 'HEAD HUNTING'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "li_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the candidate is willing to relocate',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RoleCandidateDto.prototype, "is_willing_to_relocate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if the candidate is accepted',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RoleCandidateDto.prototype, "is_accepted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The current status of the candidate',
        enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ISSUE'],
        default: 'PENDING',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ISSUE']),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "candidate_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The ID of the associated role',
        example: 101,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleCandidateDto.prototype, "roleId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The ID of the associated client (People entity)',
        example: 'client123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RoleCandidateDto.prototype, "clientId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The ID of the associated candidate (People entity)',
        example: 'candidate123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RoleCandidateDto.prototype, "candidateId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The ID of the associated prospect (People entity)',
        example: 'prospect123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], RoleCandidateDto.prototype, "prospectId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The ID of the user who added the role candidate',
        example: 'user123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleCandidateDto.prototype, "userId", void 0);
//# sourceMappingURL=role_candidate.dto.js.map