"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecruitmentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const recruitment_service_1 = require("./recruitment.service");
let RecruitmentController = class RecruitmentController {
    constructor(recruitmentService) {
        this.recruitmentService = recruitmentService;
    }
    async updateRoleCandidateStage({ chennel, screening_stage, partially_interested_stage, submission_stage, role_candidate_id, }) {
        return await this.recruitmentService.updateRoleCandidateStage(chennel, screening_stage, partially_interested_stage, submission_stage, role_candidate_id);
    }
};
exports.RecruitmentController = RecruitmentController;
__decorate([
    (0, common_1.Put)('update-role-candidate-stage'),
    (0, swagger_1.ApiOperation)({ summary: 'Update role candidate stage' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                chennel: { type: 'string' },
                screening_stage: { type: 'string' },
                partially_interested_stage: { type: 'string' },
                submission_stage: { type: 'string' },
                role_candidate_id: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RecruitmentController.prototype, "updateRoleCandidateStage", null);
exports.RecruitmentController = RecruitmentController = __decorate([
    (0, common_1.Controller)('recruitment'),
    (0, swagger_1.ApiTags)('Recruitment'),
    __metadata("design:paramtypes", [recruitment_service_1.RecruitmentService])
], RecruitmentController);
//# sourceMappingURL=recruitment.controller.js.map