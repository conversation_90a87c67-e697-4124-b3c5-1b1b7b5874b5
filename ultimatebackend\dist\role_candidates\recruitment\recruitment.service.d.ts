import { RoleCandidate } from '../role_candidates.entity';
import { Repository } from 'typeorm';
import { Roles } from 'src/roles/roles.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Languages } from 'src/languages/langauges.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';
export declare class RecruitmentService {
    private readonly roleCandidateRepository;
    private readonly rolesRepository;
    private readonly peopleRepository;
    private readonly personEmailRepository;
    private readonly personPhoneRepository;
    private readonly qualificationsRepository;
    private readonly languagesRepository;
    private readonly personSkillRepository;
    private readonly s3BucketService;
    constructor(roleCandidateRepository: Repository<RoleCandidate>, rolesRepository: Repository<Roles>, peopleRepository: Repository<People>, personEmailRepository: Repository<PersonEmail>, personPhoneRepository: Repository<PersonPhone>, qualificationsRepository: Repository<Qualifications>, languagesRepository: Repository<Languages>, personSkillRepository: Repository<PersonSkill>, s3BucketService: S3bucketService);
    updateRoleCandidateStage(chennel?: string, screening_stage?: string, partially_interested_stage?: string, submission_stage?: string, role_candidate_id?: number): Promise<RoleCandidate[]>;
}
