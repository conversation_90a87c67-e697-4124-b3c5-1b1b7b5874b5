"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecruitmentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_candidates_entity_1 = require("../role_candidates.entity");
const typeorm_2 = require("typeorm");
const roles_entity_1 = require("../../roles/roles.entity");
const people_entity_1 = require("../../people/people.entity");
const emails_entity_1 = require("../../emails/emails.entity");
const phone_entity_1 = require("../../phone/phone.entity");
const qualifications_entity_1 = require("../../qualifications/qualifications.entity");
const langauges_entity_1 = require("../../languages/langauges.entity");
const skills_entity_1 = require("../../skills/skills.entity");
const s3bucket_service_1 = require("../../s3bucket/s3bucket.service");
let RecruitmentService = class RecruitmentService {
    constructor(roleCandidateRepository, rolesRepository, peopleRepository, personEmailRepository, personPhoneRepository, qualificationsRepository, languagesRepository, personSkillRepository, s3BucketService) {
        this.roleCandidateRepository = roleCandidateRepository;
        this.rolesRepository = rolesRepository;
        this.peopleRepository = peopleRepository;
        this.personEmailRepository = personEmailRepository;
        this.personPhoneRepository = personPhoneRepository;
        this.qualificationsRepository = qualificationsRepository;
        this.languagesRepository = languagesRepository;
        this.personSkillRepository = personSkillRepository;
        this.s3BucketService = s3BucketService;
    }
    async updateRoleCandidateStage(chennel, screening_stage, partially_interested_stage, submission_stage, role_candidate_id) {
        try {
            const roleCandidate = await this.roleCandidateRepository.findOneBy({
                id: role_candidate_id,
            });
            if (!roleCandidate) {
                throw new common_1.NotFoundException('Role candidate not found');
            }
            if (screening_stage)
                roleCandidate.screening_stage = screening_stage;
            if (partially_interested_stage)
                roleCandidate.partially_interested_stage = partially_interested_stage;
            if (submission_stage)
                roleCandidate.submission_stage = submission_stage;
            roleCandidate.current_channel = chennel;
            roleCandidate.stage = 'ENGAGED';
            await this.roleCandidateRepository.save(roleCandidate);
            return [roleCandidate];
        }
        catch (error) {
            console.error('Error updating role candidate stage:', error);
            throw new common_1.InternalServerErrorException('Failed to update role candidate stage');
        }
    }
};
exports.RecruitmentService = RecruitmentService;
exports.RecruitmentService = RecruitmentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_candidates_entity_1.RoleCandidate)),
    __param(1, (0, typeorm_1.InjectRepository)(roles_entity_1.Roles)),
    __param(2, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(3, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(4, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __param(5, (0, typeorm_1.InjectRepository)(qualifications_entity_1.Qualifications)),
    __param(6, (0, typeorm_1.InjectRepository)(langauges_entity_1.Languages)),
    __param(7, (0, typeorm_1.InjectRepository)(skills_entity_1.PersonSkill)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        s3bucket_service_1.S3bucketService])
], RecruitmentService);
//# sourceMappingURL=recruitment.service.js.map