import { RoleCandidateLogService } from './role_candidate_log.service';
import { RoleCandidateLog } from './role_candidate_log.entity';
import { CreateRoleCandidateLogDto } from './dto/create-role-candidate-log.dto';
export declare class RoleCandidateLogController {
    private readonly logService;
    constructor(logService: RoleCandidateLogService);
    createLog(log: CreateRoleCandidateLogDto): Promise<CreateRoleCandidateLogDto & RoleCandidateLog>;
    getLogsByRoleCandidate(roleCandidateId: number): Promise<RoleCandidateLog[]>;
    getAllLogs(): Promise<RoleCandidateLog[]>;
    deleteLog(id: number): Promise<import("typeorm").DeleteResult>;
}
