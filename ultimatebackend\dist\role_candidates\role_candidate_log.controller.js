"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidateLogController = void 0;
const common_1 = require("@nestjs/common");
const role_candidate_log_service_1 = require("./role_candidate_log.service");
const create_role_candidate_log_dto_1 = require("./dto/create-role-candidate-log.dto");
let RoleCandidateLogController = class RoleCandidateLogController {
    constructor(logService) {
        this.logService = logService;
    }
    async createLog(log) {
        return this.logService.createLog(log);
    }
    async getLogsByRoleCandidate(roleCandidateId) {
        return this.logService.getLogsByRoleCandidate(roleCandidateId);
    }
    async getAllLogs() {
        return this.logService.getAllLogs();
    }
    async deleteLog(id) {
        return this.logService.deleteLog(id);
    }
};
exports.RoleCandidateLogController = RoleCandidateLogController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_role_candidate_log_dto_1.CreateRoleCandidateLogDto]),
    __metadata("design:returntype", Promise)
], RoleCandidateLogController.prototype, "createLog", null);
__decorate([
    (0, common_1.Get)('role-candidate/:roleCandidateId'),
    __param(0, (0, common_1.Param)('roleCandidateId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidateLogController.prototype, "getLogsByRoleCandidate", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RoleCandidateLogController.prototype, "getAllLogs", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidateLogController.prototype, "deleteLog", null);
exports.RoleCandidateLogController = RoleCandidateLogController = __decorate([
    (0, common_1.Controller)('role-candidate-logs'),
    __metadata("design:paramtypes", [role_candidate_log_service_1.RoleCandidateLogService])
], RoleCandidateLogController);
//# sourceMappingURL=role_candidate_log.controller.js.map