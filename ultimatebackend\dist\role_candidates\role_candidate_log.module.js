"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidateLogModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_candidate_log_entity_1 = require("./role_candidate_log.entity");
const role_candidate_log_service_1 = require("./role_candidate_log.service");
const role_candidate_log_controller_1 = require("./role_candidate_log.controller");
let RoleCandidateLogModule = class RoleCandidateLogModule {
};
exports.RoleCandidateLogModule = RoleCandidateLogModule;
exports.RoleCandidateLogModule = RoleCandidateLogModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([role_candidate_log_entity_1.RoleCandidateLog])],
        providers: [role_candidate_log_service_1.RoleCandidateLogService],
        controllers: [role_candidate_log_controller_1.RoleCandidateLogController],
        exports: [role_candidate_log_service_1.RoleCandidateLogService],
    })
], RoleCandidateLogModule);
//# sourceMappingURL=role_candidate_log.module.js.map