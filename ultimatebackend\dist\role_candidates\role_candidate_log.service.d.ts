import { Repository } from 'typeorm';
import { RoleCandidateLog } from './role_candidate_log.entity';
import { CreateRoleCandidateLogDto } from './dto/create-role-candidate-log.dto';
export declare class RoleCandidateLogService {
    private readonly logRepo;
    constructor(logRepo: Repository<RoleCandidateLog>);
    createLog(log: CreateRoleCandidateLogDto): Promise<CreateRoleCandidateLogDto & RoleCandidateLog>;
    getLogsByRoleCandidate(roleCandidateId: number): Promise<RoleCandidateLog[]>;
    getAllLogs(): Promise<RoleCandidateLog[]>;
    deleteLog(id: number): Promise<import("typeorm").DeleteResult>;
}
