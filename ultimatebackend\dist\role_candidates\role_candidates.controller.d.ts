import { RoleCandidatesService } from './role_candidates.service';
import { RoleCandidateDto } from './dto/role_candidate.dto';
import { UpdateConnectionStatusDto } from './dto/update-connection-status.dto';
import { RoleCandidate } from './role_candidates.entity';
export declare class RoleCandidatesController {
    private readonly roleCandidatesService;
    constructor(roleCandidatesService: RoleCandidatesService);
    private handleError;
    createRoleCandidate(roleCandidateDto: RoleCandidateDto): Promise<RoleCandidate>;
    addLinkedinToRole(businessEmail: string[], businessNumber: string[], businessName: string[], personalNumber: string[], profile_url: string, profileType: string, roleId: number, userId: string, clientId?: number, prospectId?: number): Promise<RoleCandidate>;
    getRoleCandidatesV1(roleId: number, userId: string, clientId?: number, type?: string, source?: string): Promise<RoleCandidate[]>;
    getRoleCandidates(roleId: number, userId: string, clientId?: number, type?: string, page?: number, limit?: number): Promise<{
        data: RoleCandidate[];
        total: number;
    }>;
    changeRoleCandidateStatus(roleId: number, userId: string, profile_url?: string): Promise<RoleCandidate[]>;
    deleteRoleCandidate(id: number): Promise<{
        success: boolean;
        message: string;
    }>;
    addCvToRole(file: Express.Multer.File, data: string): Promise<RoleCandidate>;
    getPendingLinkedinProfile(): Promise<RoleCandidate>;
    getRoleCandidateCount(roleId: number): Promise<{
        linkedin_count: number;
        cv_count: number;
        total_count: number;
    }>;
    get360AndPreQualificationCandidates(roleId: number): Promise<RoleCandidate[]>;
    markProfileReadyForConnection(id: number): Promise<RoleCandidate>;
    getOneReadyToSendConnectionProfile(): Promise<RoleCandidate>;
    updateConnectionRequestStatus(id: number, updateConnectionStatusDto: UpdateConnectionStatusDto): Promise<RoleCandidate>;
}
