"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidatesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const role_candidates_service_1 = require("./role_candidates.service");
const role_candidate_dto_1 = require("./dto/role_candidate.dto");
const update_connection_status_dto_1 = require("./dto/update-connection-status.dto");
const role_candidates_entity_1 = require("./role_candidates.entity");
const platform_express_1 = require("@nestjs/platform-express");
let RoleCandidatesController = class RoleCandidatesController {
    constructor(roleCandidatesService) {
        this.roleCandidatesService = roleCandidatesService;
    }
    handleError(error) {
        if (error.status === 404) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
        throw new common_1.HttpException(error.message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
    }
    async createRoleCandidate(roleCandidateDto) {
        try {
            return await this.roleCandidatesService.createRoleCandidate(roleCandidateDto);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async addLinkedinToRole(businessEmail, businessNumber, businessName, personalNumber, profile_url, profileType, roleId, userId, clientId, prospectId) {
        try {
            return await this.roleCandidatesService.addLinkedinToRole(businessEmail, businessNumber, businessName, personalNumber, profile_url, profileType, roleId, userId, clientId, prospectId);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async getRoleCandidatesV1(roleId, userId, clientId, type, source) {
        try {
            return await this.roleCandidatesService.getRoleCandidatesV1(roleId, userId, clientId, type, source);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async getRoleCandidates(roleId, userId, clientId, type, page = 1, limit = 10) {
        try {
            return await this.roleCandidatesService.getRoleCandidates(roleId, userId, clientId, type, page, limit);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async changeRoleCandidateStatus(roleId, userId, profile_url) {
        try {
            return await this.roleCandidatesService.changeRoleCandidateStatus(profile_url, roleId, userId);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async deleteRoleCandidate(id) {
        try {
            return await this.roleCandidatesService.deleteRoleCandidate(id);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async addCvToRole(file, data) {
        try {
            const parsedData = JSON.parse(data);
            return await this.roleCandidatesService.addCvToRole(parsedData, file);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async getPendingLinkedinProfile() {
        try {
            return await this.roleCandidatesService.getPendingLinkedinProfile();
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async getRoleCandidateCount(roleId) {
        try {
            const countData = await this.roleCandidatesService.getRoleCandidateCount(roleId);
            return {
                ...countData,
                total_count: countData.linkedin_count + countData.cv_count,
            };
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async get360AndPreQualificationCandidates(roleId) {
        try {
            return await this.roleCandidatesService.get360AndPreQualificationCandidates(roleId);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async markProfileReadyForConnection(id) {
        try {
            return await this.roleCandidatesService.markProfileReadyForConnection(id);
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async getOneReadyToSendConnectionProfile() {
        try {
            return await this.roleCandidatesService.getOneReadyToSendConnectionProfile();
        }
        catch (error) {
            this.handleError(error);
        }
    }
    async updateConnectionRequestStatus(id, updateConnectionStatusDto) {
        try {
            return await this.roleCandidatesService.updateConnectionRequestStatus(id, updateConnectionStatusDto.connectionStatus, updateConnectionStatusDto.responseStatus);
        }
        catch (error) {
            this.handleError(error);
        }
    }
};
exports.RoleCandidatesController = RoleCandidatesController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new Role Candidate' }),
    (0, swagger_1.ApiBody)({ type: role_candidate_dto_1.RoleCandidateDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The role candidate has been successfully created.',
        type: role_candidate_dto_1.RoleCandidateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_candidate_dto_1.RoleCandidateDto]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "createRoleCandidate", null);
__decorate([
    (0, common_1.Post)('add-linkedin-to-role'),
    (0, swagger_1.ApiBody)({
        type: role_candidate_dto_1.RoleCandidateDto,
        description: 'Add LinkedIn profile to a role',
    }),
    (0, swagger_1.ApiConsumes)('application/json'),
    (0, swagger_1.ApiOperation)({ summary: 'Add LinkedIn profile to a role' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'LinkedIn profile added successfully',
        type: role_candidate_dto_1.RoleCandidateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error' }),
    __param(0, (0, common_1.Body)('businessEmail')),
    __param(1, (0, common_1.Body)('businessNumber')),
    __param(2, (0, common_1.Body)('personalEmail')),
    __param(3, (0, common_1.Body)('personalNumber')),
    __param(4, (0, common_1.Body)('profile_url')),
    __param(5, (0, common_1.Body)('profileType')),
    __param(6, (0, common_1.Body)('roleId', common_1.ParseIntPipe)),
    __param(7, (0, common_1.Body)('userId')),
    __param(8, (0, common_1.Body)('clientId')),
    __param(9, (0, common_1.Body)('prospectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Array, Array, Array, String, String, Number, String, Number, Number]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "addLinkedinToRole", null);
__decorate([
    (0, common_1.Get)('get-role-candidates'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all role candidates' }),
    (0, swagger_1.ApiQuery)({ name: 'roleId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'clientId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'source', required: false }),
    __param(0, (0, common_1.Query)('roleId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('clientId')),
    __param(3, (0, common_1.Query)('type')),
    __param(4, (0, common_1.Query)('source')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Number, String, String]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "getRoleCandidatesV1", null);
__decorate([
    (0, common_1.Get)('get-role-candidates-paginated'),
    (0, swagger_1.ApiOperation)({ summary: 'Get role candidates with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'roleId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'clientId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Role candidates retrieved successfully with pagination',
    }),
    __param(0, (0, common_1.Query)('roleId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('clientId')),
    __param(3, (0, common_1.Query)('type')),
    __param(4, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(5, (0, common_1.Query)('limit', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, Number, String, Object, Object]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "getRoleCandidates", null);
__decorate([
    (0, common_1.Get)('change-role-candidate-status'),
    (0, swagger_1.ApiOperation)({ summary: 'Change the status of a role candidate' }),
    (0, swagger_1.ApiQuery)({ name: 'roleId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: true }),
    (0, swagger_1.ApiQuery)({ name: 'profile_url', required: false }),
    __param(0, (0, common_1.Query)('roleId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('profile_url')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String, String]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "changeRoleCandidateStatus", null);
__decorate([
    (0, common_1.Delete)('delete-role-candidate'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a role candidate' }),
    (0, swagger_1.ApiQuery)({ name: 'id', required: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Role candidate deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role candidate not found' }),
    __param(0, (0, common_1.Query)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "deleteRoleCandidate", null);
__decorate([
    (0, common_1.Post)('add-cv-to-role'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({ summary: 'Add CV to a role' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'CV added successfully',
        type: role_candidate_dto_1.RoleCandidateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role not found' }),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)('data')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "addCvToRole", null);
__decorate([
    (0, common_1.Get)('get-pending-linkedin-profile'),
    (0, swagger_1.ApiOperation)({ summary: 'Get pending LinkedIn profiles' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "getPendingLinkedinProfile", null);
__decorate([
    (0, common_1.Get)('get-role-candidate-count/:roleId'),
    (0, swagger_1.ApiParam)({ name: 'roleId', required: true, type: Number }),
    (0, swagger_1.ApiOperation)({ summary: 'Get LinkedIn and CV profile counts for a role' }),
    __param(0, (0, common_1.Param)('roleId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "getRoleCandidateCount", null);
__decorate([
    (0, common_1.Get)('get-360-and-pre-qualification-candidates/:roleId'),
    (0, swagger_1.ApiParam)({ name: 'roleId', required: true, type: Number }),
    (0, swagger_1.ApiOperation)({
        summary: 'Get 360 and pre-qualification candidates for a role',
    }),
    __param(0, (0, common_1.Param)('roleId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "get360AndPreQualificationCandidates", null);
__decorate([
    (0, common_1.Put)('mark-profile-ready-for-connection/:id'),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, type: Number }),
    (0, swagger_1.ApiOperation)({
        summary: 'Mark a profile as ready to send connection request',
        description: 'Updates the LinkedIn connection send status to READY_TO_SEND for the specified role candidate',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profile marked as ready for connection successfully',
        type: role_candidates_entity_1.RoleCandidate,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role candidate not found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "markProfileReadyForConnection", null);
__decorate([
    (0, common_1.Get)('get-ready-to-send-connection-profile'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get one profile ready to send connection request',
        description: 'Retrieves the oldest LinkedIn profile that is ready to send a connection request and marks it as SENT',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Ready to send profile retrieved successfully',
        type: role_candidates_entity_1.RoleCandidate,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'No profiles ready to send connection request found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "getOneReadyToSendConnectionProfile", null);
__decorate([
    (0, common_1.Put)('update-connection-request-status/:id'),
    (0, swagger_1.ApiParam)({ name: 'id', required: true, type: Number }),
    (0, swagger_1.ApiOperation)({
        summary: 'Update connection request status',
        description: 'Updates the LinkedIn connection send status and optionally the response status for a role candidate',
    }),
    (0, swagger_1.ApiBody)({ type: update_connection_status_dto_1.UpdateConnectionStatusDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Connection request status updated successfully',
        type: role_candidates_entity_1.RoleCandidate,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Role candidate not found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal Server Error' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_connection_status_dto_1.UpdateConnectionStatusDto]),
    __metadata("design:returntype", Promise)
], RoleCandidatesController.prototype, "updateConnectionRequestStatus", null);
exports.RoleCandidatesController = RoleCandidatesController = __decorate([
    (0, swagger_1.ApiTags)('role candidates'),
    (0, common_1.Controller)('role-candidates'),
    __metadata("design:paramtypes", [role_candidates_service_1.RoleCandidatesService])
], RoleCandidatesController);
//# sourceMappingURL=role_candidates.controller.js.map