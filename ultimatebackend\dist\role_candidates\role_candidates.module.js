"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidatesModule = void 0;
const common_1 = require("@nestjs/common");
const role_candidates_service_1 = require("./role_candidates.service");
const role_candidates_controller_1 = require("./role_candidates.controller");
const typeorm_1 = require("@nestjs/typeorm");
const role_candidates_entity_1 = require("./role_candidates.entity");
const roles_entity_1 = require("../roles/roles.entity");
const people_entity_1 = require("../people/people.entity");
const company_entity_1 = require("../company/company.entity");
const emails_entity_1 = require("../emails/emails.entity");
const phone_entity_1 = require("../phone/phone.entity");
const qualifications_entity_1 = require("../qualifications/qualifications.entity");
const langauges_entity_1 = require("../languages/langauges.entity");
const skills_entity_1 = require("../skills/skills.entity");
const s3bucket_service_1 = require("../s3bucket/s3bucket.service");
const role_candidate_log_module_1 = require("./role_candidate_log.module");
const recruitment_service_1 = require("./recruitment/recruitment.service");
const recruitment_controller_1 = require("./recruitment/recruitment.controller");
let RoleCandidatesModule = class RoleCandidatesModule {
};
exports.RoleCandidatesModule = RoleCandidatesModule;
exports.RoleCandidatesModule = RoleCandidatesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                role_candidates_entity_1.RoleCandidate,
                roles_entity_1.Roles,
                people_entity_1.People,
                company_entity_1.Company,
                emails_entity_1.PersonEmail,
                phone_entity_1.PersonPhone,
                qualifications_entity_1.Qualifications,
                langauges_entity_1.Languages,
                skills_entity_1.PersonSkill,
            ]),
            role_candidate_log_module_1.RoleCandidateLogModule,
        ],
        providers: [role_candidates_service_1.RoleCandidatesService, s3bucket_service_1.S3bucketService, recruitment_service_1.RecruitmentService],
        controllers: [role_candidates_controller_1.RoleCandidatesController, recruitment_controller_1.RecruitmentController],
    })
], RoleCandidatesModule);
//# sourceMappingURL=role_candidates.module.js.map