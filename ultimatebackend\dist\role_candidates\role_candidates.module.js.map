{"version": 3, "file": "role_candidates.module.js", "sourceRoot": "", "sources": ["../../src/role_candidates/role_candidates.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,uEAAkE;AAClE,6EAAwE;AACxE,6CAAgD;AAChD,qEAAyD;AACzD,wDAA+C;AAC/C,2DAAkD;AAClD,8DAAqD;AACrD,2DAAuD;AACvD,wDAAqD;AACrD,mFAA0E;AAC1E,oEAA2D;AAC3D,2DAAuD;AACvD,mEAAgE;AAChE,2EAAqE;AACrE,2EAAuE;AACvE,iFAA6E;AAoBtE,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;CAAG,CAAA;AAAvB,oDAAoB;+BAApB,oBAAoB;IAlBhC,IAAA,eAAM,EAAC;QACN,OAAO,EAAE;YACP,uBAAa,CAAC,UAAU,CAAC;gBACvB,sCAAa;gBACb,oBAAK;gBACL,sBAAM;gBACN,wBAAO;gBACP,2BAAW;gBACX,0BAAW;gBACX,sCAAc;gBACd,4BAAS;gBACT,2BAAW;aACZ,CAAC;YACF,kDAAsB;SACvB;QACD,SAAS,EAAE,CAAC,+CAAqB,EAAE,kCAAe,EAAE,wCAAkB,CAAC;QACvE,WAAW,EAAE,CAAC,qDAAwB,EAAE,8CAAqB,CAAC;KAC/D,CAAC;GACW,oBAAoB,CAAG"}