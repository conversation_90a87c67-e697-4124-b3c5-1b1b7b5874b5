import { RoleCandidate } from './role_candidates.entity';
import { Repository } from 'typeorm';
import { RoleCandidateDto } from './dto/role_candidate.dto';
import { Roles } from 'src/roles/roles.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Languages } from 'src/languages/langauges.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';
export declare class RoleCandidatesService {
    private readonly roleCandidateRepository;
    private readonly rolesRepository;
    private readonly peopleRepository;
    private readonly personEmailRepository;
    private readonly personPhoneRepository;
    private readonly qualificationsRepository;
    private readonly languagesRepository;
    private readonly personSkillRepository;
    private readonly s3BucketService;
    constructor(roleCandidateRepository: Repository<RoleCandidate>, rolesRepository: Repository<Roles>, peopleRepository: Repository<People>, personEmailRepository: Repository<PersonEmail>, personPhoneRepository: Repository<PersonPhone>, qualificationsRepository: Repository<Qualifications>, languagesRepository: Repository<Languages>, personSkillRepository: Repository<PersonSkill>, s3BucketService: S3bucketService);
    createRoleCandidate(roleCandidateData: RoleCandidateDto): Promise<RoleCandidate>;
    addLinkedinToRole(businessEmail: string[], businessNumber: string[], personalEmail: string[], personalNumber: string[], profile_url: string, profileType: string, roleId: number, userId: string, clientId: number, prospectId: number, is_willing_to_relocate?: boolean): Promise<RoleCandidate>;
    private addEmails;
    private addPhones;
    getRoleCandidatesV1(roleId: number, userId: string, clientId?: number, type?: string, source?: string): Promise<RoleCandidate[]>;
    getRoleCandidates(roleId: number, userId: string, clientId?: number, type?: string, page?: number, limit?: number): Promise<{
        data: RoleCandidate[];
        total: number;
    }>;
    changeRoleCandidateStatus(profile_url: string, roleId: number, userId: string): Promise<RoleCandidate[]>;
    deleteRoleCandidate(id: number): Promise<{
        success: boolean;
        message: string;
    }>;
    addCvToRole(data: any, file: Express.Multer.File): Promise<RoleCandidate>;
    getPendingLinkedinProfile(): Promise<any>;
    getRoleCandidateCount(roleId: number): Promise<{
        linkedin_count: number;
        cv_count: number;
        total_count: number;
    }>;
    get360AndPreQualificationCandidates(roleId: number): Promise<RoleCandidate[]>;
    markProfileReadyForConnection(roleCandidateId: number): Promise<RoleCandidate>;
    getOneReadyToSendConnectionProfile(): Promise<RoleCandidate>;
    updateConnectionRequestStatus(roleCandidateId: number, connectionStatus: 'SENT' | 'NOT_SENT' | 'READY_TO_SEND' | 'RECEIVED' | 'NOT_RECEIVED', responseStatus?: string): Promise<RoleCandidate>;
}
