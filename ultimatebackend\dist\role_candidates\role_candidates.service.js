"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleCandidatesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_candidates_entity_1 = require("./role_candidates.entity");
const typeorm_2 = require("typeorm");
const roles_entity_1 = require("../roles/roles.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
const phone_entity_1 = require("../phone/phone.entity");
const qualifications_entity_1 = require("../qualifications/qualifications.entity");
const langauges_entity_1 = require("../languages/langauges.entity");
const skills_entity_1 = require("../skills/skills.entity");
const people_enums_1 = require("../people/dto/people.enums");
const s3bucket_service_1 = require("../s3bucket/s3bucket.service");
let RoleCandidatesService = class RoleCandidatesService {
    constructor(roleCandidateRepository, rolesRepository, peopleRepository, personEmailRepository, personPhoneRepository, qualificationsRepository, languagesRepository, personSkillRepository, s3BucketService) {
        this.roleCandidateRepository = roleCandidateRepository;
        this.rolesRepository = rolesRepository;
        this.peopleRepository = peopleRepository;
        this.personEmailRepository = personEmailRepository;
        this.personPhoneRepository = personPhoneRepository;
        this.qualificationsRepository = qualificationsRepository;
        this.languagesRepository = languagesRepository;
        this.personSkillRepository = personSkillRepository;
        this.s3BucketService = s3BucketService;
    }
    async createRoleCandidate(roleCandidateData) {
        try {
            const role = await this.rolesRepository.findOneBy({
                id: roleCandidateData.roleId,
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const roleCandidate = this.roleCandidateRepository.create(roleCandidateData);
            roleCandidate.role = role;
            return await this.roleCandidateRepository.save({ ...roleCandidate });
        }
        catch (error) {
            console.error('Error creating role candidate:', error);
            throw new common_1.InternalServerErrorException('Failed to create role candidate');
        }
    }
    async addLinkedinToRole(businessEmail, businessNumber, personalEmail, personalNumber, profile_url, profileType, roleId, userId, clientId, prospectId, is_willing_to_relocate) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: Number(roleId) });
            if (!role)
                throw new common_1.NotFoundException('Role not found');
            let profile = await this.peopleRepository.findOneBy({ profile_url });
            if (profile !== null) {
                const existingRoleCandidate = await this.roleCandidateRepository.findOne({
                    where: {
                        candidateId: profile?.id,
                        roleId: Number(roleId),
                    },
                });
                console.log('Checking for existing role candidate:', {
                    candidateId: profile?.id,
                    roleId: Number(roleId),
                });
                console.log('existing Profile:', existingRoleCandidate);
                if (existingRoleCandidate) {
                    return existingRoleCandidate;
                }
            }
            if (!profile) {
                profile = this.peopleRepository.create({
                    profile_url,
                    userId,
                    person_type: 'CANDIDATE',
                    profile_source: people_enums_1.PersonSource.LINKEDIN,
                });
                profile = await this.peopleRepository.save(profile);
            }
            const roleCandidate = this.roleCandidateRepository.create({
                source_type: 'LINKEDIN',
                profile_url,
                profile_source: people_enums_1.PersonSource.LINKEDIN,
                li_type: profileType,
                is_willing_to_relocate: is_willing_to_relocate ?? false,
                is_accepted: false,
                candidate_status: 'PENDING',
                role,
                roleId: Number(roleId),
                clientId,
                prospectId,
                userId,
                candidateId: profile.id,
            });
            await Promise.all([
                this.addEmails(profile.id, businessEmail, 'BUSINESS'),
                this.addEmails(profile.id, personalEmail, 'PERSONAL'),
                this.addPhones(profile.id, businessNumber, 'BUSINESS'),
                this.addPhones(profile.id, personalNumber, 'PERSONAL'),
            ]);
            return await this.roleCandidateRepository.save(roleCandidate);
        }
        catch (error) {
            console.error('Error adding LinkedIn to role:', error);
            throw new common_1.InternalServerErrorException({
                error: error.message,
                message: error.message,
            });
        }
    }
    async addEmails(personId, emails, type) {
        if (emails.length === 0)
            return;
        const lowerEmails = emails.map((e) => e.trim().toLowerCase());
        const uniqueEmails = [...new Set(lowerEmails)];
        for (const email of uniqueEmails) {
            try {
                const exists = await this.personEmailRepository.findOneBy({ email });
                if (exists)
                    continue;
                const entity = this.personEmailRepository.create({
                    email,
                    email_type: type,
                    personId,
                });
                await this.personEmailRepository.save(entity);
            }
            catch (err) {
                if (err.code === '23505')
                    continue;
                console.error('Error adding email:', email, err);
            }
        }
    }
    async addPhones(personId, phones, type) {
        if (phones.length === 0)
            return;
        const normalizedPhones = phones.map((p) => p.trim());
        const uniquePhones = [...new Set(normalizedPhones)];
        for (const phone of uniquePhones) {
            try {
                const exists = await this.personPhoneRepository.findOneBy({
                    phone_number: phone,
                });
                if (exists)
                    continue;
                const entity = this.personPhoneRepository.create({
                    phone_number: phone,
                    phone_type: type,
                    personId,
                });
                await this.personPhoneRepository.save(entity);
            }
            catch (err) {
                if (err.code === '23505')
                    continue;
                console.error('Error adding phone:', phone, err);
            }
        }
    }
    async getRoleCandidatesV1(roleId, userId, clientId, type, source) {
        try {
            const where = {};
            if (source) {
                if (source === 'LINKEDIN')
                    where['source_type'] = 'LINKEDIN';
                if (source === 'JOB_BOARD')
                    where['source_type'] = 'CV';
                if (source === 'JOB_POSTING')
                    where['source_type'] = 'JOB_POSTING';
            }
            if (roleId)
                where['roleId'] = roleId;
            if (clientId)
                where['clientId'] = clientId;
            if (type)
                where['source_type'] = type;
            const candidates = await this.roleCandidateRepository.find({
                where,
                relations: [
                    'candidate',
                    'candidate.emails',
                    'candidate.phones',
                    'candidate.qualifications',
                    'candidate.experiences',
                    'candidate.skills',
                    'candidate.languages',
                    'user',
                ],
            });
            return candidates;
        }
        catch (error) {
            console.error('Error fetching role candidates:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch role candidates');
        }
    }
    async getRoleCandidates(roleId, userId, clientId, type, page = 1, limit = 10) {
        try {
            const where = {};
            if (roleId)
                where['roleId'] = roleId;
            if (clientId)
                where['clientId'] = clientId;
            if (type)
                where['source_type'] = type;
            const [data, total] = await this.roleCandidateRepository.findAndCount({
                where,
                relations: [
                    'candidate',
                    'candidate.emails',
                    'candidate.phones',
                    'candidate.qualifications',
                    'candidate.experiences',
                    'candidate.skills',
                    'candidate.languages',
                    'user',
                ],
                skip: (page - 1) * limit,
                take: limit,
                order: { id: 'DESC' },
            });
            return { data, total };
        }
        catch (error) {
            console.error('Error fetching role candidates:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch role candidates');
        }
    }
    async changeRoleCandidateStatus(profile_url, roleId, userId) {
        try {
            const roleCandidate = await this.roleCandidateRepository.findOneBy({
                profile_url,
                roleId,
            });
            if (!roleCandidate)
                throw new common_1.NotFoundException('Role candidate not found');
            roleCandidate.candidate_status = 'PENDING';
            await this.roleCandidateRepository.save(roleCandidate);
            const updatedCandidates = await this.roleCandidateRepository.find({
                where: { roleId, userId },
                relations: [
                    'candidate',
                    'candidate.emails',
                    'candidate.phones',
                    'candidate.qualifications',
                    'candidate.experiences',
                    'candidate.skills',
                    'candidate.languages',
                ],
            });
            return updatedCandidates;
        }
        catch (error) {
            console.error('Error changing role candidate status:', error);
            throw new common_1.InternalServerErrorException('Failed to change role candidate status');
        }
    }
    async deleteRoleCandidate(id) {
        try {
            const roleCandidate = await this.roleCandidateRepository.findOneBy({
                id,
            });
            if (!roleCandidate) {
                throw new common_1.NotFoundException('Role candidate not found');
            }
            await this.roleCandidateRepository.delete(id);
            return {
                success: true,
                message: 'Role candidate deleted successfully',
            };
        }
        catch (error) {
            console.error('Error deleting role candidate:', error);
            throw new common_1.InternalServerErrorException('Failed to delete role candidate');
        }
    }
    async addCvToRole(data, file) {
        try {
            const { cv_status, is_accepted, userId, clientId, roleId, fullName, phoneNumber, email, current_title, domain, industry, city, country, postal_code, street, radius, salaryMin, education, experience, skills, monthBack, source, cv_text, } = data;
            const role = await this.rolesRepository.findOneBy({ id: Number(roleId) });
            if (!role)
                throw new common_1.NotFoundException('Role not found');
            let fileUrl = undefined;
            if (file) {
                fileUrl = await this.s3BucketService.uploadFile(file);
            }
            let person = await this.peopleRepository.findOneBy({
                profile_url: fileUrl,
            });
            if (!person) {
                const [firstName, ...rest] = fullName.split(' ');
                const lastName = rest.join(' ') || '';
                person = this.peopleRepository.create({
                    first_name: firstName,
                    last_name: lastName,
                    userId,
                    current_title,
                    person_type: 'CANDIDATE',
                    domain,
                    industry,
                    location: `${street}, ${city}, ${postal_code}, ${country}`,
                    cv_text: cv_text || '',
                    cv_path: fileUrl,
                    headline: current_title,
                    profile_source: people_enums_1.PersonSource.CVL,
                    profile_url: fileUrl,
                });
                person = await this.peopleRepository.save(person);
            }
            const existing = await this.roleCandidateRepository.findOne({
                where: { profile_url: fileUrl, roleId: Number(roleId) },
            });
            if (existing)
                return existing;
            const roleCandidate = this.roleCandidateRepository.create({
                source_type: 'CV',
                profile_url: fileUrl,
                profile_source: source,
                is_willing_to_relocate: false,
                is_accepted: is_accepted,
                candidate_status: cv_status,
                role,
                roleId: Number(roleId),
                clientId,
                prospectId: null,
                userId,
                candidateId: person.id,
                radius_miles: radius,
                salary: salaryMin,
                months_back: monthBack,
            });
            if (skills && skills.length > 0) {
                const skillEntities = skills.map((skill) => {
                    return this.personSkillRepository.create({
                        skill_name: skill,
                        personId: person.id,
                    });
                });
                await this.personSkillRepository.save(skillEntities);
            }
            return await this.roleCandidateRepository.save(roleCandidate);
        }
        catch (error) {
            console.error('Error in addCvToRole:', error);
            throw new common_1.InternalServerErrorException({
                message: error.messag || 'Failed to add CV to role',
                error: error.message,
            });
        }
    }
    async getPendingLinkedinProfile() {
        try {
            const pendingProfile = await this.roleCandidateRepository.findOne({
                where: { candidate_status: 'PENDING', profile_source: 'LINKEDIN' },
            });
            if (!pendingProfile) {
                throw new common_1.NotFoundException('No pending LinkedIn profiles found');
            }
            pendingProfile.candidate_status = 'IN_PROGRESS';
            await this.roleCandidateRepository.save(pendingProfile);
            return {
                profile: pendingProfile,
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch pending LinkedIn profiles');
        }
    }
    async getRoleCandidateCount(roleId) {
        try {
            const counts = await this.roleCandidateRepository
                .createQueryBuilder('candidate')
                .select('candidate.source_type', 'source_type')
                .addSelect('COUNT(*)', 'count')
                .where('candidate.roleId = :roleId', { roleId })
                .andWhere('candidate.source_type IN (:...sources)', {
                sources: ['LINKEDIN', 'CV'],
            })
                .groupBy('candidate.source_type')
                .getRawMany();
            const countMap = counts.reduce((acc, curr) => {
                const source = curr.source_type;
                const count = parseInt(curr.count, 10);
                if (source === 'LINKEDIN')
                    acc.linkedin_count = count;
                if (source === 'CV')
                    acc.cv_count = count;
                return acc;
            }, { linkedin_count: 0, cv_count: 0 });
            countMap.total_count = countMap.linkedin_count + countMap.cv_count;
            return countMap;
        }
        catch (error) {
            console.error('Error fetching role candidate count:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch role candidate count');
        }
    }
    async get360AndPreQualificationCandidates(roleId) {
        try {
            const candidates = await this.roleCandidateRepository.find({
                where: {
                    roleId,
                },
                relations: [
                    'role',
                    'candidate',
                    'candidate.emails',
                    'candidate.phones',
                ],
            });
            if (!candidates || candidates.length === 0) {
                throw new common_1.NotFoundException('No candidates found for this role');
            }
            return candidates;
        }
        catch (error) {
            console.error('Error fetching 360 and Pre-qualification candidates:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch 360 and Pre-qualification candidates');
        }
    }
    async markProfileReadyForConnection(roleCandidateId) {
        try {
            const roleCandidate = await this.roleCandidateRepository.findOne({
                where: { id: roleCandidateId },
                relations: ['candidate', 'role'],
            });
            if (!roleCandidate) {
                throw new common_1.NotFoundException('Role candidate not found');
            }
            roleCandidate.li_connection_send_status = 'READY_TO_SEND';
            return await this.roleCandidateRepository.save(roleCandidate);
        }
        catch (error) {
            console.error('Error marking profile ready for connection:', error);
            throw new common_1.InternalServerErrorException('Failed to mark profile ready for connection');
        }
    }
    async getOneReadyToSendConnectionProfile() {
        try {
            const readyProfile = await this.roleCandidateRepository.findOne({
                where: {
                    li_connection_send_status: 'READY_TO_SEND',
                    source_type: 'LINKEDIN'
                },
                relations: [
                    'candidate',
                    'candidate.emails',
                    'candidate.phones',
                    'role',
                    'user',
                ],
                order: { created_at: 'ASC' },
            });
            if (!readyProfile) {
                throw new common_1.NotFoundException('No profiles ready to send connection request found');
            }
            readyProfile.li_connection_send_status = 'SENT';
            await this.roleCandidateRepository.save(readyProfile);
            return readyProfile;
        }
        catch (error) {
            console.error('Error fetching ready to send connection profile:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch ready to send connection profile');
        }
    }
    async updateConnectionRequestStatus(roleCandidateId, connectionStatus, responseStatus) {
        try {
            const roleCandidate = await this.roleCandidateRepository.findOne({
                where: { id: roleCandidateId },
                relations: ['candidate', 'role'],
            });
            if (!roleCandidate) {
                throw new common_1.NotFoundException('Role candidate not found');
            }
            roleCandidate.li_connection_send_status = connectionStatus;
            if (responseStatus) {
                roleCandidate.li_connection_response_status = responseStatus;
            }
            return await this.roleCandidateRepository.save(roleCandidate);
        }
        catch (error) {
            console.error('Error updating connection request status:', error);
            throw new common_1.InternalServerErrorException('Failed to update connection request status');
        }
    }
};
exports.RoleCandidatesService = RoleCandidatesService;
exports.RoleCandidatesService = RoleCandidatesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_candidates_entity_1.RoleCandidate)),
    __param(1, (0, typeorm_1.InjectRepository)(roles_entity_1.Roles)),
    __param(2, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(3, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(4, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __param(5, (0, typeorm_1.InjectRepository)(qualifications_entity_1.Qualifications)),
    __param(6, (0, typeorm_1.InjectRepository)(langauges_entity_1.Languages)),
    __param(7, (0, typeorm_1.InjectRepository)(skills_entity_1.PersonSkill)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        s3bucket_service_1.S3bucketService])
], RoleCandidatesService);
//# sourceMappingURL=role_candidates.service.js.map