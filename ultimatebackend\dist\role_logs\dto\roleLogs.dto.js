"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleLogsDto = void 0;
const class_validator_1 = require("class-validator");
const rol_los_enum_1 = require("./rol_los.enum");
const swagger_1 = require("@nestjs/swagger");
class RoleLogsDto {
}
exports.RoleLogsDto = RoleLogsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Action type',
        enum: rol_los_enum_1.RoleLogsAction,
        required: true,
        example: rol_los_enum_1.RoleLogsAction.CREATE,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(rol_los_enum_1.RoleLogsAction),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp',
        type: Date,
        required: true,
        example: '2023-10-01T00:00:00Z',
    }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RoleLogsDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Details',
        type: String,
        required: false,
        example: 'Details about the action',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role number',
        type: Number,
        required: false,
        example: 12345,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleLogsDto.prototype, "role_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Log status type',
        enum: rol_los_enum_1.RoleLogsStatus,
        required: false,
        example: rol_los_enum_1.RoleLogsStatus.PENDING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(rol_los_enum_1.RoleLogsStatus),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "log_status_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Log status at',
        enum: rol_los_enum_1.RoleLogsType,
        required: false,
        example: rol_los_enum_1.RoleLogsType.RESOURCER,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(rol_los_enum_1.RoleLogsType),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "log_status_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start time',
        type: Date,
        required: false,
        example: '2023-10-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RoleLogsDto.prototype, "start_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End time',
        type: Date,
        required: false,
        example: '2023-10-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RoleLogsDto.prototype, "end_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comment',
        type: String,
        required: false,
        example: 'This is a comment',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "comment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time spent',
        type: String,
        required: false,
        example: '2 hours',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "time_spent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Is done',
        type: Boolean,
        required: false,
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RoleLogsDto.prototype, "is_done", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status',
        type: String,
        required: false,
        example: 'active',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Role ID',
        type: Number,
        required: false,
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleLogsDto.prototype, "roleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        type: Number,
        required: false,
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleLogsDto.prototype, "userId", void 0);
//# sourceMappingURL=roleLogs.dto.js.map