import { RoleLogsService } from './role_logs.service';
import { RoleLogsDto } from './dto/roleLogs.dto';
import { RoleLogs } from './role_logs.entity';
import { UpdateRoleLogsDto } from './dto/updateRoleLogs.dto';
import { GetTrialLogsByRoleIdDto } from './dto/role_logs.dto';
export declare class RoleLogsController {
    private readonly roleLogsService;
    constructor(roleLogsService: RoleLogsService);
    createRoleLog(roleLogDto: RoleLogsDto): Promise<RoleLogs>;
    getAllRoleLogs(query: any): Promise<RoleLogs[]>;
    startRoleLog(roleId: number, userId: string): Promise<RoleLogs>;
    markRoleDone(roleId: number, userId: string): Promise<RoleLogs>;
    markRoleLeft(roleId: number, userId: string): Promise<RoleLogs>;
    getTrialLogsByRoleId(queryParams: GetTrialLogsByRoleIdDto): Promise<{
        cvsourcingRoles: RoleLogs[];
        preQualificationRoles: RoleLogs[];
        directRoles: RoleLogs[];
        trialRoles: RoleLogs[];
    }>;
    markRoleChecked(roleId: number, userId: string): Promise<RoleLogs>;
    getRoleLogById(id: number): Promise<RoleLogs>;
    updateRoleLog(id: number, updateRoleLogDto: UpdateRoleLogsDto): Promise<RoleLogs>;
    deleteRoleLog(id: number): Promise<void>;
    getRoleLogsByRoleId(roleId: number): Promise<RoleLogs[]>;
    getRoleActivityByRoleId(roleId: number): Promise<RoleLogs[]>;
    changeRoleStatus(roleLogId: number, status: string): Promise<RoleLogs>;
}
