"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleLogsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const role_logs_service_1 = require("./role_logs.service");
const roleLogs_dto_1 = require("./dto/roleLogs.dto");
const updateRoleLogs_dto_1 = require("./dto/updateRoleLogs.dto");
const role_logs_dto_1 = require("./dto/role_logs.dto");
let RoleLogsController = class RoleLogsController {
    constructor(roleLogsService) {
        this.roleLogsService = roleLogsService;
    }
    async createRoleLog(roleLogDto) {
        return this.roleLogsService.createRoleLog(roleLogDto);
    }
    async getAllRoleLogs(query) {
        return this.roleLogsService.getRoleLogs();
    }
    async startRoleLog(roleId, userId) {
        if (!roleId || !userId) {
            throw new Error('roleId and userId are required');
        }
        return this.roleLogsService.startRole(roleId, userId);
    }
    async markRoleDone(roleId, userId) {
        if (!roleId || !userId) {
            throw new Error('roleId and userId are required');
        }
        return this.roleLogsService.MarkRoleDone(roleId, userId);
    }
    async markRoleLeft(roleId, userId) {
        if (!roleId || !userId) {
            throw new Error('roleId and userId are required');
        }
        return this.roleLogsService.MarkRoleLeft(roleId, userId);
    }
    async getTrialLogsByRoleId(queryParams) {
        return this.roleLogsService.getTotalTrialLogs(queryParams);
    }
    async markRoleChecked(roleId, userId) {
        if (!roleId || !userId) {
            throw new Error('roleId and userId are required');
        }
        return this.roleLogsService.markRoleChecked(roleId, userId);
    }
    async getRoleLogById(id) {
        return this.roleLogsService.getRoleLogById(id);
    }
    async updateRoleLog(id, updateRoleLogDto) {
        return this.roleLogsService.updateRoleLog(id, updateRoleLogDto);
    }
    async deleteRoleLog(id) {
        return this.roleLogsService.deleteRoleLog(id);
    }
    async getRoleLogsByRoleId(roleId) {
        return this.roleLogsService.getRoleLogsByRoleId(roleId);
    }
    async getRoleActivityByRoleId(roleId) {
        return this.roleLogsService.getRoleActivityByRoleId(roleId);
    }
    async changeRoleStatus(roleLogId, status) {
        return this.roleLogsService.changeRoleLogStatus(roleLogId, status);
    }
};
exports.RoleLogsController = RoleLogsController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Create a new role log' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [roleLogs_dto_1.RoleLogsDto]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "createRoleLog", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get all role logs' }),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "getAllRoleLogs", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Start a role log' }),
    (0, common_1.Post)('start-role'),
    (0, swagger_1.ApiQuery)({
        name: 'roleId',
        required: true,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        required: true,
        type: String,
    }),
    __param(0, (0, common_1.Body)('roleId')),
    __param(1, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "startRoleLog", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Mark Role Done' }),
    (0, common_1.Post)('mark-role-done'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                roleId: { type: 'number' },
                userId: { type: 'string' },
            },
        },
    }),
    __param(0, (0, common_1.Body)('roleId')),
    __param(1, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "markRoleDone", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Leave a role' }),
    (0, common_1.Post)('leave-role'),
    (0, swagger_1.ApiQuery)({
        name: 'roleId',
        required: true,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        required: true,
        type: String,
    }),
    __param(0, (0, common_1.Body)('roleId')),
    __param(1, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "markRoleLeft", null);
__decorate([
    (0, common_1.Get)('getTotalTrialLogs'),
    (0, swagger_1.ApiOperation)({ summary: 'Get trial logs by roleId' }),
    (0, swagger_1.ApiQuery)({
        name: 'roleNumber',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clientNumber',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'roleDate',
        required: false,
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'end_date',
        required: false,
        type: Date,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'isAdvance',
        required: false,
        type: Boolean,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'isPrevious',
        required: false,
        type: Boolean,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'bdUserId',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'serviceId',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'acmUserId',
        required: false,
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'roleId',
        required: false,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchString',
        required: false,
        type: String,
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [role_logs_dto_1.GetTrialLogsByRoleIdDto]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "getTrialLogsByRoleId", null);
__decorate([
    (0, common_1.Post)('mark-role-checked'),
    (0, swagger_1.ApiOperation)({ summary: 'mark done in a role log' }),
    (0, swagger_1.ApiQuery)({
        name: 'roleId',
        required: true,
        type: Number,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        required: true,
        type: String,
    }),
    __param(0, (0, common_1.Body)('roleId')),
    __param(1, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "markRoleChecked", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get a role log by ID' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "getRoleLogById", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Update a role log by ID' }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateRoleLogs_dto_1.UpdateRoleLogsDto]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "updateRoleLog", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Delete a role log by ID' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "deleteRoleLog", null);
__decorate([
    (0, common_1.Get)(':roleId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get role logs by roleId' }),
    __param(0, (0, common_1.Param)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "getRoleLogsByRoleId", null);
__decorate([
    (0, common_1.Get)('role-activity/:roleId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get role logs by roleId' }),
    __param(0, (0, common_1.Param)('roleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "getRoleActivityByRoleId", null);
__decorate([
    (0, common_1.Put)('change-role-status/:roleLogId'),
    (0, swagger_1.ApiOperation)({ summary: 'Change role status' }),
    __param(0, (0, common_1.Param)('roleLogId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RoleLogsController.prototype, "changeRoleStatus", null);
exports.RoleLogsController = RoleLogsController = __decorate([
    (0, swagger_1.ApiTags)('RoleLogs'),
    (0, common_1.Controller)('role-logs'),
    __metadata("design:paramtypes", [role_logs_service_1.RoleLogsService])
], RoleLogsController);
//# sourceMappingURL=role_logs.controller.js.map