"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleLogsModule = void 0;
const common_1 = require("@nestjs/common");
const role_logs_service_1 = require("./role_logs.service");
const role_logs_controller_1 = require("./role_logs.controller");
const typeorm_1 = require("@nestjs/typeorm");
const role_logs_entity_1 = require("./role_logs.entity");
const roles_entity_1 = require("../roles/roles.entity");
const users_entity_1 = require("../users/users.entity");
let RoleLogsModule = class RoleLogsModule {
};
exports.RoleLogsModule = RoleLogsModule;
exports.RoleLogsModule = RoleLogsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([role_logs_entity_1.RoleLogs, roles_entity_1.Roles, users_entity_1.Users])],
        providers: [role_logs_service_1.RoleLogsService],
        controllers: [role_logs_controller_1.RoleLogsController],
    })
], RoleLogsModule);
//# sourceMappingURL=role_logs.module.js.map