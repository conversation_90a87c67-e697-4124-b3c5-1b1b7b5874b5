import { RoleLogs } from './role_logs.entity';
import { Repository } from 'typeorm';
import { Roles } from 'src/roles/roles.entity';
import { RoleLogsDto } from './dto/roleLogs.dto';
import { UpdateRoleLogsDto } from './dto/updateRoleLogs.dto';
import { GetTrialLogsByRoleIdDto } from './dto/role_logs.dto';
import { Users } from 'src/users/users.entity';
export declare class RoleLogsService {
    private readonly roleLogsRepository;
    private readonly rolesRepository;
    private readonly usersRepository;
    constructor(roleLogsRepository: Repository<RoleLogs>, rolesRepository: Repository<Roles>, usersRepository: Repository<Users>);
    createRoleLog(roleLog: RoleLogsDto): Promise<RoleLogs>;
    updateRoleLog(id: number, updateRoleLogDto: UpdateRoleLogsDto): Promise<RoleLogs>;
    deleteRoleLog(id: number): Promise<void>;
    getRoleLogs(): Promise<RoleLogs[]>;
    getRoleLogById(id: number): Promise<RoleLogs>;
    getRoleLogsByRoleIdAndUserId(roleId?: number, userId?: number): Promise<RoleLogs[]>;
    startRole(roleId: number, userId: string): Promise<RoleLogs>;
    MarkRoleDone(roleId: number, userId: string): Promise<RoleLogs>;
    MarkRoleLeft(roleId: number, userId: string): Promise<RoleLogs>;
    markRoleChecked(roleId: number, userId: string): Promise<RoleLogs>;
    getRoleLogsByRoleId(roleId: number): Promise<RoleLogs[]>;
    getTotalTrialLogs(queryParams: GetTrialLogsByRoleIdDto): Promise<{
        cvsourcingRoles: RoleLogs[];
        preQualificationRoles: RoleLogs[];
        directRoles: RoleLogs[];
        trialRoles: RoleLogs[];
    }>;
    getRoleActivityByRoleId(roleId: number): Promise<RoleLogs[]>;
    changeRoleLogStatus(roleLogId: number, status: string): Promise<RoleLogs>;
}
