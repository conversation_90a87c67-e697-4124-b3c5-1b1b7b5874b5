"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleLogsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_logs_entity_1 = require("./role_logs.entity");
const typeorm_2 = require("typeorm");
const roles_entity_1 = require("../roles/roles.entity");
const rol_los_enum_1 = require("./dto/rol_los.enum");
const users_entity_1 = require("../users/users.entity");
let RoleLogsService = class RoleLogsService {
    constructor(roleLogsRepository, rolesRepository, usersRepository) {
        this.roleLogsRepository = roleLogsRepository;
        this.rolesRepository = rolesRepository;
        this.usersRepository = usersRepository;
    }
    async createRoleLog(roleLog) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: roleLog.roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const newRoleLog = this.roleLogsRepository.create(roleLog);
            newRoleLog.role = role;
            return await this.roleLogsRepository.save(newRoleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error creating role log', error.message);
        }
    }
    async updateRoleLog(id, updateRoleLogDto) {
        try {
            const roleLog = await this.roleLogsRepository.findOneBy({ id });
            if (!roleLog) {
                throw new common_1.NotFoundException('Role log not found');
            }
            if (updateRoleLogDto.start_time && updateRoleLogDto.end_time) {
                const startTime = new Date(updateRoleLogDto.start_time).getTime();
                const endTime = new Date(updateRoleLogDto.end_time).getTime();
                const timeSpent = endTime - startTime;
                updateRoleLogDto.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
            }
            Object.assign(roleLog, updateRoleLogDto);
            if (updateRoleLogDto.log_status_type) {
                const role = await this.rolesRepository.findOneBy({
                    id: roleLog.roleId,
                });
                if (!role) {
                    throw new common_1.NotFoundException('Role not found');
                }
                role.current_status =
                    updateRoleLogDto.log_status_type +
                        ' at ' +
                        updateRoleLogDto.log_status_at;
                await this.rolesRepository.save(role);
            }
            return await this.roleLogsRepository.save(roleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error updating role log', error.message);
        }
    }
    async deleteRoleLog(id) {
        try {
            const result = await this.roleLogsRepository.delete(id);
            if (result.affected === 0) {
                throw new common_1.NotFoundException('Role log not found');
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error deleting role log', error.message);
        }
    }
    async getRoleLogs() {
        try {
            return await this.roleLogsRepository.find({ relations: ['role'] });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role logs', error.message);
        }
    }
    async getRoleLogById(id) {
        try {
            const roleLog = await this.roleLogsRepository.findOne({
                where: { id },
                relations: ['role'],
            });
            if (!roleLog) {
                throw new common_1.NotFoundException('Role log not found');
            }
            return roleLog;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role log', error.message);
        }
    }
    async getRoleLogsByRoleIdAndUserId(roleId, userId) {
        try {
            const query = this.roleLogsRepository
                .createQueryBuilder('roleLog')
                .leftJoinAndSelect('roleLog.role', 'role')
                .leftJoinAndSelect('roleLog.user', 'user');
            if (roleId) {
                query.andWhere('role.id = :roleId', { roleId });
            }
            if (userId) {
                query.andWhere('user.id = :userId', { userId });
            }
            return await query.getMany();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role logs', error.message);
        }
    }
    async startRole(roleId, userId) {
        try {
            if (!userId) {
                throw new common_1.BadRequestException('User ID is required');
            }
            const user = await this.usersRepository.findOneBy({
                id: userId,
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const role = await this.rolesRepository.findOneBy({ id: roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const lastRoleLog = await this.roleLogsRepository.findOne({
                where: { roleId },
                order: { start_time: 'DESC' },
            });
            if (lastRoleLog) {
                lastRoleLog.end_time = new Date();
                const startTime = new Date(lastRoleLog.start_time).getTime();
                const endTime = new Date(lastRoleLog.end_time).getTime();
                const timeSpent = endTime - startTime;
                lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
                await this.roleLogsRepository.save(lastRoleLog);
            }
            const newRoleLog = this.roleLogsRepository.create({
                action: rol_los_enum_1.RoleLogsAction.STARTED,
                timestamp: new Date(),
                details: `Started ${role.title} role`,
                role_number: role.role_number,
                log_status_type: rol_los_enum_1.RoleLogsStatus.IN_PROGRESS,
                log_status_at: rol_los_enum_1.RoleLogsType.RESOURCER,
                comment: `Started ${role.title} role`,
                time_spent: '0 minutes',
                status: rol_los_enum_1.RoleLogsStatus.IN_PROGRESS,
                next_stage: rol_los_enum_1.RoleLogsStatus.IN_PROGRESS + ' at ' + rol_los_enum_1.RoleLogsType.RESOURCER,
                start_time: new Date(),
                role_date: lastRoleLog.role_date,
                roleId: role.id,
                userId: userId,
            });
            role.current_status =
                rol_los_enum_1.RoleLogsStatus.IN_PROGRESS + ' at ' + rol_los_enum_1.RoleLogsType.RESOURCER;
            await this.rolesRepository.save(role);
            return await this.roleLogsRepository.save(newRoleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message || 'Error starting role log');
        }
    }
    async MarkRoleDone(roleId, userId) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const lastRoleLog = await this.roleLogsRepository.findOne({
                where: { roleId },
                order: { start_time: 'DESC' },
            });
            if (lastRoleLog) {
                lastRoleLog.end_time = new Date();
                const startTime = new Date(lastRoleLog.start_time).getTime();
                const endTime = new Date(lastRoleLog.end_time).getTime();
                const timeSpent = endTime - startTime;
                lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
                await this.roleLogsRepository.save(lastRoleLog);
            }
            const newRoleLog = this.roleLogsRepository.create({
                action: rol_los_enum_1.RoleLogsAction.DONE,
                timestamp: new Date(),
                details: `Marked ${role.title} role Done`,
                role_number: role.role_number,
                log_status_type: rol_los_enum_1.RoleLogsStatus.DONE,
                log_status_at: rol_los_enum_1.RoleLogsType.RESOURCER,
                comment: `Mark ${role.title} role Done`,
                time_spent: '0 minutes',
                next_stage: rol_los_enum_1.RoleLogsStatus.PENDING + ' at ' + rol_los_enum_1.RoleLogsType.QA,
                status: rol_los_enum_1.RoleLogsStatus.DONE,
                start_time: new Date(),
                roleId: role.id,
                userId: userId,
            });
            role.current_status =
                rol_los_enum_1.RoleLogsStatus.DONE + ' at ' + rol_los_enum_1.RoleLogsType.RESOURCER;
            await this.rolesRepository.save(role);
            return await this.roleLogsRepository.save(newRoleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error starting role log', error.message);
        }
    }
    async MarkRoleLeft(roleId, userId) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const lastRoleLog = await this.roleLogsRepository.findOne({
                where: { roleId },
                order: { start_time: 'DESC' },
            });
            if (lastRoleLog) {
                lastRoleLog.end_time = new Date();
                const startTime = new Date(lastRoleLog.start_time).getTime();
                const endTime = new Date(lastRoleLog.end_time).getTime();
                const timeSpent = endTime - startTime;
                lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
                await this.roleLogsRepository.save(lastRoleLog);
            }
            const newRoleLog = this.roleLogsRepository.create({
                action: rol_los_enum_1.RoleLogsAction.CANCELLED,
                timestamp: new Date(),
                details: `Marked ${role.title} role Left`,
                role_number: role.role_number,
                log_status_type: rol_los_enum_1.RoleLogsStatus.CANCELLED,
                log_status_at: rol_los_enum_1.RoleLogsType.RESOURCER,
                comment: `Mark ${role.title} role Left`,
                time_spent: '0 minutes',
                next_stage: rol_los_enum_1.RoleLogsStatus.PENDING + ' at ' + rol_los_enum_1.RoleLogsType.RESOURCER,
                status: rol_los_enum_1.RoleLogsStatus.CANCELLED,
                start_time: new Date(),
                roleId: role.id,
                userId: userId,
                role_date: lastRoleLog.role_date,
            });
            role.current_status =
                rol_los_enum_1.RoleLogsStatus.CANCELLED + ' at ' + rol_los_enum_1.RoleLogsType.RESOURCER;
            await this.rolesRepository.save(role);
            return await this.roleLogsRepository.save(newRoleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error starting role log', error.message);
        }
    }
    async markRoleChecked(roleId, userId) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            const lastRoleLog = await this.roleLogsRepository.findOne({
                where: { roleId },
                order: { start_time: 'DESC' },
            });
            if (lastRoleLog) {
                lastRoleLog.end_time = new Date();
                const startTime = new Date(lastRoleLog.start_time).getTime();
                const endTime = new Date(lastRoleLog.end_time).getTime();
                const timeSpent = endTime - startTime;
                lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
                await this.roleLogsRepository.save(lastRoleLog);
            }
            const newRoleLog = this.roleLogsRepository.create({
                action: rol_los_enum_1.RoleLogsAction.CHECKED,
                timestamp: new Date(),
                details: `Marked ${role.title} role Checked`,
                role_number: role.role_number,
                log_status_type: rol_los_enum_1.RoleLogsStatus.APPROVED,
                log_status_at: rol_los_enum_1.RoleLogsType.QA,
                comment: `Mark ${role.title} role Checked`,
                time_spent: '0 minutes',
                next_stage: rol_los_enum_1.RoleLogsStatus.PENDING + ' at ' + rol_los_enum_1.RoleLogsType.ACM,
                status: rol_los_enum_1.RoleLogsStatus.APPROVED,
                start_time: new Date(),
                roleId: role.id,
                userId: userId,
                role_date: lastRoleLog.role_date,
            });
            role.current_status = rol_los_enum_1.RoleLogsStatus.APPROVED + ' at ' + rol_los_enum_1.RoleLogsType.QA;
            await this.rolesRepository.save(role);
            return await this.roleLogsRepository.save(newRoleLog);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error starting role log', error.message);
        }
    }
    async getRoleLogsByRoleId(roleId) {
        try {
            const role = await this.rolesRepository.findOneBy({ id: roleId });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            return await this.roleLogsRepository.find({
                where: { roleId },
                relations: ['role', 'user'],
                order: { start_time: 'DESC' },
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role logs', error.message);
        }
    }
    async getTotalTrialLogs(queryParams) {
        const { roleNumber, clientNumber, roleDate, endDate, isAdvance, isPrevious, bdUserId, serviceId, userId, acmUserId, roleId, searchString, page, pageSize, } = queryParams;
        const pageInt = parseInt(page) || 0;
        const pageSizeInt = parseInt(pageSize) || 10;
        const role_date = typeof roleDate === 'string' ? roleDate.trim() : undefined;
        const end_date = typeof endDate === 'string' ? endDate.trim() : undefined;
        const isValidDate = (d) => !!d && !isNaN(new Date(d).getTime());
        try {
            if (isAdvance === true && isPrevious === true) {
                throw new common_1.BadRequestException({
                    message: 'isAdvance and isPrevious cannot be both true',
                });
            }
            const queryBuilder = this.roleLogsRepository
                .createQueryBuilder('roleLog')
                .leftJoinAndSelect('roleLog.role', 'role')
                .leftJoinAndSelect('role.person', 'person')
                .leftJoinAndSelect('roleLog.user', 'user')
                .leftJoinAndSelect('role.service', 'service');
            if (roleNumber !== undefined) {
                queryBuilder.andWhere('roleLog.role_number = :roleNumber', {
                    roleNumber,
                });
            }
            if (clientNumber !== undefined) {
                queryBuilder.andWhere('person.client_number = :clientNumber', {
                    clientNumber,
                });
            }
            if (isValidDate(role_date) && isValidDate(end_date)) {
                queryBuilder.andWhere('roleLog.role_date BETWEEN :start AND :end', {
                    start: isValidDate(role_date),
                    end: isValidDate(end_date),
                });
            }
            else if (role_date) {
                queryBuilder.andWhere('roleLog.role_date = :role_date', {
                    role_date,
                });
            }
            if (isAdvance === true) {
                queryBuilder.andWhere('roleLog.role_date > :currentDate', {
                    currentDate: new Date(),
                });
            }
            if (isPrevious === true) {
                queryBuilder.andWhere('roleLog.role_date < :currentDate', {
                    currentDate: new Date(),
                });
            }
            if (bdUserId) {
                queryBuilder.andWhere('role.bdUserId = :bdUserId', { bdUserId });
            }
            if (serviceId) {
                queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId });
            }
            if (userId) {
                queryBuilder.andWhere('role.userId = :userId', { userId });
            }
            if (acmUserId) {
                queryBuilder.andWhere('role.acmUserId = :acmUserId', { acmUserId });
            }
            if (roleId) {
                queryBuilder.andWhere('role.id = :roleId', { roleId });
            }
            if (searchString) {
                queryBuilder.andWhere('(role.title ILIKE :searchString)', {
                    searchString: `%${searchString}%`,
                });
            }
            const roleLogs = await queryBuilder.getMany();
            const groupedRoleLogs = roleLogs.reduce((acc, roleLog) => {
                if (!acc[roleLog.roleId]) {
                    acc[roleLog.roleId] = roleLog;
                }
                else {
                    const existingRoleLog = acc[roleLog.roleId];
                    if (roleLog.start_time > existingRoleLog.start_time) {
                        acc[roleLog.roleId] = roleLog;
                    }
                }
                return acc;
            }, {});
            const roleLogsArray = Object.values(groupedRoleLogs);
            roleLogsArray.sort((a, b) => {
                return (new Date(b.start_time).getTime() - new Date(a.start_time).getTime());
            });
            const paginate = (data) => {
                const start = pageInt * pageSizeInt;
                const end = start + pageSizeInt;
                return data.slice(start, end);
            };
            const cvsourcingRoles = paginate(roleLogsArray.filter((r) => r.role.serviceId === 1 && r.role.category !== 'TRIAL'));
            const preQualificationRoles = paginate(roleLogsArray.filter((r) => r.role.serviceId === 2 && r.role.category !== 'TRIAL'));
            const directRoles = paginate(roleLogsArray.filter((r) => r.role.serviceId === 3 && r.role.category !== 'TRIAL'));
            const trialRoles = paginate(roleLogsArray.filter((r) => r.role.category === 'TRIAL'));
            return {
                cvsourcingRoles,
                preQualificationRoles,
                directRoles,
                trialRoles,
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new Error('An error occurred while fetching role logs: ' + error.message);
        }
    }
    async getRoleActivityByRoleId(roleId) {
        try {
            console.log('Fetching role activity for roleId:', roleId);
            const roleLogs = await this.roleLogsRepository.find({
                where: { roleId },
                relations: ['role', 'user'],
                order: { start_time: 'DESC' },
            });
            return roleLogs ?? [];
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role activity', error.message);
        }
    }
    async changeRoleLogStatus(roleLogId, status) {
        try {
            switch (status) {
                case 'RESUMED':
                    const resumedRoleLog = await this.roleLogsRepository.findOneBy({
                        id: roleLogId,
                    });
                    if (!resumedRoleLog) {
                        throw new common_1.NotFoundException('Role log not found');
                    }
                    resumedRoleLog.status = rol_los_enum_1.RoleLogsStatus.IN_PROGRESS;
                    resumedRoleLog.next_stage =
                        rol_los_enum_1.RoleLogsStatus.IN_PROGRESS + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    resumedRoleLog.log_status_type = rol_los_enum_1.RoleLogsStatus.IN_PROGRESS;
                    resumedRoleLog.log_status_at = rol_los_enum_1.RoleLogsType.ACM;
                    resumedRoleLog.comment = 'Resumed role log';
                    resumedRoleLog.time_spent = '0 minutes';
                    resumedRoleLog.start_time = new Date();
                    resumedRoleLog.end_time = null;
                    resumedRoleLog.role_date = resumedRoleLog.role_date;
                    resumedRoleLog.action = rol_los_enum_1.RoleLogsAction.RESUMED;
                    resumedRoleLog.timestamp = new Date();
                    resumedRoleLog.roleId = resumedRoleLog.roleId;
                    resumedRoleLog.userId = resumedRoleLog.userId;
                    resumedRoleLog.role_number = resumedRoleLog.role_number;
                    resumedRoleLog.details = "Resumed ${resumedRoleLog.role.title} role";
                    const role = await this.rolesRepository.findOneBy({
                        id: resumedRoleLog.roleId,
                    });
                    if (!role) {
                        throw new common_1.NotFoundException('Role not found');
                    }
                    role.current_status =
                        rol_los_enum_1.RoleLogsStatus.IN_PROGRESS + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    await this.rolesRepository.save(role);
                    return await this.roleLogsRepository.save(resumedRoleLog);
                case 'PAUSED':
                    const pausedRoleLog = await this.roleLogsRepository.findOneBy({
                        id: roleLogId,
                    });
                    if (!pausedRoleLog) {
                        throw new common_1.NotFoundException('Role log not found');
                    }
                    pausedRoleLog.status = rol_los_enum_1.RoleLogsStatus.ON_HOLD;
                    pausedRoleLog.next_stage =
                        rol_los_enum_1.RoleLogsStatus.ON_HOLD + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    pausedRoleLog.log_status_type = rol_los_enum_1.RoleLogsStatus.ON_HOLD;
                    pausedRoleLog.log_status_at = rol_los_enum_1.RoleLogsType.ACM;
                    pausedRoleLog.comment = 'Paused role log';
                    pausedRoleLog.time_spent = '0 minutes';
                    pausedRoleLog.start_time = new Date();
                    pausedRoleLog.end_time = null;
                    pausedRoleLog.role_date = pausedRoleLog.role_date;
                    pausedRoleLog.action = rol_los_enum_1.RoleLogsAction.PAUSED;
                    pausedRoleLog.timestamp = new Date();
                    pausedRoleLog.roleId = pausedRoleLog.roleId;
                    pausedRoleLog.userId = pausedRoleLog.userId;
                    pausedRoleLog.role_number = pausedRoleLog.role_number;
                    pausedRoleLog.details = "Paused ${pausedRoleLog.role.title} role";
                    const pausedRole = await this.rolesRepository.findOneBy({
                        id: pausedRoleLog.roleId,
                    });
                    if (!pausedRole) {
                        throw new common_1.NotFoundException('Role not found');
                    }
                    pausedRole.current_status =
                        rol_los_enum_1.RoleLogsStatus.ON_HOLD + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    await this.rolesRepository.save(pausedRole);
                    return await this.roleLogsRepository.save(pausedRoleLog);
                case 'ONHOLD':
                    const onHoldRoleLog = await this.roleLogsRepository.findOneBy({
                        id: roleLogId,
                    });
                    if (!onHoldRoleLog) {
                        throw new common_1.NotFoundException('Role log not found');
                    }
                    onHoldRoleLog.status = rol_los_enum_1.RoleLogsStatus.ON_HOLD;
                    onHoldRoleLog.next_stage =
                        rol_los_enum_1.RoleLogsStatus.ON_HOLD + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    onHoldRoleLog.log_status_type = rol_los_enum_1.RoleLogsStatus.ON_HOLD;
                    onHoldRoleLog.log_status_at = rol_los_enum_1.RoleLogsType.ACM;
                    onHoldRoleLog.comment = 'On hold role log';
                    onHoldRoleLog.time_spent = '0 minutes';
                    onHoldRoleLog.start_time = new Date();
                    onHoldRoleLog.end_time = null;
                    onHoldRoleLog.role_date = onHoldRoleLog.role_date;
                    onHoldRoleLog.action = rol_los_enum_1.RoleLogsAction.UNASSIGN;
                    onHoldRoleLog.timestamp = new Date();
                    onHoldRoleLog.roleId = onHoldRoleLog.roleId;
                    onHoldRoleLog.userId = onHoldRoleLog.userId;
                    onHoldRoleLog.role_number = onHoldRoleLog.role_number;
                    onHoldRoleLog.details = "On hold ${onHoldRoleLog.role.title} role";
                    const holdedRole = await this.rolesRepository.findOneBy({
                        id: onHoldRoleLog.roleId,
                    });
                    if (!holdedRole) {
                        throw new common_1.NotFoundException('Role not found');
                    }
                    holdedRole.current_status =
                        rol_los_enum_1.RoleLogsStatus.ON_HOLD + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    await this.rolesRepository.save(holdedRole);
                    return await this.roleLogsRepository.save(onHoldRoleLog);
                case 'ISSUE':
                    const issueRoleLog = await this.roleLogsRepository.findOneBy({
                        id: roleLogId,
                    });
                    if (!issueRoleLog) {
                        throw new common_1.NotFoundException('Role log not found');
                    }
                    issueRoleLog.status = rol_los_enum_1.RoleLogsStatus.REJECTED;
                    issueRoleLog.next_stage =
                        rol_los_enum_1.RoleLogsStatus.REJECTED + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    issueRoleLog.log_status_type = rol_los_enum_1.RoleLogsStatus.REJECTED;
                    issueRoleLog.log_status_at = rol_los_enum_1.RoleLogsType.ACM;
                    issueRoleLog.comment = 'Issue role log';
                    issueRoleLog.time_spent = '0 minutes';
                    issueRoleLog.start_time = new Date();
                    issueRoleLog.end_time = null;
                    issueRoleLog.role_date = issueRoleLog.role_date;
                    issueRoleLog.action = rol_los_enum_1.RoleLogsAction.UNASSIGN;
                    issueRoleLog.timestamp = new Date();
                    issueRoleLog.roleId = issueRoleLog.roleId;
                    issueRoleLog.userId = issueRoleLog.userId;
                    issueRoleLog.role_number = issueRoleLog.role_number;
                    issueRoleLog.details = "Issue ${issueRoleLog.role.title} role";
                    const issuedRole = await this.rolesRepository.findOneBy({
                        id: issueRoleLog.roleId,
                    });
                    if (!issuedRole) {
                        throw new common_1.NotFoundException('Role not found');
                    }
                    issuedRole.current_status =
                        rol_los_enum_1.RoleLogsStatus.REJECTED + ' at ' + rol_los_enum_1.RoleLogsType.ACM;
                    await this.rolesRepository.save(issuedRole);
                    return await this.roleLogsRepository.save(issueRoleLog);
            }
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error changing role log status', error.message);
        }
    }
};
exports.RoleLogsService = RoleLogsService;
exports.RoleLogsService = RoleLogsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(role_logs_entity_1.RoleLogs)),
    __param(1, (0, typeorm_1.InjectRepository)(roles_entity_1.Roles)),
    __param(2, (0, typeorm_1.InjectRepository)(users_entity_1.Users)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], RoleLogsService);
//# sourceMappingURL=role_logs.service.js.map