"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddTrialFromWebsiteDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class AddTrialFromWebsiteDto {
}
exports.AddTrialFromWebsiteDto = AddTrialFromWebsiteDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Main title of the role', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "roleMainTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Relevant titles for the role',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "roleRelevantTitles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Location', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Postal code', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Radius in miles', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "radiusMiles", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Willing to relocate', required: false }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], AddTrialFromWebsiteDto.prototype, "willingToRelocate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Open to work', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "openToWork", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum salary', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "salaryMin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum salary', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "salaryMax", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Essential qualifications',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "essentialQualifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Essential requirements',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "essentialRequirements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Desirable or preferred requirements',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "desireableOrPreferredRequirements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Companies of interest',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "companiesOfInterest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Major reason for rejected CVs',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "majorReasonForRejectedCVs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Who do we want',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "whoDoWeWant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Who do we not want',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], AddTrialFromWebsiteDto.prototype, "whoDoWeNotWant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Contract or permanent', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "contractPermanent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Role industry', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "roleIndustry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Full name', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Job title', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "jobTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Business email', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "businessEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Company phone', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "companyPhone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Company website', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "companyWebsite", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Company industry', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "companyIndustry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Company name', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Country', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Attachments (e.g., CV, cover letter)',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], AddTrialFromWebsiteDto.prototype, "attachments", void 0);
//# sourceMappingURL=addTrialFromWebsite.dto.js.map