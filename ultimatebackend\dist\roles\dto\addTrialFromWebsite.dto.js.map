{"version": 3, "file": "addTrialFromWebsite.dto.js", "sourceRoot": "", "sources": ["../../../src/roles/dto/addTrialFromWebsite.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA2E;AAE3E,MAAa,sBAAsB;CA+KlC;AA/KD,wDA+KC;AA1KC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACvE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6DACU;AASvB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;kEACiB;AAK9B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACzD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wDACK;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0DACO;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2DACQ;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACpE,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;iEACe;AAK5B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0DACO;AAMpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yDACM;AAKnB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yDACM;AASnB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;uEACsB;AASnC;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;qEACoB;AASjC;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qCAAqC;QAClD,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;iFACgC;AAS7C;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;mEACkB;AAQ/B;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,+BAA+B;QAC5C,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yEACsB;AASnC;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;2DACU;AASvB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,oBAAoB;QACjC,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;;8DACa;AAK1B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACtE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;iEACc;AAK3B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4DACS;AAMtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wDACK;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wDACK;AAKlB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6DACU;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4DACS;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8DACW;AAKxB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACjE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;+DACY;AAMzB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7D,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2DACQ;AAKrB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACxD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;uDACI;AAUjB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sCAAsC;QACnD,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2DACQ"}