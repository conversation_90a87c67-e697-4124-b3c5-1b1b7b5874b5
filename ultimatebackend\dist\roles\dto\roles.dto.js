"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class RoleDto {
}
exports.RoleDto = RoleDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: ['REGULAR', 'TRIAL', 'FIX'],
        description: 'Category of the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['REGULAR', 'TRIAL', 'FIX']),
    __metadata("design:type", String)
], RoleDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: Date,
        description: 'Start date of the role (if FIX category)',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RoleDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: Date,
        description: 'End date of the role (if FIX category)',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], RoleDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: Number,
        description: 'Number of candidates required',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleDto.prototype, "candidates_required", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: Boolean,
        description: 'Indicates if it is a credit-based role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RoleDto.prototype, "is_credit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: Number, description: 'Role number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleDto.prototype, "role_number", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Title of the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Postal code for the role location',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "postal_code", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Client number associated with the role',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], RoleDto.prototype, "client_number", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Country for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: ['LOW', 'MEDIUM', 'HIGH'],
        description: 'Priority level of the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['LOW', 'MEDIUM', 'HIGH']),
    __metadata("design:type", String)
], RoleDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: ['FIXED', 'VARIABLE'],
        description: 'Salary payment type',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['FIXED', 'VARIABLE']),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_payment_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: String, description: 'Minimum salary offered' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: String, description: 'Maximum salary offered' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: String, description: 'Fixed salary amount' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_fixed", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: ['USD', 'GBP'],
        description: 'Currency for salary',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['USD', 'GBP']),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_currency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        enum: [
            'HOURLY',
            'DAILY',
            'WEEKLY',
            'FORTNIGHT',
            'MONTHLY',
            'BIANNUALLY',
            'ANNUALY',
        ],
        description: 'Salary type based on time period',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)([
        'HOURLY',
        'DAILY',
        'WEEKLY',
        'FORTNIGHT',
        'MONTHLY',
        'BIANNUALLY',
        'ANNUALY',
    ]),
    __metadata("design:type", String)
], RoleDto.prototype, "salary_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Current status of the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "current_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Locations for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "locations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: 'enum',
        enum: [
            'LESS_THAN_MONTH',
            '1MONTH',
            '3MONTHS',
            '6MONTHS',
            '9MONTHS',
            '12MONTHS',
            '15MONTHS',
            '18MONTHS',
            '24MONTHS',
            '36MONTHS',
            '48MONTHS',
            '60MONTHS',
            'MORE_THAN_60MONTHS',
        ],
        description: 'Months Back',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)([
        'LESS_THAN_MONTH',
        '1MONTH',
        '3MONTHS',
        '6MONTHS',
        '9MONTHS',
        '12MONTHS',
        '15MONTHS',
        '18MONTHS',
        '24MONTHS',
        '36MONTHS',
        '48MONTHS',
        '60MONTHS',
        'MORE_THAN_60MONTHS',
    ]),
    __metadata("design:type", String)
], RoleDto.prototype, "months_back", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Industry for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "industry", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: [String],
        description: 'Relevant titles for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], RoleDto.prototype, "relevant_titles", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        maxLength: 255,
        description: 'Radius in miles for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], RoleDto.prototype, "radius_miles", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: [String],
        description: 'Attachments for the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", String)
], RoleDto.prototype, "attachments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        description: 'User ID who created the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        description: 'Person ID associated with the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "personId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        description: 'ACM User ID who added the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "acmUserId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: String,
        description: 'BD User ID who added the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RoleDto.prototype, "bdUserId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        type: Number,
        description: 'Service ID associated with the role',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], RoleDto.prototype, "serviceId", void 0);
//# sourceMappingURL=roles.dto.js.map