{"version": 3, "file": "roles.dto.js", "sourceRoot": "", "sources": ["../../../src/roles/dto/roles.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAAmE;AACnE,qDASyB;AAEzB,MAAa,OAAO;CAwRnB;AAxRD,0BAwRC;AAjRC;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC;QACjC,WAAW,EAAE,sBAAsB;KACpC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;yCAClB;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,0CAA0C;KACxD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;8BACI,IAAI;2CAAC;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,IAAI;QACV,WAAW,EAAE,wCAAwC;KACtD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;8BACE,IAAI;yCAAC;AAQhB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,+BAA+B;KAC7C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;oDACqB;AAQ7B;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,wCAAwC;KACtD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;0CACQ;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;4CACa;AAUrB;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;sCACA;AAUf;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,mCAAmC;KACjD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;4CACM;AAQrB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,wCAAwC;KACtD,CAAC;IACD,IAAA,4BAAU,GAAE;;8CACU;AAUvB;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,sBAAsB;KACpC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;wCACE;AAQjB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;QAC/B,WAAW,EAAE,4BAA4B;KAC1C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;;yCAChB;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;QAC3B,WAAW,EAAE,qBAAqB;KACnC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;oDACD;AAK7B;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC5E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACS;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC5E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACS;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IACzE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACW;AAQtB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACpB,WAAW,EAAE,qBAAqB;KACnC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;gDACE;AAwBzB;IAtBC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE;YACJ,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,WAAW;YACX,SAAS;YACT,YAAY;YACZ,SAAS;SACV;QACD,WAAW,EAAE,kCAAkC;KAChD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,WAAW;QACX,SAAS;QACT,YAAY;QACZ,SAAS;KACV,CAAC;;4CACmB;AAUrB;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,4BAA4B;KAC1C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;+CACS;AAUxB;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,wBAAwB;KACtC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;0CACI;AAqCnB;IAnCC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,iBAAiB;YACjB,QAAQ;YACR,SAAS;YACT,SAAS;YACT,SAAS;YACT,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,oBAAoB;SACrB;QACD,WAAW,EAAE,aAAa;KAC3B,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC;QACN,iBAAiB;QACjB,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,oBAAoB;KACrB,CAAC;;4CACmB;AAUrB;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,uBAAuB;KACrC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;yCACG;AASlB;IAPC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;gDACE;AAU3B;IARC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,GAAG;QACd,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,GAAG,CAAC;;6CACO;AAQtB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,WAAW,EAAE,0BAA0B;KACxC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;4CACJ;AAQrB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uCACK;AAQhB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,oCAAoC;KAClD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yCACO;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,gCAAgC;KAC9C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0CACQ;AAQnB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,+BAA+B;KAC7C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yCACO;AAQlB;IANC,IAAA,6BAAmB,EAAC;QACnB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,qCAAqC;KACnD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;0CACW"}