"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateRoleDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const roles_dto_1 = require("./roles.dto");
const class_validator_1 = require("class-validator");
class UpdateRoleDto extends (0, swagger_1.PartialType)(roles_dto_1.RoleDto) {
}
exports.UpdateRoleDto = UpdateRoleDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'The ID of the role',
        required: true,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateRoleDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the role to be updated',
        required: true,
    }),
    __metadata("design:type", String)
], UpdateRoleDto.prototype, "attachment_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the role to be updated',
        required: true,
    }),
    __metadata("design:type", Number)
], UpdateRoleDto.prototype, "roleId", void 0);
//# sourceMappingURL=updateRole.dto.js.map