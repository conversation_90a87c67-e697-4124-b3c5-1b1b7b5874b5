import { RolesService } from './roles.service';
import { RoleDto } from './dto/roles.dto';
import { UpdateRoleDto } from './dto/updateRole.dto';
import { AddTrialFromWebsiteDto } from './dto/addTrialFromWebsite.dto';
export declare class RolesController {
    private readonly rolesService;
    constructor(rolesService: RolesService);
    createRole(role: RoleDto): Promise<import("./roles.entity").Roles>;
    updateRole(id: number, role: UpdateRoleDto): Promise<import("./roles.entity").Roles>;
    findRoleById(id: number): Promise<import("./roles.entity").Roles>;
    findRoleByTitle(title: string): Promise<import("./roles.entity").Roles>;
    findRoleByCategory(category: string): Promise<import("./roles.entity").Roles[]>;
    findRoleByService(serviceId: number): Promise<import("./roles.entity").Roles[]>;
    getServiceStats(serviceId: number, start_date: Date, end_date: Date): Promise<{
        service_name: string;
        total: number;
    }[]>;
    findRoleByServiceOrCategory(serviceId: number, category: string): Promise<import("./roles.entity").Roles[]>;
    getTotalTrialRoles(start_date: Date, end_date: Date, isAdvance: boolean, isPrevious: boolean, type: 'cvsourcing' | 'preQualification' | 'direct' | 'trial', bdUserId: string, serviceId: number, userId: string, acmUserId: string, roleId: number, searchString: string, roleNumber: string, clientNumber: string): Promise<{
        cvsourcingRoles: import("./roles.entity").Roles[];
        preQualificationRoles: import("./roles.entity").Roles[];
        directRoles: import("./roles.entity").Roles[];
        trialRoles: import("./roles.entity").Roles[];
    }>;
    findCvSourcingRoles(isAdvance: string, isPrevious: string, start_date: Date, end_date: Date): Promise<{
        category: string;
        roles: import("./roles.entity").Roles[];
    }[]>;
    find360_PreQualificationRoles(isAdvance: string, isPrevious: string, start_date: Date, end_date: Date, serviceId: number): Promise<{
        client_number: string;
        roles: import("./roles.entity").Roles[];
    }[]>;
    addRoleFromWebsite(role: AddTrialFromWebsiteDto): Promise<import("./roles.entity").Roles>;
}
