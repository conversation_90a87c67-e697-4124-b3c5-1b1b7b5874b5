"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesController = void 0;
const common_1 = require("@nestjs/common");
const roles_service_1 = require("./roles.service");
const swagger_1 = require("@nestjs/swagger");
const roles_dto_1 = require("./dto/roles.dto");
const updateRole_dto_1 = require("./dto/updateRole.dto");
const addTrialFromWebsite_dto_1 = require("./dto/addTrialFromWebsite.dto");
let RolesController = class RolesController {
    constructor(rolesService) {
        this.rolesService = rolesService;
    }
    async createRole(role) {
        return this.rolesService.createRole(role);
    }
    async updateRole(id, role) {
        return this.rolesService.updateRole(id, role);
    }
    async findRoleById(id) {
        return this.rolesService.findRoleById(id);
    }
    async findRoleByTitle(title) {
        return this.rolesService.findRoleByTitle(title);
    }
    async findRoleByCategory(category) {
        return this.rolesService.findRoleByCategory(category);
    }
    async findRoleByService(serviceId) {
        return this.rolesService.findRoleByService(serviceId);
    }
    async getServiceStats(serviceId, start_date, end_date) {
        return this.rolesService.getRoleServiceStats(start_date, end_date, serviceId);
    }
    async findRoleByServiceOrCategory(serviceId, category) {
        return this.rolesService.findRoleByCategoryAndService(category, serviceId);
    }
    async getTotalTrialRoles(start_date, end_date, isAdvance, isPrevious, type, bdUserId, serviceId, userId, acmUserId, roleId, searchString, roleNumber, clientNumber) {
        return this.rolesService.findAllTrialRoles(start_date, end_date, isAdvance, isPrevious, type, bdUserId, serviceId, userId, acmUserId, roleId, searchString, roleNumber, clientNumber);
    }
    async findCvSourcingRoles(isAdvance, isPrevious, start_date, end_date) {
        const parsedIsAdvance = isAdvance === 'true';
        const parsedIsPrevious = isPrevious === 'true';
        return this.rolesService.findCvSourcingRoles(parsedIsAdvance, parsedIsPrevious, start_date, end_date);
    }
    async find360_PreQualificationRoles(isAdvance, isPrevious, start_date, end_date, serviceId) {
        const parsedIsAdvance = isAdvance === 'true';
        const parsedIsPrevious = isPrevious === 'true';
        return this.rolesService.find360_PreQualificationRoles(parsedIsAdvance, parsedIsPrevious, start_date, end_date, serviceId);
    }
    async addRoleFromWebsite(role) {
        return this.rolesService.addTrialFromWebsite(role);
    }
};
exports.RolesController = RolesController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new role' }),
    (0, swagger_1.ApiBody)({ type: roles_dto_1.RoleDto }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Role already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [roles_dto_1.RoleDto]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "createRole", null);
__decorate([
    (0, common_1.Put)('update/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a role' }),
    (0, swagger_1.ApiBody)({ type: updateRole_dto_1.UpdateRoleDto }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateRole_dto_1.UpdateRoleDto]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "updateRole", null);
__decorate([
    (0, common_1.Get)('find/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Find a role by ID' }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findRoleById", null);
__decorate([
    (0, common_1.Get)('find'),
    (0, swagger_1.ApiOperation)({ summary: 'Find roles by title' }),
    (0, swagger_1.ApiQuery)({ name: 'title', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('title')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findRoleByTitle", null);
__decorate([
    (0, common_1.Get)('findByCategory'),
    (0, swagger_1.ApiOperation)({ summary: 'Find roles by category' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findRoleByCategory", null);
__decorate([
    (0, common_1.Get)('findByService'),
    (0, swagger_1.ApiOperation)({ summary: 'Find roles by service ID' }),
    (0, swagger_1.ApiQuery)({ name: 'serviceId', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findRoleByService", null);
__decorate([
    (0, common_1.Get)('service/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get role statistics by service ID' }),
    (0, swagger_1.ApiQuery)({ name: 'serviceId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'start_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'end_date', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('serviceId')),
    __param(1, (0, common_1.Query)('start_date')),
    __param(2, (0, common_1.Query)('end_date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Date,
        Date]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "getServiceStats", null);
__decorate([
    (0, common_1.Get)('ByServiceOrCategory'),
    (0, swagger_1.ApiOperation)({ summary: 'Find roles by service ID or category' }),
    (0, swagger_1.ApiQuery)({ name: 'serviceId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: true }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('serviceId')),
    __param(1, (0, common_1.Query)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findRoleByServiceOrCategory", null);
__decorate([
    (0, common_1.Get)('totalTrialRoles'),
    (0, swagger_1.ApiOperation)({ summary: 'Get total trial roles' }),
    (0, swagger_1.ApiQuery)({ name: 'start_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'end_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'isAdvance', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'isPrevious', required: false }),
    (0, swagger_1.ApiQuery)({
        name: 'type',
        required: false,
        enum: ['cvsourcing', 'preQualification', 'direct', 'trial'],
    }),
    (0, swagger_1.ApiQuery)({ name: 'bdUserId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'serviceId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'acmUserId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'roleId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'searchString', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'roleNumber', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'clientNumber', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('start_date')),
    __param(1, (0, common_1.Query)('end_date')),
    __param(2, (0, common_1.Query)('isAdvance')),
    __param(3, (0, common_1.Query)('isPrevious')),
    __param(4, (0, common_1.Query)('type')),
    __param(5, (0, common_1.Query)('bdUserId')),
    __param(6, (0, common_1.Query)('serviceId')),
    __param(7, (0, common_1.Query)('userId')),
    __param(8, (0, common_1.Query)('acmUserId')),
    __param(9, (0, common_1.Query)('roleId')),
    __param(10, (0, common_1.Query)('searchString')),
    __param(11, (0, common_1.Query)('roleNumber')),
    __param(12, (0, common_1.Query)('clientNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, Boolean, Boolean, String, String, Number, String, String, Number, String, String, String]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "getTotalTrialRoles", null);
__decorate([
    (0, common_1.Get)('findCvSourcingRoles'),
    (0, swagger_1.ApiOperation)({ summary: 'Find CV Sourcing Roles' }),
    (0, swagger_1.ApiQuery)({ name: 'isAdvance', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'isPrevious', required: false, type: Boolean }),
    (0, swagger_1.ApiQuery)({ name: 'start_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'end_date', required: false }),
    __param(0, (0, common_1.Query)('isAdvance')),
    __param(1, (0, common_1.Query)('isPrevious')),
    __param(2, (0, common_1.Query)('start_date')),
    __param(3, (0, common_1.Query)('end_date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Date,
        Date]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "findCvSourcingRoles", null);
__decorate([
    (0, common_1.Get)('find360_PreQualificationRoles'),
    (0, swagger_1.ApiOperation)({ summary: 'Find 360 PreQualification Roles' }),
    (0, swagger_1.ApiQuery)({ name: 'isAdvance', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'isPrevious', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'start_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'end_date', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'serviceId', required: false }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Query)('isAdvance')),
    __param(1, (0, common_1.Query)('isPrevious')),
    __param(2, (0, common_1.Query)('start_date')),
    __param(3, (0, common_1.Query)('end_date')),
    __param(4, (0, common_1.Query)('serviceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Date,
        Date, Number]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "find360_PreQualificationRoles", null);
__decorate([
    (0, common_1.Post)('addRoleFromWebsite'),
    (0, swagger_1.ApiOperation)({ summary: 'Add a role from the website' }),
    (0, swagger_1.ApiBody)({ type: addTrialFromWebsite_dto_1.AddTrialFromWebsiteDto }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [addTrialFromWebsite_dto_1.AddTrialFromWebsiteDto]),
    __metadata("design:returntype", Promise)
], RolesController.prototype, "addRoleFromWebsite", null);
exports.RolesController = RolesController = __decorate([
    (0, common_1.Controller)('roles'),
    (0, swagger_1.ApiTags)('Roles Management'),
    __metadata("design:paramtypes", [roles_service_1.RolesService])
], RolesController);
//# sourceMappingURL=roles.controller.js.map