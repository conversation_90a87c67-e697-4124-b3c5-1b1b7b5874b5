"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesModule = void 0;
const common_1 = require("@nestjs/common");
const roles_service_1 = require("./roles.service");
const roles_controller_1 = require("./roles.controller");
const typeorm_1 = require("@nestjs/typeorm");
const roles_entity_1 = require("./roles.entity");
const role_logs_entity_1 = require("../role_logs/role_logs.entity");
const role_history_entity_1 = require("../role-history/role-history.entity");
const people_entity_1 = require("../people/people.entity");
const company_entity_1 = require("../company/company.entity");
let RolesModule = class RolesModule {
};
exports.RolesModule = RolesModule;
exports.RolesModule = RolesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([roles_entity_1.Roles, role_logs_entity_1.RoleLogs, role_history_entity_1.RoleHistory, people_entity_1.People, company_entity_1.Company]),
        ],
        providers: [roles_service_1.RolesService],
        controllers: [roles_controller_1.RolesController],
    })
], RolesModule);
//# sourceMappingURL=roles.module.js.map