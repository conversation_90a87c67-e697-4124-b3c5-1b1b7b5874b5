import { Roles } from './roles.entity';
import { Repository } from 'typeorm';
import { RoleDto } from './dto/roles.dto';
import { UpdateRoleDto } from './dto/updateRole.dto';
import { RoleLogs } from 'src/role_logs/role_logs.entity';
import { RoleHistory } from 'src/role-history/role-history.entity';
import { AddTrialFromWebsiteDto } from './dto/addTrialFromWebsite.dto';
import { People } from 'src/people/people.entity';
import { Company } from 'src/company/company.entity';
export declare class RolesService {
    private rolesRepository;
    private roleLogsRepository;
    private roleHistoryRepository;
    private peopleRepository;
    private companyRepository;
    constructor(rolesRepository: Repository<Roles>, roleLogsRepository: Repository<RoleLogs>, roleHistoryRepository: Repository<RoleHistory>, peopleRepository: Repository<People>, companyRepository: Repository<Company>);
    createRole(role: RoleDto): Promise<Roles>;
    private buildRoleLog;
    updateRole(id: number, role: UpdateRoleDto): Promise<Roles>;
    findRoleById(id: number): Promise<Roles>;
    findRoleByTitle(title: string): Promise<Roles>;
    findRoleByCategory(category: string): Promise<Roles[]>;
    findRoleByPriority(priority: string): Promise<Roles[]>;
    findRoleBySalaryRange(min: string, max: string): Promise<Roles[]>;
    findRoleByPaymentType(paymentType: string): Promise<Roles[]>;
    findRoleByPostalCode(postalCode: string): Promise<Roles[]>;
    deleteRole(id: number): Promise<void>;
    getRoleServiceStats(start_date?: Date, end_date?: Date, serviceId?: number): Promise<{
        service_name: string;
        total: number;
    }[]>;
    findRoleByService(serviceId: number): Promise<Roles[]>;
    findRoleByCategoryAndService(category: string, serviceId?: number): Promise<Roles[]>;
    findRoleByPersonId(personId: string): Promise<Roles[]>;
    findAllTrialRoles(start_date?: Date, end_date?: Date, isAdvance?: boolean, isPrevious?: boolean, type?: 'cvsourcing' | 'preQualification' | 'direct' | 'trial', bdUserId?: string, serviceId?: number, userId?: string, acmUserId?: string, roleId?: number, searchString?: string, roleNumber?: string, clientNumber?: string): Promise<{
        cvsourcingRoles: Roles[];
        preQualificationRoles: Roles[];
        directRoles: Roles[];
        trialRoles: Roles[];
    }>;
    findCvSourcingRoles(isAdvance?: boolean, isPrevious?: boolean, start_date?: Date, end_date?: Date): Promise<{
        category: string;
        roles: Roles[];
    }[]>;
    find360_PreQualificationRoles(isAdvance?: boolean, isPrevious?: boolean, start_date?: Date, end_date?: Date, serviceId?: number): Promise<{
        client_number: string;
        roles: Roles[];
    }[]>;
    addTrialFromWebsite(trialRole: AddTrialFromWebsiteDto): Promise<Roles>;
}
