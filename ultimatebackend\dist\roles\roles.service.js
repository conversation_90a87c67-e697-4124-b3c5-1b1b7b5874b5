"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const roles_entity_1 = require("./roles.entity");
const typeorm_2 = require("typeorm");
const role_logs_entity_1 = require("../role_logs/role_logs.entity");
const rol_los_enum_1 = require("../role_logs/dto/rol_los.enum");
const role_history_entity_1 = require("../role-history/role-history.entity");
const people_entity_1 = require("../people/people.entity");
const company_entity_1 = require("../company/company.entity");
let RolesService = class RolesService {
    constructor(rolesRepository, roleLogsRepository, roleHistoryRepository, peopleRepository, companyRepository) {
        this.rolesRepository = rolesRepository;
        this.roleLogsRepository = roleLogsRepository;
        this.roleHistoryRepository = roleHistoryRepository;
        this.peopleRepository = peopleRepository;
        this.companyRepository = companyRepository;
    }
    async createRole(role) {
        try {
            if (role.category === 'FIX' && (!role.start_date || !role.end_date)) {
                throw new common_1.BadRequestException('Start and end date are required for FIX category');
            }
            role.current_status = 'PENDING at RESOURCER';
            const newRole = this.rolesRepository.create(role);
            const savedRole = await this.rolesRepository.save(newRole);
            const baseLogData = {
                roleId: savedRole.id,
                details: `Role ${savedRole.title} created`,
                role_number: savedRole.role_number,
                comment: `Role ${savedRole.title} created`,
                userId: role.userId,
            };
            if (role.category === 'FIX') {
                const startDate = new Date(role.start_date);
                const endDate = new Date(role.end_date);
                const logs = [];
                for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                    logs.push(this.buildRoleLog(baseLogData, new Date(d)));
                }
                await this.roleLogsRepository.insert(logs);
            }
            else {
                const log = this.buildRoleLog(baseLogData, new Date());
                await this.roleLogsRepository.insert(log);
            }
            return savedRole;
        }
        catch (error) {
            console.error('Create Role Error:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Failed to create role',
                error: error.message,
            });
        }
    }
    buildRoleLog(base, timestamp) {
        const log = new role_logs_entity_1.RoleLogs();
        log.roleId = base.roleId;
        log.action = rol_los_enum_1.RoleLogsAction.CREATE;
        log.role_date = timestamp;
        log.timestamp = timestamp;
        log.details = base.details;
        log.role_number = base.role_number;
        log.log_status_type = rol_los_enum_1.RoleLogsStatus.PENDING;
        log.log_status_at = rol_los_enum_1.RoleLogsType.RESOURCER;
        log.start_time = timestamp;
        log.comment = base.comment;
        log.time_spent = '0';
        log.next_stage = `${rol_los_enum_1.RoleLogsStatus.PENDING} at ${rol_los_enum_1.RoleLogsType.RESOURCER}`;
        log.status = `${rol_los_enum_1.RoleLogsStatus.PENDING} at ${rol_los_enum_1.RoleLogsType.RESOURCER}`;
        log.userId = base.userId;
        return log;
    }
    async updateRole(id, role) {
        try {
            const existingRole = await this.rolesRepository.findOne({
                where: { id },
            });
            if (!existingRole) {
                throw new common_1.NotFoundException('Role not found');
            }
            if (role.category === 'FIX') {
                if (!role.start_date || !role.end_date) {
                    throw new common_1.BadRequestException('Start and end date are required for FIX category');
                }
            }
            console.log(typeof existingRole.attachments, 'sdfsdfdsffsdf', existingRole.attachments);
            if (role.attachments) {
                await this.roleHistoryRepository.save({
                    roleId: existingRole.id,
                    attachment_url: existingRole.attachments,
                });
            }
            const updatedRole = this.rolesRepository.merge(existingRole, role);
            const savedRole = await this.rolesRepository.save(updatedRole);
            return savedRole;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to update role', error.message);
        }
    }
    async findRoleById(id) {
        try {
            const role = await this.rolesRepository.findOne({
                where: { id },
                relations: ['roleLogs'],
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            return role;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find role', error.message);
        }
    }
    async findRoleByTitle(title) {
        try {
            const role = await this.rolesRepository.findOne({
                where: { title },
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            return role;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find role', error.message);
        }
    }
    async findRoleByCategory(category) {
        try {
            const roles = await this.rolesRepository.find({
                where: { category },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this category');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleByPriority(priority) {
        try {
            const roles = await this.rolesRepository.find({
                where: { priority },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this priority');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleBySalaryRange(min, max) {
        try {
            const roles = await this.rolesRepository.find({
                where: { salary_min: min, salary_max: max },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this salary range');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleByPaymentType(paymentType) {
        try {
            const roles = await this.rolesRepository.find({
                where: { salary_payment_type: paymentType },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this payment type');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleByPostalCode(postalCode) {
        try {
            const roles = await this.rolesRepository.find({
                where: { postal_code: postalCode },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this postal code');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async deleteRole(id) {
        try {
            const role = await this.rolesRepository.findOne({
                where: { id },
            });
            if (!role) {
                throw new common_1.NotFoundException('Role not found');
            }
            await this.rolesRepository.delete({ id });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to delete role', error.message);
        }
    }
    async getRoleServiceStats(start_date, end_date, serviceId) {
        try {
            console.log('Fetching roles service stats...');
            const servicesCount = await this.rolesRepository.count();
            if (servicesCount === 0) {
                return [];
            }
            const queryBuilder = this.rolesRepository
                .createQueryBuilder('roles')
                .leftJoin('roles.service', 'service')
                .select('service.id', 'service_id')
                .addSelect('service.name', 'service_name')
                .addSelect('COUNT(roles.id)', 'total')
                .groupBy('service.id, service.name');
            if (serviceId) {
                queryBuilder.andWhere('service.id = :serviceId', { serviceId });
            }
            if (start_date && end_date) {
                queryBuilder.andWhere('roles.start_date BETWEEN :start_date AND :end_date', { start_date, end_date });
            }
            else if (start_date) {
                queryBuilder.andWhere('roles.start_date >= :start_date', {
                    start_date,
                });
            }
            else if (end_date) {
                queryBuilder.andWhere('roles.start_date <= :end_date', {
                    end_date,
                });
            }
            const stats = await queryBuilder.getRawMany();
            return stats.length > 0 ? stats : [];
        }
        catch (error) {
            console.error('Error retrieving service client stats:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Error retrieving service client stats',
                error: error.message,
            });
        }
    }
    async findRoleByService(serviceId) {
        try {
            const roles = await this.rolesRepository.find({
                where: { serviceId },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this service');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleByCategoryAndService(category, serviceId) {
        try {
            const whereCondition = { category };
            if (serviceId) {
                whereCondition.serviceId = serviceId;
            }
            const roles = await this.rolesRepository.find({
                where: whereCondition,
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this category and service');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findRoleByPersonId(personId) {
        try {
            const roles = await this.rolesRepository.find({
                where: { personId },
            });
            if (!roles || roles.length === 0) {
                throw new common_1.NotFoundException('No roles found for this person');
            }
            return roles;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to find roles', error.message);
        }
    }
    async findAllTrialRoles(start_date, end_date, isAdvance, isPrevious, type, bdUserId, serviceId, userId, acmUserId, roleId, searchString, roleNumber, clientNumber) {
        try {
            console.log('jksdgfisdbfsdbfnsdf', Boolean(roleNumber), Boolean(clientNumber));
            const queryBuilder = this.rolesRepository
                .createQueryBuilder('role')
                .leftJoinAndSelect('role.service', 'service')
                .leftJoinAndSelect('role.person', 'person')
                .leftJoinAndSelect('role.roleLogs', 'roleLogs')
                .leftJoinAndSelect('role.focus_points', 'focus_points');
            if (type === 'cvsourcing') {
                queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 1 });
            }
            else if (type === 'preQualification') {
                queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 2 });
            }
            else if (type === 'direct') {
                queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 3 });
            }
            else if (type === 'trial') {
                queryBuilder.andWhere('role.category = :category', {
                    category: 'TRIAL',
                });
            }
            const startStr = start_date?.toString().slice(0, 10);
            const endStr = end_date?.toString().slice(0, 10);
            if (startStr && endStr && startStr === endStr) {
                queryBuilder.andWhere('role.start_date = :start_date', {
                    start_date: startStr,
                });
            }
            else if (startStr && endStr) {
                queryBuilder.andWhere('role.start_date BETWEEN :start_date AND :end_date', {
                    start_date: startStr,
                    end_date: endStr,
                });
            }
            else if (startStr) {
                queryBuilder.andWhere('role.start_date >= :start_date', {
                    start_date: startStr,
                });
            }
            else if (endStr) {
                queryBuilder.andWhere('role.start_date <= :end_date', {
                    end_date: endStr,
                });
            }
            if (searchString?.trim()) {
                queryBuilder.andWhere(`
          role.title ILIKE :searchString
          OR CAST(role.role_number AS TEXT) ILIKE :searchString 
          OR CAST(role.client_number AS TEXT) ILIKE :searchString
        `, {
                    searchString: `%${searchString}%`,
                });
            }
            if (bdUserId)
                queryBuilder.andWhere('role.bdUserId = :bdUserId', { bdUserId });
            if (serviceId)
                queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId });
            if (userId)
                queryBuilder.andWhere('role.userId = :userId', { userId });
            if (acmUserId)
                queryBuilder.andWhere('role.acmUserId = :acmUserId', { acmUserId });
            if (roleId)
                queryBuilder.andWhere('role.id = :roleId', { roleId });
            queryBuilder.leftJoinAndSelect('role.user', 'user');
            queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
            queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');
            queryBuilder.orderBy('role.start_date', 'DESC');
            const roles = await queryBuilder.getMany();
            if (!roles || roles.length === 0) {
                return {
                    cvsourcingRoles: [],
                    preQualificationRoles: [],
                    directRoles: [],
                    trialRoles: [],
                };
            }
            const cvsourcingRoles = roles.filter((role) => role.service?.id === 1);
            const preQualificationRoles = roles.filter((role) => role.service?.id === 2);
            const directRoles = roles.filter((role) => role.service?.id === 3);
            const trialRoles = roles.filter((role) => role.category === 'TRIAL');
            return {
                cvsourcingRoles,
                preQualificationRoles,
                directRoles,
                trialRoles,
            };
        }
        catch (error) {
            console.error('Error retrieving roles:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Error retrieving roles',
                error: error.message,
            });
        }
    }
    async findCvSourcingRoles(isAdvance, isPrevious, start_date, end_date) {
        try {
            const queryBuilder = this.rolesRepository
                .createQueryBuilder('role')
                .leftJoinAndSelect('role.service', 'service')
                .leftJoinAndSelect('role.person', 'person')
                .leftJoinAndSelect('role.roleLogs', 'roleLogs')
                .where('role.serviceId = :serviceId', { serviceId: 1 });
            const today = new Date();
            today.setUTCHours(0, 0, 0, 0);
            if (isAdvance) {
                queryBuilder.andWhere('roleLogs.role_date > :today', { today });
            }
            if (isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date < :today', { today });
            }
            const normalizeDate = (date) => {
                const normalized = new Date(date);
                normalized.setUTCHours(0, 0, 0, 0);
                return normalized;
            };
            const start = start_date ? normalizeDate(start_date) : undefined;
            const end = end_date ? normalizeDate(end_date) : undefined;
            if (start && end && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date BETWEEN :start AND :end', {
                    start,
                    end,
                });
            }
            else if (start && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date >= :start', { start });
            }
            else if (end && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date <= :end', { end });
            }
            queryBuilder.leftJoinAndSelect('role.user', 'user');
            queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
            queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');
            const roles = await queryBuilder.getMany();
            if (!roles.length)
                return [];
            const groupedRoles = roles.reduce((acc, role) => {
                if (!acc[role.category]) {
                    acc[role.category] = [];
                }
                acc[role.category].push(role);
                return acc;
            }, {});
            return Object.entries(groupedRoles).map(([category, roles]) => ({
                category,
                roles,
            }));
        }
        catch (error) {
            console.error('Error retrieving CVSourcing roles:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Error retrieving CVSourcing roles',
                error: error.message,
            });
        }
    }
    async find360_PreQualificationRoles(isAdvance, isPrevious, start_date, end_date, serviceId) {
        try {
            const queryBuilder = this.rolesRepository
                .createQueryBuilder('role')
                .leftJoinAndSelect('role.service', 'service')
                .leftJoinAndSelect('role.person', 'person')
                .leftJoinAndSelect('role.roleLogs', 'roleLogs')
                .where('role.serviceId = :serviceId', { serviceId });
            if (isAdvance) {
                queryBuilder.andWhere('roleLogs.role_date > :today', {
                    today: new Date(),
                });
            }
            if (isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date < :today', {
                    today: new Date(),
                });
            }
            if (start_date && end_date && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date BETWEEN :start_date AND :end_date', {
                    start_date,
                    end_date,
                });
            }
            else if (start_date && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date >= :start_date', {
                    start_date,
                });
            }
            else if (end_date && !isAdvance && !isPrevious) {
                queryBuilder.andWhere('roleLogs.role_date <= :end_date', {
                    end_date,
                });
            }
            queryBuilder.leftJoinAndSelect('role.user', 'user');
            queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
            queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');
            const roles = await queryBuilder.getMany();
            if (!roles || roles.length === 0) {
                return [];
            }
            const groupedRoles = roles.reduce((acc, role) => {
                const clientNumber = role.client_number || 'Unknown';
                if (!acc[clientNumber]) {
                    acc[clientNumber] = [];
                }
                acc[clientNumber].push(role);
                return acc;
            }, {});
            return Object.entries(groupedRoles).map(([client_number, roles]) => ({
                client_number,
                roles,
            }));
        }
        catch (error) {
            console.error('Error retrieving PreQualification roles:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Error retrieving PreQualification roles',
                error: error.message,
            });
        }
    }
    async addTrialFromWebsite(trialRole) {
        try {
            const website = trialRole.companyWebsite?.trim();
            if (!website)
                throw new common_1.BadRequestException('Company website is required');
            const domainMatch = website.match(/(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
            const domain = domainMatch ? domainMatch[1].toLowerCase() : website.toLowerCase();
            if (!domain)
                throw new common_1.BadRequestException('Invalid company website');
            let company = await this.companyRepository.findOne({ where: { website: domain } });
            if (!company) {
                company = this.companyRepository.create({
                    name: trialRole.companyName,
                    website: domain,
                    company_country: trialRole.country,
                    industry: trialRole.companyIndustry,
                    company_phone: trialRole.companyPhone,
                });
                company = await this.companyRepository.save(company);
            }
            const personEmailRepo = this.peopleRepository.manager.getRepository('PersonEmail');
            let personEmail = await personEmailRepo.findOne({ where: { email: trialRole.businessEmail } });
            let person = null;
            if (personEmail?.personId) {
                person = await this.peopleRepository.findOne({ where: { id: personEmail.personId } });
            }
            if (!person) {
                person = this.peopleRepository.create({
                    first_name: trialRole.fullName.split(' ')[0],
                    last_name: trialRole.fullName.split(' ').slice(1).join(' '),
                    current_title: trialRole.jobTitle,
                    companyId: company.id,
                });
                person = await this.peopleRepository.save(person);
                const existingEmail = await personEmailRepo.findOne({ where: { email: trialRole.businessEmail } });
                if (!existingEmail) {
                    await personEmailRepo.save({
                        email: trialRole.businessEmail,
                        email_type: 'BUSINESS',
                        personId: person.id,
                    });
                }
            }
            else {
                if (person.companyId !== company.id) {
                    const existingPersonAtCompany = await this.peopleRepository.findOne({
                        where: {
                            first_name: trialRole.fullName.split(' ')[0],
                            last_name: trialRole.fullName.split(' ').slice(1).join(' '),
                            companyId: company.id,
                        },
                    });
                    if (existingPersonAtCompany) {
                        person = existingPersonAtCompany;
                    }
                    else {
                        person = this.peopleRepository.create({
                            first_name: trialRole.fullName.split(' ')[0],
                            last_name: trialRole.fullName.split(' ').slice(1).join(' '),
                            current_title: trialRole.jobTitle,
                            companyId: company.id,
                        });
                        person = await this.peopleRepository.save(person);
                    }
                    const alreadyLinked = await personEmailRepo.findOne({
                        where: { email: trialRole.businessEmail, personId: person.id },
                    });
                    if (!alreadyLinked) {
                        await personEmailRepo.save({
                            email: trialRole.businessEmail,
                            email_type: 'BUSINESS',
                            personId: person.id,
                        });
                    }
                }
            }
            const role = this.rolesRepository.create({
                title: trialRole.roleMainTitle,
                relevant_titles: trialRole.roleRelevantTitles,
                locations: trialRole.location,
                postal_code: trialRole.postalCode,
                radius_miles: trialRole.radiusMiles,
                willing_to_relocate: trialRole.willingToRelocate,
                open_to_work: trialRole.openToWork,
                salary_min: trialRole.salaryMin,
                salary_max: trialRole.salaryMax,
                essential_qualifications: trialRole.essentialQualifications,
                essential_requirements: trialRole.essentialRequirements,
                desireable_or_preferred_requirements: trialRole.desireableOrPreferredRequirements,
                companies_of_interest: trialRole.companiesOfInterest,
                major_reason_for_rejected_cvs: trialRole.majorReasonForRejectedCVs ? [trialRole.majorReasonForRejectedCVs] : [],
                who_do_we_want: trialRole.whoDoWeWant,
                who_do_we_not_want: trialRole.whoDoWeNotWant,
                employement_type: trialRole.contractPermanent,
                role_industry: trialRole.roleIndustry,
                attachments: trialRole.attachments,
                source: 'WEBSITE',
            });
            return await this.rolesRepository.save(role);
        }
        catch (error) {
            console.error('Error adding trial role:', error);
            throw new common_1.InternalServerErrorException({
                message: 'Failed to add trial role',
                error: error?.message || error,
            });
        }
    }
};
exports.RolesService = RolesService;
exports.RolesService = RolesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(roles_entity_1.Roles)),
    __param(1, (0, typeorm_1.InjectRepository)(role_logs_entity_1.RoleLogs)),
    __param(2, (0, typeorm_1.InjectRepository)(role_history_entity_1.RoleHistory)),
    __param(3, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(4, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], RolesService);
//# sourceMappingURL=roles.service.js.map