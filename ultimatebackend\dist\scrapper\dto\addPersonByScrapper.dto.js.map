{"version": 3, "file": "addPersonByScrapper.dto.js", "sourceRoot": "", "sources": ["../../../src/scrapper/dto/addPersonByScrapper.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA6C;AAE7C,MAAa,sBAAsB;CAyDlC;AAzDD,wDAyDC;AAvDC;IADC,IAAA,4BAAU,GAAE;;2DACO;AAGpB;IADC,IAAA,4BAAU,GAAE;;yDACK;AAGlB;IADC,IAAA,4BAAU,GAAE;;0DACM;AAGnB;IADC,IAAA,4BAAU,GAAE;;yDACK;AAGlB;IADC,IAAA,4BAAU,GAAE;;sDACE;AAGf;IADC,IAAA,4BAAU,GAAE;;0DACM;AAGnB;IADC,IAAA,4BAAU,GAAE;;wDACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;mEACe;AAG5B;IADC,IAAA,4BAAU,GAAE;;uDACG;AAGhB;IADC,IAAA,4BAAU,GAAE;;wDACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;6DACS;AAGtB;IADC,IAAA,4BAAU,GAAE;;uDACG;AAGhB;IADC,IAAA,4BAAU,GAAE;;uDACG;AAGhB;IADC,IAAA,4BAAU,GAAE;;yDACK;AAGlB;IADC,IAAA,4BAAU,GAAE;;0DACM;AAGnB;IADC,IAAA,4BAAU,GAAE;;qDACC;AAGd;IADC,IAAA,4BAAU,GAAE;;4DACQ;AAGrB;IADC,IAAA,4BAAU,GAAE;;gEACY;AAGzB;IADC,IAAA,4BAAU,GAAE;;qEACiB"}