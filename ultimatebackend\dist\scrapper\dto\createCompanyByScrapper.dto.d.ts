export declare class CreateCompanyByScrapperDto {
    public_id?: string;
    company_id?: string;
    profile_url?: string;
    profile_url_encoded?: string;
    name?: string;
    logo?: string;
    cover?: string;
    address?: string;
    phone_number?: string;
    website?: string;
    tagline?: string;
    staff_count?: number;
    staff_count_range_start?: number;
    staff_count_range_end?: number;
    follower_count?: number;
    actual_range?: number;
    description?: string;
    founded_on?: string;
    headquarter_country?: string;
    headquarter_city?: string;
    headquarter_geographic_area?: string;
    headquarter_line1?: string;
    headquarter_line2?: string;
    headquarter_postal_code?: string;
    industry?: string;
    specialities?: string[];
    company_email?: string;
    is_scrapped_fully?: boolean;
    scrapper_level?: number;
    user_id: string;
    sector_id: number;
    region: string;
    scrapper_profile_name: string;
}
export declare class UpdateCompanyByScrapperDto extends CreateCompanyByScrapperDto {
    id: number;
}
