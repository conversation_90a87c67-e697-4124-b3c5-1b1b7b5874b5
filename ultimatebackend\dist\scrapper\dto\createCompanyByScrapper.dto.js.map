{"version": 3, "file": "createCompanyByScrapper.dto.js", "sourceRoot": "", "sources": ["../../../src/scrapper/dto/createCompanyByScrapper.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAMyB;AAEzB,MAAa,0BAA0B;CAmGtC;AAnGD,gEAmGC;AAjGC;IADC,IAAA,4BAAU,GAAE;;6DACM;AAGnB;IADC,IAAA,4BAAU,GAAE;;8DACO;AAGpB;IADC,IAAA,4BAAU,GAAE;;+DACQ;AAGrB;IADC,IAAA,4BAAU,GAAE;;uEACgB;AAG7B;IADC,IAAA,4BAAU,GAAE;;wDACC;AAGd;IADC,IAAA,4BAAU,GAAE;;wDACC;AAGd;IADC,IAAA,4BAAU,GAAE;;yDACE;AAGf;IADC,IAAA,4BAAU,GAAE;;2DACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;gEACS;AAGtB;IADC,IAAA,4BAAU,GAAE;;2DACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;2DACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;+DACQ;AAGrB;IADC,IAAA,4BAAU,GAAE;;2EACoB;AAGjC;IADC,IAAA,4BAAU,GAAE;;yEACkB;AAG/B;IADC,IAAA,4BAAU,GAAE;;kEACW;AAGxB;IADC,IAAA,4BAAU,GAAE;;gEACS;AAGtB;IADC,IAAA,4BAAU,GAAE;;+DACQ;AAGrB;IADC,IAAA,4BAAU,GAAE;;8DACO;AAGpB;IADC,IAAA,4BAAU,GAAE;;uEACgB;AAG7B;IADC,IAAA,4BAAU,GAAE;;oEACa;AAG1B;IADC,IAAA,4BAAU,GAAE;;+EACwB;AAGrC;IADC,IAAA,4BAAU,GAAE;;qEACc;AAG3B;IADC,IAAA,4BAAU,GAAE;;qEACc;AAG3B;IADC,IAAA,4BAAU,GAAE;;2EACoB;AAGjC;IADC,IAAA,4BAAU,GAAE;;4DACK;AAGlB;IADC,IAAA,4BAAU,GAAE;;gEACW;AAGxB;IADC,IAAA,4BAAU,GAAE;;iEACU;AAGvB;IADC,IAAA,4BAAU,GAAE;;qEACe;AAG5B;IADC,IAAA,4BAAU,GAAE;;kEACW;AAGxB;IADC,IAAA,4BAAU,GAAE;;2DACG;AAGhB;IADC,IAAA,4BAAU,GAAE;;6DACK;AAGlB;IADC,IAAA,4BAAU,GAAE;;0DACE;AAGf;IADC,IAAA,4BAAU,GAAE;;yEACiB;AAGhC,MAAa,0BAA2B,SAAQ,0BAA0B;CAGzE;AAHD,gEAGC;AADC;IADC,IAAA,4BAAU,GAAE;;sDACF"}