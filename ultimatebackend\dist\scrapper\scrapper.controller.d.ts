import { ScrapperService } from './scrapper.service';
import { CreateCompanyByScrapperDto, UpdateCompanyByScrapperDto } from './dto/createCompanyByScrapper.dto';
import { AddPersonByScrapperDto } from './dto/addPersonByScrapper.dto';
import { AddJobByScrapperDto } from './dto/addJobByScrapper.dto';
import { ScrapperStatsDto } from './dto/scrapperStats.dto';
import { GetDailyScrapperReportDto } from './dto/getDailyScrapper.dto';
export declare class ScrapperController {
    private readonly scrapperService;
    constructor(scrapperService: ScrapperService);
    addCompanyByScrapper(data: CreateCompanyByScrapperDto): Promise<{
        message: string;
        company: import("../company/company.entity").Company;
    }>;
    updateCompanyByScrapper(data: UpdateCompanyByScrapperDto): Promise<{
        message: string;
        company: import("../company/company.entity").Company;
    }>;
    addPersonByScrapper(data: AddPersonByScrapperDto): Promise<{
        message: string;
        person: import("../people/people.entity").People;
        found: boolean;
    } | {
        message: any;
        found: boolean;
        person?: undefined;
    }>;
    addJobPostByScrapper(data: AddJobByScrapperDto): Promise<{
        message: string;
        job?: undefined;
    } | {
        message: string;
        job: import("../jobs/jobs.entity").Jobs;
    }>;
    addPersonOnly(data: AddPersonByScrapperDto): Promise<{
        message: string;
        person: import("../people/people.entity").People;
        found: boolean;
    } | {
        message: string;
        person: import("../people/people.entity").People;
        found?: undefined;
    }>;
    getCompanyLinkWithRegion(): Promise<{
        message: string;
        company: import("../company/company.entity").Company;
    } | {
        message: string;
        company?: undefined;
    }>;
    createScrapperDailyReport(data: ScrapperStatsDto): Promise<{
        message: string;
    }>;
    getDailyScrapperReport(queryParams: GetDailyScrapperReportDto): Promise<{
        data: import("./scrapperStats.entity").ScrapperStats[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
        };
    }>;
}
