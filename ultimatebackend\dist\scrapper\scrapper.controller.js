"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapperController = void 0;
const common_1 = require("@nestjs/common");
const scrapper_service_1 = require("./scrapper.service");
const createCompanyByScrapper_dto_1 = require("./dto/createCompanyByScrapper.dto");
const addPersonByScrapper_dto_1 = require("./dto/addPersonByScrapper.dto");
const addJobByScrapper_dto_1 = require("./dto/addJobByScrapper.dto");
const scrapperStats_dto_1 = require("./dto/scrapperStats.dto");
const getDailyScrapper_dto_1 = require("./dto/getDailyScrapper.dto");
let ScrapperController = class ScrapperController {
    constructor(scrapperService) {
        this.scrapperService = scrapperService;
    }
    async addCompanyByScrapper(data) {
        return this.scrapperService.createCompany(data);
    }
    async updateCompanyByScrapper(data) {
        return this.scrapperService.updateCompany(data);
    }
    async addPersonByScrapper(data) {
        return this.scrapperService.addPersonByScrapper(data);
    }
    async addJobPostByScrapper(data) {
        return this.scrapperService.addJobByScrapper(data);
    }
    async addPersonOnly(data) {
        return this.scrapperService.addPersonOnly(data);
    }
    async getCompanyLinkWithRegion() {
        return this.scrapperService.getCompanyLinkWithRegion();
    }
    async createScrapperDailyReport(data) {
        return this.scrapperService.createScrapperDailyReport(data);
    }
    async getDailyScrapperReport(queryParams) {
        return this.scrapperService.getDailyScrapperReport(queryParams);
    }
};
exports.ScrapperController = ScrapperController;
__decorate([
    (0, common_1.Post)('addCompanyByScrapper'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createCompanyByScrapper_dto_1.CreateCompanyByScrapperDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "addCompanyByScrapper", null);
__decorate([
    (0, common_1.Post)('updateCompanyByScrapper'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [createCompanyByScrapper_dto_1.UpdateCompanyByScrapperDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "updateCompanyByScrapper", null);
__decorate([
    (0, common_1.Post)('addPersonByScrapper'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [addPersonByScrapper_dto_1.AddPersonByScrapperDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "addPersonByScrapper", null);
__decorate([
    (0, common_1.Post)('addJobPostByScrapper'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [addJobByScrapper_dto_1.AddJobByScrapperDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "addJobPostByScrapper", null);
__decorate([
    (0, common_1.Post)('addPersonOnly'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [addPersonByScrapper_dto_1.AddPersonByScrapperDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "addPersonOnly", null);
__decorate([
    (0, common_1.Get)('getCompanyLinkWithRegion'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "getCompanyLinkWithRegion", null);
__decorate([
    (0, common_1.Post)('createScrapperDailyReport'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [scrapperStats_dto_1.ScrapperStatsDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "createScrapperDailyReport", null);
__decorate([
    (0, common_1.Get)('getDailyScrapperReport'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [getDailyScrapper_dto_1.GetDailyScrapperReportDto]),
    __metadata("design:returntype", Promise)
], ScrapperController.prototype, "getDailyScrapperReport", null);
exports.ScrapperController = ScrapperController = __decorate([
    (0, common_1.Controller)('scrapper'),
    __metadata("design:paramtypes", [scrapper_service_1.ScrapperService])
], ScrapperController);
//# sourceMappingURL=scrapper.controller.js.map