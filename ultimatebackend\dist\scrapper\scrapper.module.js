"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapperModule = void 0;
const common_1 = require("@nestjs/common");
const scrapper_service_1 = require("./scrapper.service");
const scrapper_controller_1 = require("./scrapper.controller");
const typeorm_1 = require("@nestjs/typeorm");
const company_entity_1 = require("../company/company.entity");
const country_entity_1 = require("../country/country.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
const phone_entity_1 = require("../phone/phone.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const scrapperStats_entity_1 = require("./scrapperStats.entity");
const company_scrapper_control_entity_1 = require("../company_scrapper_control/entities/company_scrapper_control.entity");
let ScrapperModule = class ScrapperModule {
};
exports.ScrapperModule = ScrapperModule;
exports.ScrapperModule = ScrapperModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                company_entity_1.Company,
                country_entity_1.Country,
                people_entity_1.People,
                emails_entity_1.PersonEmail,
                people_assignment_entity_1.PeopleAssignment,
                phone_entity_1.PersonPhone,
                jobs_entity_1.Jobs,
                scrapperStats_entity_1.ScrapperStats,
                company_scrapper_control_entity_1.CompanyScrapperControl,
            ]),
        ],
        controllers: [scrapper_controller_1.ScrapperController],
        providers: [scrapper_service_1.ScrapperService],
    })
], ScrapperModule);
//# sourceMappingURL=scrapper.module.js.map