import { Company } from 'src/company/company.entity';
import { Repository } from 'typeorm';
import { CreateCompanyByScrapperDto, UpdateCompanyByScrapperDto } from './dto/createCompanyByScrapper.dto';
import { Country } from 'src/country/country.entity';
import { AddPersonByScrapperDto } from './dto/addPersonByScrapper.dto';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { AddJobByScrapperDto } from './dto/addJobByScrapper.dto';
import { ScrapperStatsDto } from './dto/scrapperStats.dto';
import { ScrapperStats } from './scrapperStats.entity';
import { GetDailyScrapperReportDto } from './dto/getDailyScrapper.dto';
import { DataSource } from 'typeorm';
import { CompanyScrapperControl } from 'src/company_scrapper_control/entities/company_scrapper_control.entity';
export declare class ScrapperService {
    private companyRepository;
    private countryRepository;
    private peopleRepository;
    private personEmailRepository;
    private peopleAssignmentRepository;
    private personPhoneRepository;
    private jobsRepository;
    private scrapperStatsRepository;
    private companyScrapperControlRepository;
    private readonly dataSource;
    constructor(companyRepository: Repository<Company>, countryRepository: Repository<Country>, peopleRepository: Repository<People>, personEmailRepository: Repository<PersonEmail>, peopleAssignmentRepository: Repository<PeopleAssignment>, personPhoneRepository: Repository<PersonPhone>, jobsRepository: Repository<Jobs>, scrapperStatsRepository: Repository<ScrapperStats>, companyScrapperControlRepository: Repository<CompanyScrapperControl>, dataSource: DataSource);
    createCompany(data: CreateCompanyByScrapperDto): Promise<{
        message: string;
        company: Company;
    }>;
    getCountryIdFromCity(cityName: string): Promise<number>;
    updateCompany(data: UpdateCompanyByScrapperDto): Promise<{
        message: string;
        company: Company;
    }>;
    addPersonByScrapper(data: AddPersonByScrapperDto): Promise<{
        message: string;
        person: People;
        found: boolean;
    } | {
        message: any;
        found: boolean;
        person?: undefined;
    }>;
    addJobByScrapper(data: AddJobByScrapperDto): Promise<{
        message: string;
        job?: undefined;
    } | {
        message: string;
        job: Jobs;
    }>;
    addPersonOnly(data: AddPersonByScrapperDto): Promise<{
        message: string;
        person: People;
        found: boolean;
    } | {
        message: string;
        person: People;
        found?: undefined;
    }>;
    getCompanyLinkWithRegion(): Promise<{
        message: string;
        company: Company;
    } | {
        message: string;
        company?: undefined;
    }>;
    createScrapperDailyReport(data: ScrapperStatsDto): Promise<{
        message: string;
    }>;
    getDailyScrapperReport(queryParams: GetDailyScrapperReportDto): Promise<{
        data: ScrapperStats[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
        };
    }>;
}
