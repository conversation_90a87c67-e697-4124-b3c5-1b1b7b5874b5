"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScrapperService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const company_entity_1 = require("../company/company.entity");
const typeorm_2 = require("typeorm");
const country_entity_1 = require("../country/country.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
const people_assignment_entity_1 = require("../people-assignments/entities/people-assignment.entity");
const phone_entity_1 = require("../phone/phone.entity");
const jobs_entity_1 = require("../jobs/jobs.entity");
const UsStates_1 = require("../helper/UsStates");
const people_enums_1 = require("../people/dto/people.enums");
const scrapperStats_entity_1 = require("./scrapperStats.entity");
const typeorm_3 = require("typeorm");
const country_state_city_1 = require("country-state-city");
const company_scrapper_control_entity_1 = require("../company_scrapper_control/entities/company_scrapper_control.entity");
let ScrapperService = class ScrapperService {
    constructor(companyRepository, countryRepository, peopleRepository, personEmailRepository, peopleAssignmentRepository, personPhoneRepository, jobsRepository, scrapperStatsRepository, companyScrapperControlRepository, dataSource) {
        this.companyRepository = companyRepository;
        this.countryRepository = countryRepository;
        this.peopleRepository = peopleRepository;
        this.personEmailRepository = personEmailRepository;
        this.peopleAssignmentRepository = peopleAssignmentRepository;
        this.personPhoneRepository = personPhoneRepository;
        this.jobsRepository = jobsRepository;
        this.scrapperStatsRepository = scrapperStatsRepository;
        this.companyScrapperControlRepository = companyScrapperControlRepository;
        this.dataSource = dataSource;
    }
    async createCompany(data) {
        try {
            const processedProfileUrl = data.profile_url.replace('/life', '');
            const existingCompany = await this.companyRepository.findOne({
                where: {
                    profile_url: (0, typeorm_2.ILike)(`%${processedProfileUrl}%`),
                    name: data.name,
                },
            });
            if (existingCompany) {
                console.log('Company already exists');
                existingCompany.region = data.region;
                await this.companyRepository.save(existingCompany);
                return {
                    message: 'Company already exists',
                    company: existingCompany,
                };
            }
            let c_id = null;
            const countries = await this.countryRepository.find({
                select: ['id', 'name'],
            });
            const country = countries.find((country) => data.address.toLowerCase().includes(country.name.toLowerCase()));
            if (country) {
                c_id = country.id;
            }
            else {
                const company = await this.companyRepository.findOne({
                    where: {
                        address: (0, typeorm_2.ILike)(`%${data.address}%`),
                    },
                    relations: ['country'],
                });
                if (company && company.country) {
                    c_id = company.country.id;
                }
            }
            const newCompany = this.companyRepository.create({
                ...data,
                countryId: c_id,
                company_phone: data.phone_number,
                founded: data.founded_on,
                cover_photo: data.cover,
                profile_url: processedProfileUrl,
                sectorId: data.sector_id,
                scrapper_profile_name: data.scrapper_profile_name,
            });
            const savedCompany = await this.companyRepository.save(newCompany);
            console.log('New Company created by Scrapper !!!');
            return {
                message: 'Company created successfully',
                company: savedCompany,
            };
        }
        catch (error) {
            console.log('ERROR to create company: ', error);
            throw new common_1.InternalServerErrorException(`Failed to create new company: ${error.message}`);
        }
    }
    async getCountryIdFromCity(cityName) {
        const parts = cityName.split(',').map((part) => part.trim());
        if (parts.includes('New York')) {
            return 2;
        }
        if (parts.includes('London')) {
            return 1;
        }
        const allCities = country_state_city_1.City.getAllCities();
        let matchedCity = null;
        if (parts.length >= 2) {
            const cityPart = parts[0];
            const regionOrCountryPart = parts[1];
            const allCountries = country_state_city_1.Country.getAllCountries();
            const possibleCountry = allCountries.find((country) => country.name.toLowerCase() === regionOrCountryPart.toLowerCase() ||
                country.name
                    .toLowerCase()
                    .includes(regionOrCountryPart.toLowerCase()));
            if (possibleCountry) {
                matchedCity = allCities.find((city) => city.name.toLowerCase() === cityPart.toLowerCase() &&
                    city.countryCode === possibleCountry.isoCode);
            }
            if (!matchedCity) {
                const allStates = country_state_city_1.State.getAllStates();
                const possibleState = allStates.find((state) => state.name.toLowerCase() === regionOrCountryPart.toLowerCase());
                if (possibleState) {
                    matchedCity = allCities.find((city) => city.name.toLowerCase() === cityPart.toLowerCase() &&
                        city.stateCode === possibleState.isoCode &&
                        city.countryCode === possibleState.countryCode);
                }
            }
        }
        if (!matchedCity) {
            const cityPart = parts[0];
            const citiesWithName = allCities.filter((city) => city.name.toLowerCase() === cityPart.toLowerCase());
            if (citiesWithName.length > 1) {
                citiesWithName.forEach((city) => {
                    const country = country_state_city_1.Country.getCountryByCode(city.countryCode);
                });
                matchedCity =
                    citiesWithName.find((city) => city.countryCode === 'GB') ||
                        citiesWithName[0];
            }
            else {
                matchedCity = citiesWithName[0];
            }
        }
        if (!matchedCity) {
            console.log('City not found: ', cityName);
            return null;
        }
        const country = country_state_city_1.Country.getCountryByCode(matchedCity.countryCode);
        if (!country) {
            console.log('Country not found: ', matchedCity.countryCode);
            return null;
        }
        const dbCountry = await this.countryRepository.findOne({
            where: { name: country.name },
        });
        if (!dbCountry) {
            return null;
        }
        return dbCountry.id;
    }
    async updateCompany(data) {
        try {
            const processedProfileUrl = data.profile_url.replace('/life', '');
            const existingCompany = await this.companyRepository.findOne({
                where: { id: data.id },
            });
            if (!existingCompany) {
                console.log('Company not found');
                throw new Error('Company not found');
            }
            let countryId = null;
            if (existingCompany.countryId === null) {
                countryId = await this.getCountryIdFromCity(data.headquarter_country);
                if (countryId === null) {
                    countryId = null;
                }
            }
            const updateCompany = await this.companyRepository.update({ id: data.id }, {
                public_id: data.public_id,
                profile_url: data.profile_url
                    ? data.profile_url.replace('/life', '')
                    : undefined,
                profile_url_encoded: data.profile_url_encoded,
                logo: data.logo,
                cover_photo: data.cover,
                company_phone: data.phone_number,
                website: data.website,
                tagline: data.tagline,
                staff_count: data.staff_count,
                staff_count_range_start: data.staff_count_range_start,
                staff_count_range_end: data.staff_count_range_end,
                followers_count: isNaN(data.follower_count)
                    ? null
                    : data.follower_count,
                description: data.description,
                founded: data.founded_on,
                headquarter_country: data.headquarter_country,
                headquarter_city: data.headquarter_city,
                headquarter_geographic_area: data.headquarter_geographic_area,
                headquarter_line1: data.headquarter_line1,
                headquarter_line2: data.headquarter_line2,
                headquarter_postal_code: data.headquarter_postal_code,
                industry: data.industry,
                specialities: data.specialities,
                company_email: data.company_email,
                is_scrapped_fully: data.is_scrapped_fully,
                scrapper_level: data.scrapper_level,
                sectorId: data.sector_id,
                scrapper_profile_name: data.scrapper_profile_name,
                countryId: existingCompany.countryId === null
                    ? countryId
                    : existingCompany.countryId,
            });
            console.log('Company updated by Scrapper !!!');
            const updatedCompany = await this.companyRepository.findOne({
                where: { id: data.id },
            });
            return {
                message: 'Company updated successfully',
                company: updatedCompany,
            };
        }
        catch (error) {
            console.log('ERROR to update company: ', error);
            await this.companyRepository.update({ id: data.id }, {
                is_scrapped_fully: false,
                scrapper_level: 2,
            });
            throw new common_1.InternalServerErrorException(`Failed to update company: ${error.message}`);
        }
    }
    async addPersonByScrapper(data) {
        try {
            if (!data.full_name || !data.first_name || !data.last_name) {
                throw new Error('Full name, first name, and last name are required.');
            }
            if (!data.company_id) {
                throw new Error('Company ID is required.');
            }
            if (!data.sector_id) {
                throw new Error('Sector ID is required.');
            }
            const peopleCount = await this.peopleRepository.count({
                where: {
                    companyId: data.company_id,
                },
            });
            if (peopleCount >= 200) {
                throw new Error('You have reached the maximum limit of people against this company.');
            }
            const existingPerson = await this.peopleRepository.findOne({
                where: {
                    profile_url: data.profile_url,
                },
            });
            if (existingPerson) {
                const personId = existingPerson.id;
                await this.peopleRepository.update(personId, {
                    profile_img: data.avator,
                });
                if (data.email) {
                    const existingEmail = await this.personEmailRepository.findOne({
                        where: {
                            email: data.email,
                            personId: personId,
                        },
                    });
                    if (!existingEmail) {
                        await this.personEmailRepository.save({
                            email: data.email,
                            email_type: 'BUSINESS',
                            personId: personId,
                        });
                    }
                }
                if (data.phone_number) {
                    const existingPhone = await this.personPhoneRepository.findOne({
                        where: {
                            phone_number: data.phone_number,
                            personId: personId,
                        },
                    });
                    if (!existingPhone) {
                        await this.personPhoneRepository.save({
                            phone_number: data.phone_number,
                            personId: personId,
                            phone_type: 'BUSINESS',
                        });
                    }
                }
                console.log('Person with LinkedIn profile already exists.');
                return {
                    message: 'Person with LinkedIn profile already exists.',
                    person: existingPerson,
                    found: true,
                };
            }
            let c_id = null;
            const countries = await this.countryRepository.find({
                select: ['id', 'name'],
            });
            const country = countries.find((country) => {
                data.address.toLowerCase().includes(country.name.toLowerCase());
            });
            if (country) {
                c_id = country.id;
            }
            const newPerson = this.peopleRepository.create({
                full_name: data.full_name,
                first_name: data.first_name,
                last_name: data.last_name,
                headline: data.headline,
                profile_img: data.avator,
                profile_url: data.profile_url,
                current_title: data.current_title,
                SR_specied_industry: data.SR_specied_industry,
                industry: data.industry,
                summary: data.summary,
                location: data.address,
                is_hiring: data.is_hiring_person == 'true' ? true : false,
                companyId: data.company_id,
                sectorId: Number(data.sector_id),
                countryId: c_id ? c_id : data.country_id,
                profile_source: people_enums_1.PersonSource.JOB_POST_SCRAPPER,
                person_type: people_enums_1.PersonType.JOB_POST_LEAD,
                scrapper_profile_name: data.scrapper_profile_name,
            });
            if (newPerson) {
                if (data.email) {
                    await this.personEmailRepository.save({
                        email: data.email,
                        email_type: 'BUSINESS',
                        personId: newPerson.id,
                    });
                }
                if (data.phone_number) {
                    await this.personPhoneRepository.save({
                        phone_number: data.phone_number,
                        personId: newPerson.id,
                        phone_type: 'BUSINESS',
                    });
                }
            }
            console.log('New person created by scrapper!');
            await this.peopleRepository.save(newPerson);
            return {
                message: 'Person added successfully',
                person: newPerson,
                found: true,
            };
        }
        catch (error) {
            console.log('ERROR to add person: ', error);
            return {
                message: error.message,
                found: false,
            };
        }
    }
    async addJobByScrapper(data) {
        try {
            if (!data.company_id) {
                throw new Error('Company ID is required.');
            }
            if (!data.sector_id) {
                throw new Error('Sector ID is required.');
            }
            const existingJob = await this.jobsRepository.findOne({
                where: {
                    title: data.job_title,
                    companyId: data.company_id,
                    sectorId: data.sector_id,
                },
            });
            console.log('Job post with the same title already exists.');
            if (existingJob) {
                return {
                    message: 'Job post with the same title already exists.',
                };
            }
            const experienceLevelsMap = {
                Internship: 'INTERN',
                'Entry level': 'ENTRY_LEVEL',
                Associate: 'ASSOCIATE',
                'Mid-Senior level': 'MID_SENIOR_LEVEL',
                'Senior level': 'SENIOR_LEVEL',
                Director: 'DIRECTOR',
                Executive: 'EXECUTIVE',
            };
            const jobTypesMap = {
                'Full-time': 'FULL_TIME',
                'Part-time': 'PART_TIME',
                Contract: 'CONTRACT',
                Temporary: 'TEMPORARY',
                Internship: 'INTERNSHIP',
            };
            const remoteTypesMap = {
                'On-site': 'ONSITE',
                Remote: 'REMOTE',
                Hybrid: 'HYBRID',
            };
            let experience_level = null;
            let job_type = null;
            let job_location_type = null;
            let leftoverSalaryText = null;
            if (data.salary_experience_remote) {
                const patternParts = [
                    ...Object.keys(experienceLevelsMap),
                    ...Object.keys(jobTypesMap),
                    ...Object.keys(remoteTypesMap),
                ].map((str) => str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
                const regex = new RegExp(`(${patternParts.join('|')})`, 'g');
                const matches = data.salary_experience_remote.match(regex);
                leftoverSalaryText = data.salary_experience_remote;
                if (matches) {
                    matches.forEach((part) => {
                        leftoverSalaryText = leftoverSalaryText.replace(part, '').trim();
                        if (experienceLevelsMap[part]) {
                            experience_level = experienceLevelsMap[part];
                        }
                        else if (jobTypesMap[part]) {
                            job_type = jobTypesMap[part];
                        }
                        else if (remoteTypesMap[part]) {
                            job_location_type = remoteTypesMap[part];
                        }
                    });
                }
            }
            let city = null;
            let state = null;
            if (data.location) {
                const ukStates = ['England', 'Scotland', 'Wales', 'Northern Ireland'];
                data.location
                    .split(',')
                    .map((part) => part.trim())
                    .forEach((part) => {
                    if (UsStates_1.usStates.some((s) => s.full === part || s.short === part)) {
                        state = part;
                    }
                    else if (ukStates.includes(part)) {
                        state = part;
                    }
                    else {
                        city = part;
                    }
                });
            }
            let c_id = null;
            if (data.location) {
                const allCountries = await this.countryRepository.find({
                    select: ['id', 'name'],
                });
                const found = allCountries.find((country) => data.location.toLowerCase().includes(country.name.toLowerCase()));
                if (found) {
                    c_id = found.id;
                }
                else {
                    const fallbackJob = await this.jobsRepository.findOne({
                        where: [
                            { job_location_country: (0, typeorm_2.ILike)(`%${data.location}%`) },
                            { job_location_state: (0, typeorm_2.ILike)(`%${data.location}%`) },
                            { job_location_city: (0, typeorm_2.ILike)(`%${data.location}%`) },
                        ],
                    });
                    if (fallbackJob) {
                        c_id = fallbackJob.countryId;
                    }
                }
            }
            let job_posting_date = null;
            if (data.job_post_date) {
                job_posting_date = new Date(data.job_post_date);
                if (isNaN(job_posting_date.getTime())) {
                    throw new Error(`Invalid job_post_date: ${data.job_post_date}`);
                }
            }
            const newJob = this.jobsRepository.create({
                title: data.job_title ?? null,
                description: data.job_description ?? null,
                job_posting_link: data.job_posting_link ?? null,
                job_posting_date: job_posting_date,
                experience_level: experience_level,
                job_type: job_type,
                job_location_type: job_location_type,
                SR_specified_industry: data.SR_specified_industry ?? null,
                applicants: data.applicants ?? null,
                job_location_city: city,
                job_location_state: state,
                job_location_country: data.location ?? null,
                job_location_zip: null,
                currency: null,
                salary_min: null,
                salary_max: null,
                salary_period: null,
                industry: data.industry ?? null,
                skill_required: data.skills_required ?? null,
                skills: data.skills_required
                    ? data.skills_required.split(',').map((s) => s.trim())
                    : [],
                companyId: data.company_id,
                personId: null,
                sectorId: data.sector_id,
                countryId: c_id,
                scrapper_profile_name: data.scrapper_profile_name,
            });
            const savedJob = await this.jobsRepository.save(newJob);
            console.log('New job post created by scrapper');
            return {
                message: 'Job added successfully.',
                job: savedJob,
            };
        }
        catch (error) {
            console.error('Error adding job:', error.message);
            throw new common_1.InternalServerErrorException(`Failed to add job: ${error.message}`);
        }
    }
    async addPersonOnly(data) {
        console.log('Adding person by scrapper', data.country_id);
        try {
            if (!data.profile_url) {
                console.log('Profile URL is required.');
                throw new Error('Profile URL is required.');
            }
            if (!data.sector_id) {
                console.log('Sector ID is required.');
                throw new Error('Sector ID is required.');
            }
            let is_company_id = data.company_id ? Number(data.company_id) : null;
            if (!is_company_id) {
                is_company_id = null;
            }
            const existingPerson = await this.peopleRepository.findOne({
                where: {
                    profile_url: (0, typeorm_2.ILike)(`%${data.profile_url}%`),
                },
            });
            if (existingPerson) {
                console.log('Person already exists');
                return {
                    message: 'Person already exists',
                    person: existingPerson,
                    found: true,
                };
            }
            const newPerson = this.peopleRepository.create({
                full_name: data.full_name,
                first_name: data.first_name,
                last_name: data.last_name,
                headline: data.headline,
                profile_img: data.avator,
                profile_url: data.profile_url,
                current_title: data.current_title,
                SR_specied_industry: data.SR_specied_industry,
                industry: data.industry,
                summary: data.summary,
                location: data.address,
                is_hiring: data.is_hiring_person == 'true' ? true : false,
                companyId: data.company_id,
                sectorId: Number(data.sector_id),
                countryId: data.country_id ? Number(data.country_id) : null,
                profile_source: people_enums_1.PersonSource.JOB_POST_SCRAPPER,
                person_type: people_enums_1.PersonType.JOB_POST_LEAD,
                scrapper_profile_name: data.scrapper_profile_name,
            });
            const savedPerson = await this.peopleRepository.save(newPerson);
            console.log('New person added by scrapper');
            return {
                message: 'Person added successfully.',
                person: savedPerson,
            };
        }
        catch (error) {
            console.log('Error adding person: because scrapper sending country id = 0');
            throw new common_1.InternalServerErrorException(`Failed to add person: ${error.message}`);
        }
    }
    async getCompanyLinkWithRegion() {
        const getQuery = await this.companyScrapperControlRepository.find();
        const singleQuery = getQuery[0];
        let whereCondition = {};
        if (singleQuery.is_default == 'false' && singleQuery.countryId) {
            whereCondition.countryId = singleQuery.countryId;
        }
        if (singleQuery.is_default == 'false' && singleQuery.sectorId) {
            whereCondition.sectorId = singleQuery.sectorId;
        }
        if (singleQuery.is_default == 'false' && singleQuery.company_source) {
            whereCondition.company_source = singleQuery.company_source;
        }
        try {
            const companyLink = await this.companyRepository.findOne({
                select: [
                    'id',
                    'profile_url',
                    'profile_url_encoded',
                    'countryId',
                    'sectorId',
                    'userId',
                    'company_id',
                    'region',
                ],
                where: {
                    ...whereCondition,
                    scrapper_level: 2,
                    is_scrapped_fully: false,
                },
            });
            if (companyLink) {
                companyLink.is_scrapped_fully = true;
                await this.companyRepository.save(companyLink);
                console.log('Company link with region found and marked as scrapped fully.');
                return {
                    message: 'Company link with region found and marked as scrapped fully.',
                    company: companyLink,
                };
            }
            const fallbackCompany = await this.companyRepository.findOne({
                select: [
                    'id',
                    'profile_url',
                    'profile_url_encoded',
                    'countryId',
                    'sectorId',
                    'userId',
                    'company_id',
                ],
                where: whereCondition,
                order: {
                    id: 'ASC',
                },
            });
            if (fallbackCompany) {
                fallbackCompany.is_scrapped_fully = true;
                await this.companyRepository.save(fallbackCompany);
                console.log('Company link found and marked as scrapped fully.');
                return {
                    message: 'Company link found and marked as scrapped fully.',
                    company: fallbackCompany,
                };
            }
            console.log('No company link with region found.');
            return {
                message: 'No company link with region found.',
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(`Failed to get company link with region: ${error.message}`);
        }
    }
    async createScrapperDailyReport(data) {
        if (data.date) {
            if (typeof data.date === 'string' && data.date.includes('T')) {
                data.date = data.date.split('T')[0];
            }
        }
        const reportData = {
            date: data.date,
            total_clicks: data.totalClicks,
            matching_criteria: data.matchingCriteria,
            unmatching_criteria: data.unmatchingCriteria,
            direct_companies: data.directCompanies,
            snr_companies: data.snrCompanies,
            existing_companies: data.existingCompanies,
            existing_direct_companies: data.existingDirectCompanies,
            existing_snr_companies: data.existingSnrCompanies,
            new_companies: data.newCompanies,
            new_direct_companies: data.newDirectCompanies,
            new_snr_companies: data.newSnrCompanies,
            region: data.region,
            login_errors: data.loginErrors,
            job_click_errors: data.jobClickErrors,
            no_member_error: data.NoMembersErrors,
            scrapper_profile_name: data.scrapper_profile_name,
        };
        try {
            const existingReport = await this.scrapperStatsRepository.findOne({
                where: {
                    date: data.date,
                    region: data.region,
                },
            });
            if (existingReport) {
                await this.scrapperStatsRepository.update({
                    date: data.date,
                    region: data.region,
                }, reportData);
                return {
                    message: `Report for date ${data.date} and region ${data.region} updated successfully`,
                };
            }
            else {
                await this.scrapperStatsRepository.save(reportData);
                return {
                    message: `New report for date ${data.date} and region ${data.region} created successfully`,
                };
            }
        }
        catch (error) {
            console.error('Error creating scrapper daily report:', error);
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    async getDailyScrapperReport(queryParams) {
        const { date, region, page, pageSize } = queryParams;
        const limit = parseInt(pageSize);
        const skip = parseInt(page) * limit;
        if (!page) {
            throw new common_1.BadRequestException('Page number is required');
        }
        let whereCondition = {};
        if (date) {
            whereCondition.date = date;
        }
        if (region) {
            whereCondition.region = (0, typeorm_2.ILike)(`%${region}%`);
        }
        try {
            const report = await this.scrapperStatsRepository.findAndCount({
                where: whereCondition,
                order: {
                    date: 'DESC',
                },
                take: limit,
                skip: skip,
            });
            const reportData = {
                data: report[0],
                pagination: {
                    page: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: report[1],
                },
            };
            return reportData;
        }
        catch (error) {
            console.error('Error creating scrapper daily report:', error);
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
};
exports.ScrapperService = ScrapperService;
exports.ScrapperService = ScrapperService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __param(1, (0, typeorm_1.InjectRepository)(country_entity_1.Country)),
    __param(2, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(3, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(4, (0, typeorm_1.InjectRepository)(people_assignment_entity_1.PeopleAssignment)),
    __param(5, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __param(6, (0, typeorm_1.InjectRepository)(jobs_entity_1.Jobs)),
    __param(7, (0, typeorm_1.InjectRepository)(scrapperStats_entity_1.ScrapperStats)),
    __param(8, (0, typeorm_1.InjectRepository)(company_scrapper_control_entity_1.CompanyScrapperControl)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_3.DataSource])
], ScrapperService);
//# sourceMappingURL=scrapper.service.js.map