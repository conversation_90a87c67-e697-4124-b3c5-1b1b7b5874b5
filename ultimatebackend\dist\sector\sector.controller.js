"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SectorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const sector_service_1 = require("./sector.service");
let SectorController = class SectorController {
    constructor(sectorService) {
        this.sectorService = sectorService;
    }
    async createSector(name) {
        return this.sectorService.createSector(name);
    }
    async updateSector(id, name) {
        return this.sectorService.updateSector(id, name);
    }
    async deleteSector(id) {
        return this.sectorService.deleteSector(id);
    }
};
exports.SectorController = SectorController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create Sector' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SectorController.prototype, "createSector", null);
__decorate([
    (0, common_1.Put)('update'),
    (0, swagger_1.ApiOperation)({ summary: 'Update Sector' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], SectorController.prototype, "updateSector", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete Sector' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SectorController.prototype, "deleteSector", null);
exports.SectorController = SectorController = __decorate([
    (0, common_1.Controller)('sector'),
    (0, swagger_1.ApiTags)('sector'),
    __metadata("design:paramtypes", [sector_service_1.SectorService])
], SectorController);
//# sourceMappingURL=sector.controller.js.map