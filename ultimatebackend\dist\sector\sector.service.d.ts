import { Sector } from './sector.entity';
import { Repository } from 'typeorm';
export declare class SectorService {
    private sectorRepository;
    constructor(sectorRepository: Repository<Sector>);
    createSector(name: string): Promise<void>;
    updateSector(id: number, name: string): Promise<Sector>;
    deleteSector(id: number): Promise<void>;
    findAll(): Promise<Sector[]>;
    findOne(id: number): Promise<Sector>;
    findByName(name: string): Promise<Sector>;
    findByNameAndIdNot(name: string, id: number): Promise<Sector>;
}
