"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SectorService = void 0;
const common_1 = require("@nestjs/common");
const sector_entity_1 = require("./sector.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let SectorService = class SectorService {
    constructor(sectorRepository) {
        this.sectorRepository = sectorRepository;
    }
    async createSector(name) {
        await this.sectorRepository.insert({
            name,
        });
    }
    async updateSector(id, name) {
        const sector = await this.sectorRepository.findOne({ where: { id } });
        await this.sectorRepository.update({ id }, {
            name,
        });
        return sector;
    }
    async deleteSector(id) {
        await this.sectorRepository.delete({ id });
    }
    async findAll() {
        return this.sectorRepository.find();
    }
    async findOne(id) {
        return this.sectorRepository.findOne({ where: { id } });
    }
    async findByName(name) {
        return this.sectorRepository.findOne({ where: { name } });
    }
    async findByNameAndIdNot(name, id) {
        return this.sectorRepository.findOne({ where: { name, id: id } });
    }
};
exports.SectorService = SectorService;
exports.SectorService = SectorService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sector_entity_1.Sector)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SectorService);
//# sourceMappingURL=sector.service.js.map