"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceStepsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SequenceStepsDto {
}
exports.SequenceStepsDto = SequenceStepsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the sequence step',
        example: 'Initial Outreach',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SequenceStepsDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Order of the sequence step',
        example: 0,
        default: 0,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SequenceStepsDto.prototype, "order", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the sequence step',
        enum: ['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'OTHER'],
        default: 'OUTREACH',
    }),
    (0, class_validator_1.IsEnum)(['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'OTHER']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SequenceStepsDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Medium of the sequence step',
        enum: ['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'],
        default: 'EMAIL',
    }),
    (0, class_validator_1.IsEnum)(['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER']),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SequenceStepsDto.prototype, "medium", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the associated email template',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SequenceStepsDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the associated role sequence',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SequenceStepsDto.prototype, "roleSequenceId", void 0);
//# sourceMappingURL=sequenceSteps.dto.js.map