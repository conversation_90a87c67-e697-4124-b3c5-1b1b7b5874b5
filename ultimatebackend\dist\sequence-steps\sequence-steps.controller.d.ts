import { SequenceStepsService } from './sequence-steps.service';
import { SequenceStepsDto } from './dto/sequenceSteps.dto';
import { UpdateSequenceStepDto } from './dto/updateSequenceStep.dto';
export declare class SequenceStepsController {
    private readonly sequenceStepsService;
    constructor(sequenceStepsService: SequenceStepsService);
    createSequenceStep(sequenceStepsDto: SequenceStepsDto): Promise<any>;
    getAllSequenceSteps(): Promise<any[]>;
    getSequenceStepById(id: number): Promise<any>;
    updateSequenceStep(id: number, sequenceStepsDto: UpdateSequenceStepDto): Promise<any>;
    deleteSequenceStep(id: number): Promise<void>;
}
