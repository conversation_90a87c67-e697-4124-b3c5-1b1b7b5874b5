"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceStepsController = void 0;
const common_1 = require("@nestjs/common");
const sequence_steps_service_1 = require("./sequence-steps.service");
const swagger_1 = require("@nestjs/swagger");
const sequenceSteps_dto_1 = require("./dto/sequenceSteps.dto");
const updateSequenceStep_dto_1 = require("./dto/updateSequenceStep.dto");
let SequenceStepsController = class SequenceStepsController {
    constructor(sequenceStepsService) {
        this.sequenceStepsService = sequenceStepsService;
    }
    async createSequenceStep(sequenceStepsDto) {
        return this.sequenceStepsService.createSequenceSteps(sequenceStepsDto);
    }
    async getAllSequenceSteps() {
        return this.sequenceStepsService.getSequenceSteps();
    }
    async getSequenceStepById(id) {
        return this.sequenceStepsService.getSequenceStepsById(id);
    }
    async updateSequenceStep(id, sequenceStepsDto) {
        return this.sequenceStepsService.updateSequenceSteps(id, sequenceStepsDto);
    }
    async deleteSequenceStep(id) {
        return this.sequenceStepsService.deleteSequenceSteps(id);
    }
};
exports.SequenceStepsController = SequenceStepsController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Create a new sequence step' }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [sequenceSteps_dto_1.SequenceStepsDto]),
    __metadata("design:returntype", Promise)
], SequenceStepsController.prototype, "createSequenceStep", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get all sequence steps' }),
    (0, common_1.Get)('get-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SequenceStepsController.prototype, "getAllSequenceSteps", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get a sequence step by ID' }),
    (0, common_1.Get)(':id'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceStepsController.prototype, "getSequenceStepById", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Update a sequence step by ID' }),
    (0, common_1.Put)(':id'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, updateSequenceStep_dto_1.UpdateSequenceStepDto]),
    __metadata("design:returntype", Promise)
], SequenceStepsController.prototype, "updateSequenceStep", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Delete a sequence step by ID' }),
    (0, common_1.Delete)(':id'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceStepsController.prototype, "deleteSequenceStep", null);
exports.SequenceStepsController = SequenceStepsController = __decorate([
    (0, swagger_1.ApiTags)('SequenceSteps'),
    (0, common_1.Controller)('sequence-steps'),
    __metadata("design:paramtypes", [sequence_steps_service_1.SequenceStepsService])
], SequenceStepsController);
//# sourceMappingURL=sequence-steps.controller.js.map