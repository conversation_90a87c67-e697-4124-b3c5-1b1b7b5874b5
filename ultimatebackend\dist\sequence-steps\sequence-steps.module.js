"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceStepsModule = void 0;
const common_1 = require("@nestjs/common");
const sequence_steps_service_1 = require("./sequence-steps.service");
const sequence_steps_controller_1 = require("./sequence-steps.controller");
const typeorm_1 = require("@nestjs/typeorm");
const sequence_steps_entity_1 = require("./sequence_steps.entity");
let SequenceStepsModule = class SequenceStepsModule {
};
exports.SequenceStepsModule = SequenceStepsModule;
exports.SequenceStepsModule = SequenceStepsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sequence_steps_entity_1.SequenceSteps])],
        providers: [sequence_steps_service_1.SequenceStepsService],
        controllers: [sequence_steps_controller_1.SequenceStepsController],
    })
], SequenceStepsModule);
//# sourceMappingURL=sequence-steps.module.js.map