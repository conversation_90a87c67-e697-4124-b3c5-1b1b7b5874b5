import { SequenceSteps } from './sequence_steps.entity';
import { Repository } from 'typeorm';
import { SequenceStepsDto } from './dto/sequenceSteps.dto';
import { UpdateSequenceStepDto } from './dto/updateSequenceStep.dto';
export declare class SequenceStepsService {
    private readonly sequenceStepsRepository;
    constructor(sequenceStepsRepository: Repository<SequenceSteps>);
    createSequenceSteps(sequenceSteps: SequenceStepsDto): Promise<SequenceSteps>;
    getSequenceSteps(): Promise<SequenceSteps[]>;
    getSequenceStepsById(id: number): Promise<SequenceSteps>;
    updateSequenceSteps(id: number, sequenceSteps: UpdateSequenceStepDto): Promise<SequenceSteps>;
    deleteSequenceSteps(id: number): Promise<void>;
}
