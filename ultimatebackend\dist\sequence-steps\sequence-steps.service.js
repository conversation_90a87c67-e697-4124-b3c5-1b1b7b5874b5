"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceStepsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const sequence_steps_entity_1 = require("./sequence_steps.entity");
const typeorm_2 = require("typeorm");
let SequenceStepsService = class SequenceStepsService {
    constructor(sequenceStepsRepository) {
        this.sequenceStepsRepository = sequenceStepsRepository;
    }
    async createSequenceSteps(sequenceSteps) {
        try {
            const newSequenceSteps = this.sequenceStepsRepository.create(sequenceSteps);
            return await this.sequenceStepsRepository.save(newSequenceSteps);
        }
        catch (error) {
            throw new Error('Error creating sequence steps: ' + error.message);
        }
    }
    async getSequenceSteps() {
        try {
            return await this.sequenceStepsRepository.find();
        }
        catch (error) {
            throw new Error('Error fetching sequence steps: ' + error.message);
        }
    }
    async getSequenceStepsById(id) {
        try {
            const sequenceSteps = await this.sequenceStepsRepository.findOne({
                where: { id },
            });
            if (!sequenceSteps) {
                throw new Error('Sequence steps not found');
            }
            return sequenceSteps;
        }
        catch (error) {
            throw new Error('Error fetching sequence steps: ' + error.message);
        }
    }
    async updateSequenceSteps(id, sequenceSteps) {
        try {
            await this.sequenceStepsRepository.update(id, sequenceSteps);
            return await this.getSequenceStepsById(id);
        }
        catch (error) {
            throw new Error('Error updating sequence steps: ' + error.message);
        }
    }
    async deleteSequenceSteps(id) {
        try {
            const result = await this.sequenceStepsRepository.delete(id);
            if (result.affected === 0) {
                throw new Error('Sequence steps not found');
            }
        }
        catch (error) {
            throw new Error('Error deleting sequence steps: ' + error.message);
        }
    }
};
exports.SequenceStepsService = SequenceStepsService;
exports.SequenceStepsService = SequenceStepsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sequence_steps_entity_1.SequenceSteps)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SequenceStepsService);
//# sourceMappingURL=sequence-steps.service.js.map