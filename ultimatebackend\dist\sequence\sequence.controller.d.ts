import { SequenceService } from './sequence.service';
import { RoleSequenceDto, UpdateRoleSequenceDto } from './dto/sequence..dto';
import { RoleSequence } from './sequence.entity';
export declare class SequenceController {
    private readonly sequenceService;
    constructor(sequenceService: SequenceService);
    createSequence(sequenceDto: RoleSequenceDto): Promise<RoleSequence>;
    getAllSequences(): Promise<RoleSequence[]>;
    getSequenceById(id: number): Promise<RoleSequence>;
    updateSequence(id: number, updateSequenceDto: UpdateRoleSequenceDto): Promise<RoleSequence>;
    deleteSequence(id: number): Promise<void>;
    startSequence(sequenceId: number, body: {
        candidateIds: number[];
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    getSequenceWithSteps(sequenceId: number): Promise<RoleSequence>;
    startSequenceWithTestCandidates(sequenceId: number): Promise<{
        success: boolean;
        message: string;
    }>;
    startSequenceWithRoleCandidates(sequenceId: number, body: {
        roleId: number;
        limit?: number;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    getSequenceStats(sequenceId: number): Promise<any>;
    debugSequence(sequenceId: number): Promise<any>;
    checkDatabaseHealth(): Promise<any>;
}
