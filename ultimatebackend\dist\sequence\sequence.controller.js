"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const sequence_service_1 = require("./sequence.service");
const sequence__dto_1 = require("./dto/sequence..dto");
let SequenceController = class SequenceController {
    constructor(sequenceService) {
        this.sequenceService = sequenceService;
    }
    async createSequence(sequenceDto) {
        return this.sequenceService.createRoleSequence(sequenceDto);
    }
    async getAllSequences() {
        return this.sequenceService.getRoleSequence();
    }
    async getSequenceById(id) {
        return this.sequenceService.getRoleSequenceById(id);
    }
    async updateSequence(id, updateSequenceDto) {
        return this.sequenceService.updateRoleSequence(id, updateSequenceDto);
    }
    async deleteSequence(id) {
        return this.sequenceService.deleteRoleSequence(id);
    }
    async startSequence(sequenceId, body) {
        await this.sequenceService.startSequence(sequenceId, body.candidateIds);
        return {
            success: true,
            message: `Sequence ${sequenceId} started for ${body.candidateIds.length} candidates`,
        };
    }
    async getSequenceWithSteps(sequenceId) {
        return this.sequenceService.getSequenceWithSteps(sequenceId);
    }
    async startSequenceWithTestCandidates(sequenceId) {
        await this.sequenceService.startSequenceWithTestCandidates(sequenceId);
        return {
            success: true,
            message: `Sequence ${sequenceId} started with test candidates`,
        };
    }
    async startSequenceWithRoleCandidates(sequenceId, body) {
        await this.sequenceService.startSequenceWithRoleCandidates(sequenceId, body.roleId, body.limit || 10);
        return {
            success: true,
            message: `Sequence ${sequenceId} started with role candidates for role ${body.roleId}`,
        };
    }
    async getSequenceStats(sequenceId) {
        return this.sequenceService.getSequenceStats(sequenceId);
    }
    async debugSequence(sequenceId) {
        return this.sequenceService.debugSequenceData(sequenceId);
    }
    async checkDatabaseHealth() {
        return this.sequenceService.checkDatabaseHealth();
    }
};
exports.SequenceController = SequenceController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Create a new sequence' }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [sequence__dto_1.RoleSequenceDto]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "createSequence", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get all sequences' }),
    (0, common_1.Get)('get-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "getAllSequences", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get a sequence by ID' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "getSequenceById", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Update a sequence by ID' }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, sequence__dto_1.UpdateRoleSequenceDto]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "updateSequence", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Delete a sequence by ID' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "deleteSequence", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Start a sequence for candidates' }),
    (0, common_1.Post)(':id/start'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "startSequence", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get sequence with steps' }),
    (0, common_1.Get)(':id/with-steps'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "getSequenceWithSteps", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Start sequence with test candidates' }),
    (0, common_1.Post)(':id/start-with-test-candidates'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "startSequenceWithTestCandidates", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Start sequence with role candidates' }),
    (0, common_1.Post)(':id/start-with-role-candidates'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "startSequenceWithRoleCandidates", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get sequence execution statistics' }),
    (0, common_1.Get)(':id/stats'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "getSequenceStats", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Debug sequence and candidate data' }),
    (0, common_1.Get)(':id/debug'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "debugSequence", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Check database health for sequences' }),
    (0, common_1.Get)('debug/health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SequenceController.prototype, "checkDatabaseHealth", null);
exports.SequenceController = SequenceController = __decorate([
    (0, common_1.Controller)('sequence'),
    (0, swagger_1.ApiTags)('Sequence'),
    __metadata("design:paramtypes", [sequence_service_1.SequenceService])
], SequenceController);
//# sourceMappingURL=sequence.controller.js.map