"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceModule = void 0;
const common_1 = require("@nestjs/common");
const sequence_service_1 = require("./sequence.service");
const sequence_controller_1 = require("./sequence.controller");
const typeorm_1 = require("@nestjs/typeorm");
const sequence_entity_1 = require("./sequence.entity");
const sequence_steps_entity_1 = require("../sequence-steps/sequence_steps.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
const phone_entity_1 = require("../phone/phone.entity");
const candidate_sequence_status_module_1 = require("../candidate-sequence-status/candidate-sequence-status.module");
const queue_module_1 = require("../queue/queue.module");
let SequenceModule = class SequenceModule {
};
exports.SequenceModule = SequenceModule;
exports.SequenceModule = SequenceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                sequence_entity_1.RoleSequence,
                sequence_steps_entity_1.SequenceSteps,
                role_candidates_entity_1.RoleCandidate,
                people_entity_1.People,
                emails_entity_1.PersonEmail,
                phone_entity_1.PersonPhone,
            ]),
            candidate_sequence_status_module_1.CandidateSequenceStatusModule,
            queue_module_1.QueueModule,
        ],
        providers: [sequence_service_1.SequenceService],
        controllers: [sequence_controller_1.SequenceController],
        exports: [sequence_service_1.SequenceService],
    })
], SequenceModule);
//# sourceMappingURL=sequence.module.js.map