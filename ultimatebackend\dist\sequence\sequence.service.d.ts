import { RoleSequence } from './sequence.entity';
import { Repository } from 'typeorm';
import { RoleSequenceDto, UpdateRoleSequenceDto } from './dto/sequence..dto';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { QueueService } from 'src/queue/queue.service';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
export declare class SequenceService {
    private readonly roleSequenceRepository;
    private readonly stepRepository;
    private readonly candidateRepository;
    private readonly peopleRepository;
    private readonly personEmailRepository;
    private readonly personPhoneRepository;
    private readonly candidateSequenceStatusService;
    private readonly queueService;
    private readonly logger;
    constructor(roleSequenceRepository: Repository<RoleSequence>, stepRepository: Repository<SequenceSteps>, candidateRepository: Repository<RoleCandidate>, peopleRepository: Repository<People>, personEmailRepository: Repository<PersonEmail>, personPhoneRepository: Repository<PersonPhone>, candidateSequenceStatusService: CandidateSequenceStatusService, queueService: QueueService);
    createRoleSequence(roleSequence: RoleSequenceDto): Promise<RoleSequence>;
    getRoleSequence(): Promise<RoleSequence[]>;
    getRoleSequenceById(id: number): Promise<RoleSequence>;
    updateRoleSequence(id: number, updateRoleSequenceDto: UpdateRoleSequenceDto): Promise<RoleSequence>;
    deleteRoleSequence(id: number): Promise<void>;
    startSequenceWithTestCandidates(sequenceId: number): Promise<void>;
    private filterNewCandidates;
    startSequenceWithRoleCandidates(sequenceId: number, roleId: number, limit?: number): Promise<void>;
    private autoStartSequenceWithTestCandidates;
    startSequence(sequenceId: number, candidateIds: number[]): Promise<void>;
    private startFirstSteps;
    private executeSteps;
    private executeStep;
    private addContactInformation;
    processNextSteps(candidateId: number, completedStepId: number): Promise<void>;
    getSequenceWithSteps(sequenceId: number): Promise<RoleSequence>;
    getSequenceStats(sequenceId: number): Promise<any>;
    debugSequenceData(sequenceId: number): Promise<any>;
    checkDatabaseHealth(): Promise<any>;
    private generateHealthRecommendations;
}
