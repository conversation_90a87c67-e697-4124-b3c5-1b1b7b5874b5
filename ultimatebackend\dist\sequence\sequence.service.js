"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SequenceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SequenceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const sequence_entity_1 = require("./sequence.entity");
const typeorm_2 = require("typeorm");
const candidate_sequence_status_service_1 = require("../candidate-sequence-status/candidate-sequence-status.service");
const queue_service_1 = require("../queue/queue.service");
const sequence_steps_entity_1 = require("../sequence-steps/sequence_steps.entity");
const role_candidates_entity_1 = require("../role_candidates/role_candidates.entity");
const people_entity_1 = require("../people/people.entity");
const emails_entity_1 = require("../emails/emails.entity");
const phone_entity_1 = require("../phone/phone.entity");
const candidate_sequence_status_entity_1 = require("../candidate-sequence-status/candidate-sequence-status.entity");
let SequenceService = SequenceService_1 = class SequenceService {
    constructor(roleSequenceRepository, stepRepository, candidateRepository, peopleRepository, personEmailRepository, personPhoneRepository, candidateSequenceStatusService, queueService) {
        this.roleSequenceRepository = roleSequenceRepository;
        this.stepRepository = stepRepository;
        this.candidateRepository = candidateRepository;
        this.peopleRepository = peopleRepository;
        this.personEmailRepository = personEmailRepository;
        this.personPhoneRepository = personPhoneRepository;
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.queueService = queueService;
        this.logger = new common_1.Logger(SequenceService_1.name);
    }
    async createRoleSequence(roleSequence) {
        try {
            const newRoleSequence = this.roleSequenceRepository.create(roleSequence);
            const savedSequence = await this.roleSequenceRepository.save(newRoleSequence);
            if (savedSequence.status === 'ACTIVE') {
                this.logger.log(`Auto-starting sequence ${savedSequence.id} with test candidates`);
                await this.autoStartSequenceWithTestCandidates(savedSequence.id);
            }
            return savedSequence;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error creating role sequence', error.message);
        }
    }
    async getRoleSequence() {
        try {
            return await this.roleSequenceRepository.find();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role sequences', error.message);
        }
    }
    async getRoleSequenceById(id) {
        try {
            const roleSequence = await this.roleSequenceRepository.findOne({
                where: { id },
            });
            if (!roleSequence) {
                throw new common_1.NotFoundException('Role sequence not found');
            }
            return roleSequence;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error fetching role sequence', error.message);
        }
    }
    async updateRoleSequence(id, updateRoleSequenceDto) {
        try {
            const roleSequence = await this.getRoleSequenceById(id);
            if (!roleSequence) {
                throw new common_1.NotFoundException('Role sequence not found');
            }
            Object.assign(roleSequence, updateRoleSequenceDto);
            return await this.roleSequenceRepository.save(roleSequence);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error updating role sequence', error.message);
        }
    }
    async deleteRoleSequence(id) {
        try {
            const roleSequence = await this.getRoleSequenceById(id);
            if (!roleSequence) {
                throw new common_1.NotFoundException('Role sequence not found');
            }
            await this.roleSequenceRepository.remove(roleSequence);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Error deleting role sequence', error.message);
        }
    }
    async startSequenceWithTestCandidates(sequenceId) {
        this.logger.log(`Starting sequence ${sequenceId} with test candidates (public method)`);
        const totalCandidates = await this.candidateRepository.count();
        if (totalCandidates === 0) {
            throw new Error('No role candidates found in the database. Please add some candidates first.');
        }
        await this.autoStartSequenceWithTestCandidates(sequenceId);
    }
    async filterNewCandidates(candidateIds, sequenceId) {
        const existingStatuses = await this.candidateSequenceStatusService.getCandidatesInSequence(sequenceId);
        const existingCandidateIds = new Set(existingStatuses.map((status) => status.candidateId));
        const newCandidateIds = candidateIds.filter(candidateId => !existingCandidateIds.has(candidateId));
        this.logger.log(`Filtered candidates: ${candidateIds.length} total, ${existingCandidateIds.size} existing, ${newCandidateIds.length} new`);
        return newCandidateIds;
    }
    async startSequenceWithRoleCandidates(sequenceId, roleId, limit = 10) {
        try {
            this.logger.log(`Starting sequence ${sequenceId} with role candidates for role ${roleId}`);
            const roleCandidates = await this.candidateRepository.find({
                where: {
                    roleId: roleId,
                },
                relations: ['candidate', 'candidate.emails', 'candidate.phones', 'role'],
                take: limit,
            });
            if (roleCandidates.length === 0) {
                throw new Error(`No role candidates found for role ${roleId}`);
            }
            const candidateIds = roleCandidates.map(c => c.id);
            this.logger.log(`Starting sequence ${sequenceId} with ${candidateIds.length} role candidates for role ${roleId}`);
            await this.startSequence(sequenceId, candidateIds);
            this.logger.log(`Sequence ${sequenceId} started successfully with ${candidateIds.length} role candidates`);
        }
        catch (error) {
            this.logger.error(`Failed to start sequence ${sequenceId} with role candidates: ${error.message}`);
            throw error;
        }
    }
    async autoStartSequenceWithTestCandidates(sequenceId) {
        try {
            this.logger.log(`Auto-starting sequence ${sequenceId} with test candidates`);
            const sequence = await this.roleSequenceRepository.findOne({
                where: { id: sequenceId },
                relations: ['sequenceSteps'],
            });
            if (!sequence) {
                this.logger.error(`Sequence ${sequenceId} not found`);
                return;
            }
            if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
                this.logger.warn(`Sequence ${sequenceId} has no steps, skipping auto-start`);
                return;
            }
            const testCandidates = await this.candidateRepository.find({
                take: 5,
                relations: ['candidate', 'candidate.emails', 'candidate.phones', 'role'],
            });
            if (testCandidates.length === 0) {
                this.logger.warn(`No role candidates found to auto-start sequence ${sequenceId}. This is normal if no candidates exist yet.`);
                return;
            }
            const validCandidates = testCandidates.filter(c => c.candidate && c.candidate.id);
            if (validCandidates.length === 0) {
                this.logger.warn(`No valid role candidates with proper candidate records found for sequence ${sequenceId}`);
                return;
            }
            const candidateIds = validCandidates.map(c => c.id);
            this.logger.log(`Auto-starting sequence ${sequenceId} with ${candidateIds.length} role candidates: ${candidateIds.join(', ')}`);
            await this.startSequence(sequenceId, candidateIds);
            this.logger.log(`Sequence ${sequenceId} auto-started successfully with ${candidateIds.length} candidates`);
        }
        catch (error) {
            this.logger.error(`Failed to auto-start sequence ${sequenceId}: ${error.message}`, error.stack);
        }
    }
    async startSequence(sequenceId, candidateIds) {
        this.logger.log(`Starting sequence ${sequenceId} for ${candidateIds.length} candidates: [${candidateIds.join(', ')}]`);
        try {
            if (!sequenceId) {
                throw new Error('Sequence ID is required');
            }
            if (!candidateIds || candidateIds.length === 0) {
                throw new Error('At least one candidate ID is required');
            }
            const sequence = await this.roleSequenceRepository.findOne({
                where: { id: sequenceId },
                relations: ['sequenceSteps'],
            });
            if (!sequence) {
                throw new common_1.NotFoundException(`Sequence with ID ${sequenceId} not found`);
            }
            if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
                throw new Error(`Sequence ${sequenceId} has no steps defined`);
            }
            this.logger.log(`Found sequence "${sequence.name}" with ${sequence.sequenceSteps.length} steps`);
            for (const candidateId of candidateIds) {
                const candidate = await this.candidateRepository.findOne({
                    where: { id: candidateId },
                    relations: ['candidate'],
                });
                if (!candidate) {
                    throw new Error(`Candidate with ID ${candidateId} not found`);
                }
                this.logger.log(`Validated candidate ${candidateId}: ${candidate.candidate?.first_name} ${candidate.candidate?.last_name}`);
            }
            const newCandidateIds = await this.filterNewCandidates(candidateIds, sequenceId);
            if (newCandidateIds.length === 0) {
                this.logger.warn(`All candidates are already in sequence ${sequenceId}`);
                return;
            }
            this.logger.log(`Adding ${newCandidateIds.length} new candidates to sequence ${sequenceId}: [${newCandidateIds.join(', ')}]`);
            await this.candidateSequenceStatusService.initializeCandidateSequence(newCandidateIds, sequenceId);
            await this.startFirstSteps(candidateIds, sequenceId);
            this.logger.log(`Sequence ${sequenceId} started successfully for ${candidateIds.length} candidates`);
        }
        catch (error) {
            this.logger.error(`Failed to start sequence ${sequenceId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Error starting sequence', error.message);
        }
    }
    async startFirstSteps(candidateIds, sequenceId) {
        for (const candidateId of candidateIds) {
            const firstSteps = await this.candidateSequenceStatusService.getPendingSteps(candidateId, sequenceId, 0);
            if (firstSteps.length === 0) {
                const allSteps = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, sequenceId);
                const minOrder = Math.min(...allSteps.map(s => s.stepOrder));
                const minOrderSteps = await this.candidateSequenceStatusService.getPendingSteps(candidateId, sequenceId, minOrder);
                await this.executeSteps(candidateId, minOrderSteps);
            }
            else {
                await this.executeSteps(candidateId, firstSteps);
            }
        }
    }
    async executeSteps(candidateId, steps) {
        for (const step of steps) {
            await this.executeStep(candidateId, step);
        }
    }
    async executeStep(candidateId, candidateSequenceStatus) {
        try {
            console.log(`🚀 SEQUENCE-SERVICE: executeStep called for candidate ${candidateId}, status ID ${candidateSequenceStatus.id}`);
            console.log(`🚀 SEQUENCE-SERVICE: candidateSequenceStatus details:`, {
                id: candidateSequenceStatus.id,
                candidateId: candidateSequenceStatus.candidateId,
                sequenceId: candidateSequenceStatus.sequenceId,
                stepId: candidateSequenceStatus.stepId,
                stepOrder: candidateSequenceStatus.stepOrder,
                status: candidateSequenceStatus.status
            });
            console.log(`🚀 SEQUENCE-SERVICE: Fetching candidate ${candidateId} with contact information...`);
            const candidate = await this.candidateRepository.findOne({
                where: { id: candidateId },
                relations: ['candidate', 'candidate.emails', 'candidate.phones'],
            });
            if (!candidate) {
                console.error(`🚀 SEQUENCE-SERVICE: ❌ Candidate ${candidateId} not found`);
                throw new Error(`Candidate ${candidateId} not found`);
            }
            console.log(`🚀 SEQUENCE-SERVICE: ✅ Found candidate:`, {
                id: candidate.id,
                candidateId: candidate.candidateId,
                roleId: candidate.roleId,
                candidateName: candidate.candidate ? `${candidate.candidate.first_name} ${candidate.candidate.last_name}` : 'No candidate record',
                emailCount: candidate.candidate?.emails?.length || 0,
                phoneCount: candidate.candidate?.phones?.length || 0
            });
            console.log(`🚀 SEQUENCE-SERVICE: Fetching step ${candidateSequenceStatus.stepId} details...`);
            const step = await this.stepRepository.findOne({
                where: { id: candidateSequenceStatus.stepId },
                relations: ['emailTemplate'],
            });
            if (!step) {
                console.error(`🚀 SEQUENCE-SERVICE: ❌ Step ${candidateSequenceStatus.stepId} not found`);
                throw new Error(`Step ${candidateSequenceStatus.stepId} not found`);
            }
            console.log(`🚀 SEQUENCE-SERVICE: ✅ Found step:`, {
                id: step.id,
                name: step.name,
                medium: step.medium,
                type: step.type,
                order: step.order,
                templateId: step.templateId,
                hasEmailTemplate: !!step.emailTemplate
            });
            console.log(`🚀 SEQUENCE-SERVICE: Verifying candidateSequenceStatus ${candidateSequenceStatus.id} exists...`);
            const verifyStatus = await this.candidateSequenceStatusService.findById(candidateSequenceStatus.id);
            if (!verifyStatus) {
                console.error(`🚀 SEQUENCE-SERVICE: ❌ CandidateSequenceStatus ${candidateSequenceStatus.id} not found in database!`);
                throw new Error(`CandidateSequenceStatus ${candidateSequenceStatus.id} not found in database`);
            }
            console.log(`🚀 SEQUENCE-SERVICE: ✅ CandidateSequenceStatus ${candidateSequenceStatus.id} verified in database`);
            const jobData = {
                candidateSequenceStatusId: candidateSequenceStatus.id,
                candidateId,
                stepId: step.id,
                sequenceId: candidateSequenceStatus.sequenceId,
                medium: step.medium,
                templateId: step.templateId,
                metadata: {
                    stepName: step.name,
                    stepType: step.type,
                    candidateName: `${candidate.candidate?.first_name} ${candidate.candidate?.last_name}`,
                },
            };
            console.log(`🚀 SEQUENCE-SERVICE: Prepared initial job data:`, jobData);
            console.log(`🚀 SEQUENCE-SERVICE: Adding contact information for medium: ${step.medium}`);
            await this.addContactInformation(jobData, candidate.candidate, step.medium);
            console.log(`🚀 SEQUENCE-SERVICE: Final job data with contact info:`, jobData);
            console.log(`🚀 SEQUENCE-SERVICE: Adding job to ${step.medium} queue...`);
            await this.queueService.addJobToQueue(step.medium, jobData);
            console.log(`🚀 SEQUENCE-SERVICE: ✅ Step ${step.id} (${step.name}) queued for candidate ${candidateId} via ${step.medium}`);
            this.logger.log(`Step ${step.id} queued for candidate ${candidateId} via ${step.medium}`);
        }
        catch (error) {
            console.error(`🚀 SEQUENCE-SERVICE: ❌ Failed to execute step for candidate ${candidateId}:`, error.message);
            console.error(`🚀 SEQUENCE-SERVICE: ❌ Error stack:`, error.stack);
            this.logger.error(`Failed to execute step for candidate ${candidateId}: ${error.message}`);
            throw error;
        }
    }
    async addContactInformation(jobData, person, medium) {
        console.log(`🚀 SEQUENCE-SERVICE: addContactInformation called for medium: ${medium}`);
        console.log(`🚀 SEQUENCE-SERVICE: Person data:`, {
            id: person?.id,
            firstName: person?.first_name,
            lastName: person?.last_name,
            emailCount: person?.emails?.length || 0,
            phoneCount: person?.phones?.length || 0,
            profileUrl: person?.profile_url
        });
        if (!person) {
            console.error(`🚀 SEQUENCE-SERVICE: ❌ No person data provided for contact information`);
            return;
        }
        switch (medium.toUpperCase()) {
            case 'EMAIL':
                console.log(`🚀 SEQUENCE-SERVICE: Processing EMAIL medium...`);
                console.log(`🚀 SEQUENCE-SERVICE: Available emails:`, person.emails?.map(e => ({
                    id: e.id,
                    email: e.email,
                    type: e.email_type
                })));
                const primaryEmail = person.emails?.find(e => e.email_type === 'BUSINESS') || person.emails?.[0];
                if (primaryEmail) {
                    jobData.recipientEmail = primaryEmail.email;
                    console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient email: ${primaryEmail.email} (type: ${primaryEmail.email_type})`);
                }
                else {
                    console.error(`🚀 SEQUENCE-SERVICE: ❌ No email found for person ${person.id}`);
                }
                break;
            case 'SMS':
            case 'WHATSAPP':
            case 'CALL':
                console.log(`🚀 SEQUENCE-SERVICE: Processing ${medium} medium...`);
                console.log(`🚀 SEQUENCE-SERVICE: Available phones:`, person.phones?.map(p => ({
                    id: p.id,
                    phone: p.phone_number,
                    type: p.phone_type
                })));
                const primaryPhone = person.phones?.find(p => p.phone_type === 'BUSINESS') || person.phones?.[0];
                if (primaryPhone) {
                    jobData.recipientPhone = primaryPhone.phone_number;
                    console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient phone: ${primaryPhone.phone_number} (type: ${primaryPhone.phone_type})`);
                }
                else {
                    console.error(`🚀 SEQUENCE-SERVICE: ❌ No phone found for person ${person.id}`);
                }
                break;
            case 'LINKEDIN':
                console.log(`🚀 SEQUENCE-SERVICE: Processing LINKEDIN medium...`);
                if (person.profile_url) {
                    jobData.recipientLinkedIn = person.profile_url;
                    console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient LinkedIn: ${person.profile_url}`);
                }
                else {
                    console.error(`🚀 SEQUENCE-SERVICE: ❌ No LinkedIn profile URL found for person ${person.id}`);
                }
                break;
            default:
                console.error(`🚀 SEQUENCE-SERVICE: ❌ Unknown medium: ${medium}`);
                break;
        }
        console.log(`🚀 SEQUENCE-SERVICE: Final contact info added to job data:`, {
            recipientEmail: jobData.recipientEmail,
            recipientPhone: jobData.recipientPhone,
            recipientLinkedIn: jobData.recipientLinkedIn
        });
    }
    async processNextSteps(candidateId, completedStepId) {
        try {
            const completedStatus = await this.candidateSequenceStatusService
                .getCandidateSequenceStatus(candidateId, 0)
                .then(statuses => statuses.find(s => s.stepId === completedStepId));
            if (!completedStatus) {
                throw new Error('Completed step status not found');
            }
            const sequenceId = completedStatus.sequenceId;
            const currentOrder = completedStatus.stepOrder;
            const currentOrderStatuses = await this.candidateSequenceStatusService
                .getCandidateSequenceStatus(candidateId, sequenceId)
                .then(statuses => statuses.filter(s => s.stepOrder === currentOrder));
            const allCurrentCompleted = currentOrderStatuses.every(s => s.status === candidate_sequence_status_entity_1.SequenceStepStatus.COMPLETED || s.status === candidate_sequence_status_entity_1.SequenceStepStatus.FAILED);
            if (allCurrentCompleted) {
                const nextSteps = await this.candidateSequenceStatusService.getNextSteps(candidateId, sequenceId, currentOrder);
                if (nextSteps.length > 0) {
                    await this.executeSteps(candidateId, nextSteps);
                    this.logger.log(`Next steps triggered for candidate ${candidateId} after completing step ${completedStepId}`);
                }
                else {
                    this.logger.log(`Sequence completed for candidate ${candidateId}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Failed to process next steps for candidate ${candidateId}: ${error.message}`);
            throw error;
        }
    }
    async getSequenceWithSteps(sequenceId) {
        const sequence = await this.roleSequenceRepository.findOne({
            where: { id: sequenceId },
            relations: ['sequenceSteps', 'sequenceSteps.emailTemplate'],
        });
        if (!sequence) {
            throw new common_1.NotFoundException('Sequence not found');
        }
        if (sequence.sequenceSteps) {
            sequence.sequenceSteps.sort((a, b) => a.order - b.order);
        }
        return sequence;
    }
    async getSequenceStats(sequenceId) {
        const sequence = await this.getSequenceWithSteps(sequenceId);
        const allStatuses = await this.candidateSequenceStatusService
            .getStepsByStatus(candidate_sequence_status_entity_1.SequenceStepStatus.PENDING, 10000)
            .then(statuses => statuses.filter(s => s.sequenceId === sequenceId));
        const stats = {
            sequenceId,
            sequenceName: sequence.name,
            totalSteps: sequence.sequenceSteps.length,
            totalCandidates: new Set(allStatuses.map(s => s.candidateId)).size,
            statusBreakdown: {
                pending: 0,
                queued: 0,
                sent: 0,
                delivered: 0,
                opened: 0,
                clicked: 0,
                replied: 0,
                completed: 0,
                failed: 0,
                skipped: 0,
            },
        };
        for (const status of allStatuses) {
            stats.statusBreakdown[status.status.toLowerCase()]++;
        }
        return stats;
    }
    async debugSequenceData(sequenceId) {
        try {
            const sequence = await this.roleSequenceRepository.findOne({
                where: { id: sequenceId },
                relations: ['sequenceSteps'],
            });
            const allCandidates = await this.candidateRepository.find({
                take: 10,
                relations: ['candidate', 'role'],
            });
            const existingStatuses = await this.candidateSequenceStatusService.getCandidatesInSequence(sequenceId);
            return {
                sequence: {
                    exists: !!sequence,
                    id: sequence?.id,
                    name: sequence?.name,
                    status: sequence?.status,
                    stepsCount: sequence?.sequenceSteps?.length || 0,
                    steps: sequence?.sequenceSteps?.map(step => ({
                        id: step.id,
                        name: step.name,
                        order: step.order,
                        medium: step.medium,
                        type: step.type,
                        templateId: step.templateId,
                        roleSequenceId: step.roleSequenceId
                    })) || []
                },
                candidates: {
                    totalCount: allCandidates.length,
                    candidates: allCandidates.map(candidate => ({
                        id: candidate.id,
                        candidateId: candidate.candidateId,
                        roleId: candidate.roleId,
                        hasCandidate: !!candidate.candidate,
                        candidateName: candidate.candidate ? `${candidate.candidate.first_name} ${candidate.candidate.last_name}` : 'No candidate record',
                        hasRole: !!candidate.role,
                        roleName: candidate.role?.title || 'No role'
                    }))
                },
                existingStatuses: {
                    count: existingStatuses.length,
                    statuses: existingStatuses.map(status => ({
                        candidateId: status.candidateId,
                        stepId: status.stepId,
                        status: status.status,
                        stepOrder: status.stepOrder
                    }))
                },
                validation: {
                    sequenceExists: !!sequence,
                    sequenceHasSteps: sequence?.sequenceSteps?.length > 0,
                    candidatesExist: allCandidates.length > 0,
                    candidatesHaveValidRecords: allCandidates.filter(c => c.candidate && c.candidate.id).length
                }
            };
        }
        catch (error) {
            this.logger.error(`Failed to debug sequence ${sequenceId}: ${error.message}`);
            return {
                error: error.message,
                stack: error.stack
            };
        }
    }
    async checkDatabaseHealth() {
        try {
            const sequenceCount = await this.roleSequenceRepository.count();
            const sequences = await this.roleSequenceRepository.find({
                take: 5,
                relations: ['sequenceSteps'],
            });
            const stepCount = await this.stepRepository.count();
            const steps = await this.stepRepository.find({
                take: 5,
                relations: ['roleSequence'],
            });
            const candidateCount = await this.candidateRepository.count();
            const candidates = await this.candidateRepository.find({
                take: 5,
                relations: ['candidate', 'role'],
            });
            const peopleCount = await this.peopleRepository.count();
            const people = await this.peopleRepository.find({
                take: 5,
                relations: ['emails', 'phones'],
            });
            return {
                summary: {
                    sequences: sequenceCount,
                    steps: stepCount,
                    roleCandidates: candidateCount,
                    people: peopleCount,
                    healthy: sequenceCount > 0 && stepCount > 0 && candidateCount > 0 && peopleCount > 0
                },
                details: {
                    sequences: sequences.map(seq => ({
                        id: seq.id,
                        name: seq.name,
                        status: seq.status,
                        stepsCount: seq.sequenceSteps?.length || 0
                    })),
                    steps: steps.map(step => ({
                        id: step.id,
                        name: step.name,
                        medium: step.medium,
                        order: step.order,
                        sequenceId: step.roleSequenceId,
                        hasSequence: !!step.roleSequence
                    })),
                    roleCandidates: candidates.map(candidate => ({
                        id: candidate.id,
                        candidateId: candidate.candidateId,
                        roleId: candidate.roleId,
                        hasCandidate: !!candidate.candidate,
                        hasRole: !!candidate.role
                    })),
                    people: people.map(person => ({
                        id: person.id,
                        name: `${person.first_name} ${person.last_name}`,
                        emailCount: person.emails?.length || 0,
                        phoneCount: person.phones?.length || 0
                    }))
                },
                recommendations: this.generateHealthRecommendations(sequenceCount, stepCount, candidateCount, peopleCount)
            };
        }
        catch (error) {
            this.logger.error(`Failed to check database health: ${error.message}`);
            return {
                error: error.message,
                healthy: false
            };
        }
    }
    generateHealthRecommendations(sequences, steps, candidates, people) {
        const recommendations = [];
        if (sequences === 0) {
            recommendations.push('No sequences found. Create a sequence first using the Advanced Sequences component.');
        }
        if (steps === 0) {
            recommendations.push('No sequence steps found. Add steps to your sequences.');
        }
        if (candidates === 0) {
            recommendations.push('No role candidates found. Add candidates to roles first.');
        }
        if (people === 0) {
            recommendations.push('No people records found. Import or create candidate profiles.');
        }
        if (candidates > 0 && people === 0) {
            recommendations.push('Role candidates exist but no people records found. Ensure candidates are linked to people records.');
        }
        if (recommendations.length === 0) {
            recommendations.push('Database appears healthy for sequence execution.');
        }
        return recommendations;
    }
};
exports.SequenceService = SequenceService;
exports.SequenceService = SequenceService = SequenceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sequence_entity_1.RoleSequence)),
    __param(1, (0, typeorm_1.InjectRepository)(sequence_steps_entity_1.SequenceSteps)),
    __param(2, (0, typeorm_1.InjectRepository)(role_candidates_entity_1.RoleCandidate)),
    __param(3, (0, typeorm_1.InjectRepository)(people_entity_1.People)),
    __param(4, (0, typeorm_1.InjectRepository)(emails_entity_1.PersonEmail)),
    __param(5, (0, typeorm_1.InjectRepository)(phone_entity_1.PersonPhone)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        candidate_sequence_status_service_1.CandidateSequenceStatusService,
        queue_service_1.QueueService])
], SequenceService);
//# sourceMappingURL=sequence.service.js.map