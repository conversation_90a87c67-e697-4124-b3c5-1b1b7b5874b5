"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PersonSkillDto = exports.SkillProficiencyLevel = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var SkillProficiencyLevel;
(function (SkillProficiencyLevel) {
    SkillProficiencyLevel["BEGINNER"] = "BEGINNER";
    SkillProficiencyLevel["INTERMEDIATE"] = "INTERMEDIATE";
    SkillProficiencyLevel["ADVANCED"] = "ADVANCED";
    SkillProficiencyLevel["EXPERT"] = "EXPERT";
})(SkillProficiencyLevel || (exports.SkillProficiencyLevel = SkillProficiencyLevel = {}));
class PersonSkillDto {
}
exports.PersonSkillDto = PersonSkillDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'JavaScript',
        description: 'The name of the skill',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PersonSkillDto.prototype, "skill_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'ADVANCED',
        description: 'Proficiency level of the skill',
        enum: SkillProficiencyLevel,
        required: false,
    }),
    (0, class_validator_1.IsEnum)(SkillProficiencyLevel),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], PersonSkillDto.prototype, "proficiency_level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'personId',
        description: 'Proficiency level of the skill',
        type: Number,
        required: false,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], PersonSkillDto.prototype, "personId", void 0);
//# sourceMappingURL=person-skill.dto.js.map