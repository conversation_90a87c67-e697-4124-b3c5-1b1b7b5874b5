import { SkillsService } from './skills.service';
import { PersonSkillDto } from './dto/person-skill.dto';
import { UpdatepersonSkillsDto } from './dto/updatePersonSkills.dto';
export declare class SkillsController {
    private readonly skillsService;
    constructor(skillsService: SkillsService);
    createSkill(skill: PersonSkillDto): Promise<import("./skills.entity").PersonSkill>;
    updateSkill(skill: UpdatepersonSkillsDto): Promise<import("./skills.entity").PersonSkill>;
    deleteSkill(id: number): Promise<void>;
    listSkills(): Promise<import("./skills.entity").PersonSkill[]>;
}
