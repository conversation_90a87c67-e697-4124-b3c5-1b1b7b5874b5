import { PersonSkill } from './skills.entity';
import { Repository } from 'typeorm';
import { PersonSkillDto } from './dto/person-skill.dto';
import { UpdatepersonSkillsDto } from './dto/updatePersonSkills.dto';
export declare class SkillsService {
    private readonly skillsRepository;
    constructor(skillsRepository: Repository<PersonSkill>);
    createSkill(skill: PersonSkillDto): Promise<PersonSkill>;
    updateSkill(skill: UpdatepersonSkillsDto): Promise<PersonSkill>;
    deleteSkill(id: number): Promise<void>;
    getAllSkills(): Promise<PersonSkill[]>;
    getSkillById(id: number): Promise<PersonSkill>;
    getSkillsByPersonId(personId: number): Promise<PersonSkill[]>;
}
