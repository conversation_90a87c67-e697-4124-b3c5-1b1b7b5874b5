"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillsService = void 0;
const common_1 = require("@nestjs/common");
const skills_entity_1 = require("./skills.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let SkillsService = class SkillsService {
    constructor(skillsRepository) {
        this.skillsRepository = skillsRepository;
    }
    async createSkill(skill) {
        try {
            const newSkill = this.skillsRepository.create(skill);
            return await this.skillsRepository.save(newSkill);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error creating skill',
                error: error.message,
            });
        }
    }
    async updateSkill(skill) {
        try {
            const existingSkill = await this.skillsRepository.findOne({
                where: { id: skill.id },
            });
            if (!existingSkill) {
                throw new common_1.NotFoundException('Skill not found');
            }
            Object.assign(existingSkill, skill);
            return await this.skillsRepository.save(existingSkill);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error updating skill',
                error: error.message,
            });
        }
    }
    async deleteSkill(id) {
        try {
            const skill = await this.skillsRepository.findOne({ where: { id } });
            if (!skill) {
                throw new common_1.NotFoundException('Skill not found');
            }
            await this.skillsRepository.remove(skill);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error deleting skill',
                error: error.message,
            });
        }
    }
    async getAllSkills() {
        try {
            return await this.skillsRepository.find();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching skills',
                error: error.message,
            });
        }
    }
    async getSkillById(id) {
        try {
            const skill = await this.skillsRepository.findOne({ where: { id } });
            if (!skill) {
                throw new common_1.NotFoundException('Skill not found');
            }
            return skill;
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching skill',
                error: error.message,
            });
        }
    }
    async getSkillsByPersonId(personId) {
        try {
            return await this.skillsRepository.find({
                where: { person: { id: personId } },
            });
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                message: 'Error fetching skills by person ID',
                error: error.message,
            });
        }
    }
};
exports.SkillsService = SkillsService;
exports.SkillsService = SkillsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(skills_entity_1.PersonSkill)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SkillsService);
//# sourceMappingURL=skills.service.js.map