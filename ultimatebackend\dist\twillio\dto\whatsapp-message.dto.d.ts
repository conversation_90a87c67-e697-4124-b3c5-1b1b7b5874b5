export declare class WhatsAppMessageDto {
    to: string;
    message: string;
    mediaUrl?: string;
    mediaType?: string;
    region?: string;
    isTemplate?: boolean;
    templateData?: Record<string, any>;
}
export declare class WhatsAppStatusDto {
    messageSid: string;
    messageStatus: string;
    region?: string;
    templateData?: Record<string, any>;
    isTemplate?: boolean;
    errorMessage?: string;
}
export declare class SmsMessageDto {
    to: string;
    message: string;
    region?: string;
}
