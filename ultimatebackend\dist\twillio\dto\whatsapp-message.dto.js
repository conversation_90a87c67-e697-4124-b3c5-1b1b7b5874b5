"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsMessageDto = exports.WhatsAppStatusDto = exports.WhatsAppMessageDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class WhatsAppMessageDto {
}
exports.WhatsAppMessageDto = WhatsAppMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient phone number in E.164 format, e.g., +1234567890',
        example: '+1234567890',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], WhatsAppMessageDto.prototype, "to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Text message to send',
        example: 'Hello from WhatsApp!',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], WhatsAppMessageDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional media URL to send with the message',
        example: 'https://example.com/image.jpg',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], WhatsAppMessageDto.prototype, "mediaUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional media type (e.g., image, video, audio)',
        example: 'image',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WhatsAppMessageDto.prototype, "mediaType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Region for WhatsApp sender (UK or US)',
        example: 'UK',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WhatsAppMessageDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the message is a template message',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], WhatsAppMessageDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional template data for template messages',
        example: { name: 'John Doe', title: 'Welcome', location: 'New York' },
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], WhatsAppMessageDto.prototype, "templateData", void 0);
class WhatsAppStatusDto {
}
exports.WhatsAppStatusDto = WhatsAppStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Twilio Message SID',
        example: 'SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], WhatsAppStatusDto.prototype, "messageSid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the message',
        example: 'delivered',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], WhatsAppStatusDto.prototype, "messageStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional error message if the status is failed',
        example: 'Message failed due to insufficient funds',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WhatsAppStatusDto.prototype, "region", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional template data for template messages',
        example: { name: 'John Doe', title: 'Welcome', location: 'New York' },
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], WhatsAppStatusDto.prototype, "templateData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the message is a template message',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], WhatsAppStatusDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional media URL to send with the message',
        example: 'https://example.com/image.jpg',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], WhatsAppStatusDto.prototype, "errorMessage", void 0);
class SmsMessageDto {
}
exports.SmsMessageDto = SmsMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient phone number in E.164 format, e.g., +1234567890',
        example: '+1234567890',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SmsMessageDto.prototype, "to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Text message to send',
        example: 'Hello from SMS!',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SmsMessageDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Region for SMS sender (UK or US)',
        example: 'UK',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], SmsMessageDto.prototype, "region", void 0);
//# sourceMappingURL=whatsapp-message.dto.js.map