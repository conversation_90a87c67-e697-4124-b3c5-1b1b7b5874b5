"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendMessageDto = exports.MessageChannel = exports.SendWhatsAppDto = exports.SendTemplateWhatsAppDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class SendTemplateWhatsAppDto {
}
exports.SendTemplateWhatsAppDto = SendTemplateWhatsAppDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SendTemplateWhatsAppDto.prototype, "to", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SendTemplateWhatsAppDto.prototype, "templateName", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Array)
], SendTemplateWhatsAppDto.prototype, "templateParams", void 0);
class SendWhatsAppDto {
}
exports.SendWhatsAppDto = SendWhatsAppDto;
var MessageChannel;
(function (MessageChannel) {
    MessageChannel["SMS"] = "sms";
    MessageChannel["WHATSAPP"] = "whatsapp";
})(MessageChannel || (exports.MessageChannel = MessageChannel = {}));
class SendMessageDto {
}
exports.SendMessageDto = SendMessageDto;
__decorate([
    (0, class_validator_1.IsPhoneNumber)(null),
    __metadata("design:type", String)
], SendMessageDto.prototype, "to", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendMessageDto.prototype, "message", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(MessageChannel),
    (0, class_transformer_1.Type)(() => String),
    __metadata("design:type", String)
], SendMessageDto.prototype, "channel", void 0);
//# sourceMappingURL=whatsapp.dto.js.map