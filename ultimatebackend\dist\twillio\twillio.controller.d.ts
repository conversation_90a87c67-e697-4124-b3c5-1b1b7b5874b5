import { TwillioService } from './twillio.service';
import { Request, Response } from 'express';
import { SmsMessageDto, WhatsAppMessageDto } from './dto/whatsapp-message.dto';
import { WhatsAppMessage } from './whatsapp-message.entity';
import { Repository } from 'typeorm';
import { TwillioGateway } from './twillio.gateway';
export declare class TwillioController {
    private readonly twillioService;
    private readonly whatsappMessageRepo;
    private readonly twillioGateway;
    private readonly logger;
    constructor(twillioService: TwillioService, whatsappMessageRepo: Repository<WhatsAppMessage>, twillioGateway: TwillioGateway);
    sendWhatsAppMessage(messageDto: WhatsAppMessageDto): Promise<{
        success: boolean;
        messageSid: any;
        status: any;
    }>;
    handleIncomingWhatsApp(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    receiveWhatsAppStatus(body: any): Promise<{
        success: boolean;
        status: string;
    }>;
    getMessageStatus(messageSid: string): Promise<{
        success: boolean;
        status: import("twilio/lib/rest/api/v2010/account/message").MessageStatus;
    }>;
    getToken(identity: string): {
        token: string;
    };
    handleWebhook(req: Request, res: Response): void;
    getWhatsAppHistory(number: string): Promise<{
        success: boolean;
        messages: WhatsAppMessage[];
    }>;
    getWhatsAppMessagesByNumber(phoneNumber: string): Promise<WhatsAppMessage[]>;
    sendSMSMessage(messageDto: SmsMessageDto): Promise<{
        success: boolean;
        messageSid: string;
        status: import("twilio/lib/rest/api/v2010/account/message").MessageStatus;
    }>;
    getSmsMessagesByPhoneNumber(phoneNumber: string): Promise<WhatsAppMessage[]>;
    handleIncomingSMS(body: any): Promise<{
        success: boolean;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
    }>;
    receiveSMSStatus(body: any): Promise<{
        success: boolean;
        status: string;
    }>;
    handleTwilioError(body: any): Promise<{
        success: boolean;
        message: string;
    }>;
    sendCampaign(candidates: any[]): Promise<{
        results: any[];
        summary: {
            whatsappSent: number;
            whatsappFailed: number;
            emailSent: number;
            emailFailed: number;
        };
    }>;
    testPhoneFormat(body: {
        phoneNumber: string;
        countryCode?: string;
    }): Promise<{
        success: boolean;
        original: string;
        formatted: string;
        countryCode: string;
        message: string;
    }>;
}
