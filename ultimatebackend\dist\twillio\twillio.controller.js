"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TwillioController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwillioController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const twillio_service_1 = require("./twillio.service");
const twilio = require("twilio");
const whatsapp_message_dto_1 = require("./dto/whatsapp-message.dto");
const whatsapp_message_entity_1 = require("./whatsapp-message.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const twillio_gateway_1 = require("./twillio.gateway");
const phone_formatter_util_1 = require("../utils/phone-formatter.util");
let TwillioController = TwillioController_1 = class TwillioController {
    constructor(twillioService, whatsappMessageRepo, twillioGateway) {
        this.twillioService = twillioService;
        this.whatsappMessageRepo = whatsappMessageRepo;
        this.twillioGateway = twillioGateway;
        this.logger = new common_1.Logger(TwillioController_1.name);
    }
    async sendWhatsAppMessage(messageDto) {
        try {
            let to = messageDto.to.trim();
            try {
                to = (0, phone_formatter_util_1.formatToE164)(to);
                this.logger.log(`Phone number formatted to E.164: ${to}`);
            }
            catch (formatError) {
                this.logger.error(`Failed to format phone number: ${formatError.message}`);
                throw new common_1.HttpException(`Invalid phone number format: ${formatError.message}`, common_1.HttpStatus.BAD_REQUEST);
            }
            if (!to.startsWith('whatsapp:')) {
                to = `whatsapp:${to}`;
            }
            this.logger.log(`Sending WhatsApp message to ${to}...`);
            const result = await this.twillioService.sendWhatsAppMessage(to, messageDto.message, messageDto.mediaUrl, messageDto.region, messageDto.isTemplate || false, messageDto.templateData);
            const savedMessage = await this.twillioService.saveSentWhatsAppMessage(result, to, messageDto.message, messageDto.mediaUrl, messageDto.mediaType);
            this.twillioGateway.sendWhatsAppMessageNotification(savedMessage);
            return {
                success: true,
                messageSid: result.sid,
                status: result.status,
            };
        }
        catch (error) {
            this.logger.error(`Failed to send WhatsApp message: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async handleIncomingWhatsApp(req, res) {
        try {
            const body = req.body;
            this.logger.log(`Incoming WhatsApp message: ${JSON.stringify(body)}`);
            const from = body.From?.replace('whatsapp:', '');
            const to = body.To?.replace('whatsapp:', '');
            const messageSid = body.MessageSid;
            const messageType = body.MessageType;
            const messageToSave = {
                messageSid,
                sender: from,
                status: 'received',
                messageType,
                timestamp: new Date(),
            };
            if (messageType === 'document' || messageType === 'image') {
                messageToSave.text = 'Media';
                messageToSave.mediaType = body.MediaContentType0;
                messageToSave.mediaUrl = body.MediaUrl0;
            }
            else {
                messageToSave.text = body.Body;
            }
            await this.twillioService.saveMessage(to, from, messageToSave);
            this.twillioGateway.sendWhatsAppMessageNotification({
                messageSid: body.MessageSid,
                sender: body.From,
                to: body.To,
                text: body.Body,
                status: body.SmsStatus || 'received',
                messageType: body.MessageType,
                timestamp: new Date().toISOString(),
            });
            return res.status(200).send('OK');
        }
        catch (error) {
            this.logger.error(`Error processing incoming WhatsApp message: ${error.message}`);
            return res.status(500).send('Error');
        }
    }
    async receiveWhatsAppStatus(body) {
        try {
            this.logger.log(`WhatsApp status update received: ${JSON.stringify(body)}`);
            const messageSid = body.MessageSid;
            const messageStatus = body.MessageStatus;
            const errorMessage = body.ErrorMessage || null;
            const statusUpdate = {
                messageSid,
                messageStatus,
            };
            await this.twillioService.saveWhatsAppStatusUpdate(messageSid, messageStatus);
            this.twillioGateway.handleUpdateMessageStatus(statusUpdate);
            return { success: true, status: 'Status update received' };
        }
        catch (error) {
            this.logger.error(`Error processing WhatsApp status update: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getMessageStatus(messageSid) {
        try {
            const status = await this.twillioService.getMessageStatus(messageSid);
            return {
                success: true,
                status,
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    getToken(identity) {
        const userIdentity = identity || 'default_user';
        const token = this.twillioService.generateAccessToken(userIdentity);
        return { token };
    }
    handleWebhook(req, res) {
        const twiml = new twilio.twiml.VoiceResponse();
        const toNumber = req.body.To;
        console.log('Webhook received:', req.body);
        console.log('To Number:', toNumber);
        if (toNumber) {
            const dial = twiml.dial({
                callerId: process.env.TWILIO_PHONE_NUMBER,
            });
            dial.number(toNumber);
        }
        else {
            twiml.say('Thank you for calling!');
        }
        res.type('text/xml');
        res.send(twiml.toString());
    }
    async getWhatsAppHistory(number) {
        const messages = await this.whatsappMessageRepo.find({
            where: [{ to: number }, { from: number }],
            order: { createdAt: 'ASC' },
        });
        return { success: true, messages };
    }
    async getWhatsAppMessagesByNumber(phoneNumber) {
        try {
            const messages = await this.twillioService.getWhatsAppMessagesByNumber(phoneNumber);
            return messages;
        }
        catch (error) {
            this.logger.error(`Error fetching WhatsApp messages for ${phoneNumber}: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async sendSMSMessage(messageDto) {
        try {
            let to = messageDto.to.trim();
            try {
                to = (0, phone_formatter_util_1.formatToE164)(to);
                this.logger.log(`Phone number formatted to E.164: ${to}`);
            }
            catch (formatError) {
                this.logger.error(`Failed to format phone number: ${formatError.message}`);
                throw new common_1.HttpException(`Invalid phone number format: ${formatError.message}`, common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log(`Sending SMS message to ${to}...`);
            const result = await this.twillioService.sendSMSMessage(to, messageDto.message);
            const savedMessage = await this.twillioService.saveSentSMSMessage(result, to, messageDto.message);
            this.twillioGateway.sendSMSMessageNotification(savedMessage);
            return {
                success: true,
                messageSid: result.sid,
                status: result.status,
            };
        }
        catch (error) {
            this.logger.error(`Failed to send SMS message: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getSmsMessagesByPhoneNumber(phoneNumber) {
        try {
            const formattedNumber = phoneNumber.startsWith('+')
                ? phoneNumber
                : `+${phoneNumber}`;
            const messages = await this.whatsappMessageRepo.find({
                where: [
                    { to: (0, typeorm_1.ILike)(`%${formattedNumber}`) },
                    { from: (0, typeorm_1.ILike)(`%${formattedNumber}`) },
                ],
                order: { createdAt: 'ASC' },
            });
            const smsMessages = messages.filter((msg) => !msg.to?.includes('whatsapp:') && !msg.from?.includes('whatsapp:'));
            return smsMessages;
        }
        catch (error) {
            this.logger.error(`Error fetching SMS messages for ${phoneNumber}: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handleIncomingSMS(body) {
        try {
            this.logger.log(`Incoming SMS message: ${JSON.stringify(body)}`);
            const from = body.From;
            const to = body.To;
            const messageSid = body.MessageSid;
            const messageToSave = {
                messageSid,
                sender: from,
                status: 'received',
                text: body.Body,
                timestamp: new Date(),
            };
            await this.twillioService.saveMessage(to, from, messageToSave);
            this.twillioGateway.sendSMSMessageNotification(messageToSave);
            return { success: true };
        }
        catch (error) {
            this.logger.error(`Error processing incoming SMS message: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async receiveSMSStatus(body) {
        try {
            this.logger.log(`SMS status update received: ${JSON.stringify(body)}`);
            const messageSid = body.MessageSid;
            const messageStatus = body.MessageStatus;
            const errorMessage = body.ErrorMessage || null;
            await this.twillioService.saveWhatsAppStatusUpdate(messageSid, messageStatus);
            this.twillioGateway.handleUpdateMessageStatus({
                messageSid,
                messageStatus,
                errorMessage,
            });
            return { success: true, status: 'Status update received' };
        }
        catch (error) {
            this.logger.error(`Error processing SMS status update: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async handleTwilioError(body) {
        this.logger.error(`Twilio error received: ${JSON.stringify(body)}`);
        return { success: true, message: 'Error received' };
    }
    async sendCampaign(candidates) {
        return this.twillioService.sendCombinedWhatsAppAndEmail(candidates);
    }
    async testPhoneFormat(body) {
        try {
            const { phoneNumber, countryCode = '+44' } = body;
            if (!phoneNumber) {
                throw new common_1.HttpException('Phone number is required', common_1.HttpStatus.BAD_REQUEST);
            }
            const formattedNumber = (0, phone_formatter_util_1.formatToE164)(phoneNumber, countryCode);
            let detectedCountryCode = countryCode;
            const knownCountryCodes = [
                { code: '1', name: 'US/Canada' },
                { code: '44', name: 'UK' },
                { code: '33', name: 'France' },
                { code: '49', name: 'Germany' },
                { code: '39', name: 'Italy' },
                { code: '34', name: 'Spain' },
                { code: '31', name: 'Netherlands' },
                { code: '32', name: 'Belgium' },
                { code: '41', name: 'Switzerland' },
                { code: '43', name: 'Austria' },
                { code: '45', name: 'Denmark' },
                { code: '46', name: 'Sweden' },
                { code: '47', name: 'Norway' },
                { code: '358', name: 'Finland' },
                { code: '91', name: 'India' },
                { code: '86', name: 'China' },
                { code: '81', name: 'Japan' },
                { code: '82', name: 'South Korea' },
                { code: '61', name: 'Australia' },
                { code: '64', name: 'New Zealand' },
                { code: '55', name: 'Brazil' },
                { code: '52', name: 'Mexico' },
                { code: '7', name: 'Russia/Kazakhstan' },
                { code: '380', name: 'Ukraine' },
                { code: '48', name: 'Poland' },
                { code: '420', name: 'Czech Republic' },
                { code: '421', name: 'Slovakia' },
                { code: '36', name: 'Hungary' },
                { code: '40', name: 'Romania' },
                { code: '359', name: 'Bulgaria' },
                { code: '385', name: 'Croatia' },
                { code: '386', name: 'Slovenia' },
                { code: '381', name: 'Serbia' },
                { code: '382', name: 'Montenegro' },
                { code: '387', name: 'Bosnia and Herzegovina' },
                { code: '389', name: 'North Macedonia' },
                { code: '30', name: 'Greece' },
                { code: '90', name: 'Turkey' },
                { code: '972', name: 'Israel' },
                { code: '971', name: 'UAE' },
                { code: '966', name: 'Saudi Arabia' },
                { code: '965', name: 'Kuwait' },
                { code: '974', name: 'Qatar' },
                { code: '973', name: 'Bahrain' },
                { code: '968', name: 'Oman' },
                { code: '962', name: 'Jordan' },
                { code: '961', name: 'Lebanon' },
                { code: '963', name: 'Syria' },
                { code: '964', name: 'Iraq' },
                { code: '98', name: 'Iran' },
                { code: '92', name: 'Pakistan' },
                { code: '880', name: 'Bangladesh' },
                { code: '94', name: 'Sri Lanka' },
                { code: '95', name: 'Myanmar' },
                { code: '66', name: 'Thailand' },
                { code: '65', name: 'Singapore' },
                { code: '60', name: 'Malaysia' },
                { code: '62', name: 'Indonesia' },
                { code: '63', name: 'Philippines' },
                { code: '84', name: 'Vietnam' },
                { code: '855', name: 'Cambodia' },
                { code: '856', name: 'Laos' },
                { code: '853', name: 'Macau' },
                { code: '852', name: 'Hong Kong' },
                { code: '886', name: 'Taiwan' },
                { code: '20', name: 'Egypt' },
                { code: '27', name: 'South Africa' },
                { code: '234', name: 'Nigeria' },
                { code: '254', name: 'Kenya' },
                { code: '233', name: 'Ghana' },
                { code: '212', name: 'Morocco' },
                { code: '213', name: 'Algeria' },
                { code: '216', name: 'Tunisia' },
                { code: '218', name: 'Libya' },
                { code: '251', name: 'Ethiopia' },
                { code: '256', name: 'Uganda' },
                { code: '255', name: 'Tanzania' },
                { code: '260', name: 'Zambia' },
                { code: '263', name: 'Zimbabwe' },
                { code: '265', name: 'Malawi' },
                { code: '267', name: 'Botswana' },
                { code: '268', name: 'Eswatini' },
                { code: '264', name: 'Namibia' },
                { code: '266', name: 'Lesotho' }
            ];
            for (const country of knownCountryCodes.sort((a, b) => b.code.length - a.code.length)) {
                if (formattedNumber.startsWith(`+${country.code}`)) {
                    detectedCountryCode = `+${country.code}`;
                    break;
                }
            }
            return {
                success: true,
                original: phoneNumber,
                formatted: formattedNumber,
                countryCode: detectedCountryCode,
                message: 'Phone number formatted successfully'
            };
        }
        catch (error) {
            this.logger.error(`Phone formatting test failed: ${error.message}`);
            throw new common_1.HttpException({
                success: false,
                message: error.message,
                original: body.phoneNumber
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.TwillioController = TwillioController;
__decorate([
    (0, common_1.Post)('whatsapp/send'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a WhatsApp message (template or freeform)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Message sent successfully' }),
    (0, swagger_1.ApiBody)({ type: whatsapp_message_dto_1.WhatsAppMessageDto }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [whatsapp_message_dto_1.WhatsAppMessageDto]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "sendWhatsAppMessage", null);
__decorate([
    (0, common_1.Post)('whatsapp/recieve'),
    (0, swagger_1.ApiOperation)({
        summary: 'Receive incoming WhatsApp messages via Twilio Webhook',
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Message received' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "handleIncomingWhatsApp", null);
__decorate([
    (0, common_1.Post)('whatsapp/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Receive WhatsApp message status updates' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "receiveWhatsAppStatus", null);
__decorate([
    (0, common_1.Get)('whatsapp/status/:messageSid'),
    (0, swagger_1.ApiOperation)({ summary: 'Get WhatsApp message status' }),
    __param(0, (0, common_1.Param)('messageSid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "getMessageStatus", null);
__decorate([
    (0, common_1.Get)('token'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Twilio Access Token' }),
    __param(0, (0, common_1.Query)('identity')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TwillioController.prototype, "getToken", null);
__decorate([
    (0, common_1.Post)('webhook'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], TwillioController.prototype, "handleWebhook", null);
__decorate([
    (0, common_1.Get)('whatsapp/history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get WhatsApp message history for a number' }),
    __param(0, (0, common_1.Query)('number')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "getWhatsAppHistory", null);
__decorate([
    (0, common_1.Get)('whatsapp/messages/:phoneNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Get WhatsApp messages by phone number' }),
    __param(0, (0, common_1.Param)('phoneNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "getWhatsAppMessagesByNumber", null);
__decorate([
    (0, common_1.Post)('sms/send'),
    (0, swagger_1.ApiOperation)({ summary: 'Send an SMS message' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'SMS sent successfully' }),
    (0, swagger_1.ApiBody)({ type: whatsapp_message_dto_1.SmsMessageDto }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [whatsapp_message_dto_1.SmsMessageDto]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "sendSMSMessage", null);
__decorate([
    (0, common_1.Get)('sms/messages/:phoneNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Get SMS messages by phone number' }),
    __param(0, (0, common_1.Param)('phoneNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "getSmsMessagesByPhoneNumber", null);
__decorate([
    (0, common_1.Post)('sms/receive'),
    (0, swagger_1.ApiOperation)({ summary: 'Receive incoming SMS messages via Twilio Webhook' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "handleIncomingSMS", null);
__decorate([
    (0, common_1.Post)('sms/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Receive SMS message status updates' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "receiveSMSStatus", null);
__decorate([
    (0, common_1.Post)('error'),
    (0, swagger_1.ApiOperation)({ summary: 'Handle Twilio error webhook' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "handleTwilioError", null);
__decorate([
    (0, common_1.Post)('sendWAEmailCampaign'),
    (0, swagger_1.ApiOperation)({ summary: 'Send combined WhatsApp and Email campaign' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "sendCampaign", null);
__decorate([
    (0, common_1.Post)('test-phone-format'),
    (0, swagger_1.ApiOperation)({ summary: 'Test phone number formatting to E.164' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioController.prototype, "testPhoneFormat", null);
exports.TwillioController = TwillioController = TwillioController_1 = __decorate([
    (0, common_1.Controller)('twillio'),
    (0, swagger_1.ApiTags)('twillio'),
    __param(1, (0, typeorm_2.InjectRepository)(whatsapp_message_entity_1.WhatsAppMessage)),
    __metadata("design:paramtypes", [twillio_service_1.TwillioService,
        typeorm_1.Repository,
        twillio_gateway_1.TwillioGateway])
], TwillioController);
//# sourceMappingURL=twillio.controller.js.map