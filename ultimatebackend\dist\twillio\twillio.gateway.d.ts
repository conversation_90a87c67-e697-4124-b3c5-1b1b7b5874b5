import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TwillioService } from './twillio.service';
import { MessagesService } from 'src/messages/messages.service';
export declare class TwillioGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly twilioService;
    private readonly messagesService;
    server: Server;
    constructor(twilioService: TwillioService, messagesService: MessagesService);
    private normalizeNumber;
    handleConnection(client: Socket): void;
    handleDisconnect(client: Socket): void;
    sendWhatsAppMessageNotification(message: any): void;
    sendSMSMessageNotification(message: any): void;
    handleUpdateMessageStatus(data: any): Promise<void>;
    handleSendMessage(data: any, client: Socket): Promise<void>;
    handleSendMediaMessage(data: any, client: Socket): Promise<void>;
    handleGetMessageStatus(data: any, client: Socket): Promise<void>;
    handleSendSMSMessage(data: any, client: Socket): Promise<void>;
}
