"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwillioGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const twillio_service_1 = require("./twillio.service");
const messages_service_1 = require("../messages/messages.service");
const phone_formatter_util_1 = require("../utils/phone-formatter.util");
let TwillioGateway = class TwillioGateway {
    constructor(twilioService, messagesService) {
        this.twilioService = twilioService;
        this.messagesService = messagesService;
    }
    normalizeNumber(number) {
        return (0, phone_formatter_util_1.normalizePhoneNumber)(number);
    }
    handleConnection(client) {
        console.log('Client connected:', client.id);
    }
    handleDisconnect(client) {
        console.log('Client disconnected:', client.id);
    }
    sendWhatsAppMessageNotification(message) {
        console.log('Sending WhatsApp message notification:', message);
        const normalized = {
            ...message,
            sender: this.normalizeNumber(message.sender),
            from: this.normalizeNumber(message.from),
            to: this.normalizeNumber(message.to),
        };
        this.server.emit('whatsapp-message', normalized);
    }
    sendSMSMessageNotification(message) {
        const normalized = {
            ...message,
            sender: this.normalizeNumber(message.sender || message.from),
            from: this.normalizeNumber(message.from),
            to: this.normalizeNumber(message.to),
        };
        this.server.emit('sms-message', normalized);
    }
    async handleUpdateMessageStatus(data) {
        const { messageSid, messageStatus } = data;
        console.log(`Updating message status for SID ${messageSid} to ${messageStatus}`);
        try {
            const updatedMessage = await this.twilioService.saveWhatsAppStatusUpdate(messageSid, messageStatus);
            this.server.emit('message-status-updated', {
                messageSid: updatedMessage.messageSid,
                status: updatedMessage.status,
            });
        }
        catch (error) {
            console.error('Error updating message status:', error);
        }
    }
    async handleSendMessage(data, client) {
        const { from, to, message, templateData } = data;
        try {
            const normalizedFrom = this.normalizeNumber(from);
            const normalizedTo = this.normalizeNumber(to);
            const chat = await this.messagesService.getOrCreateChat(normalizedFrom, normalizedTo);
            const isWithin24h = chat.lastInboundTimestamp && Date.now() - new Date(chat.lastInboundTimestamp).getTime() < 24 * 60 * 60 * 1000;
            let response;
            if (isWithin24h) {
                response = await this.twilioService.sendWhatsAppMessage(normalizedTo, message);
            }
            else {
                response = await this.twilioService.sendWhatsAppMessage(normalizedTo, '', undefined, 'UK', true, templateData);
            }
            const saved = await this.messagesService.saveMessage(normalizedFrom, normalizedTo, {
                messageSid: response.sid,
                sender: normalizedFrom,
                text: message || 'Template sent',
                status: response.status,
                timestamp: new Date(),
            });
            this.sendWhatsAppMessageNotification(saved);
            client.emit('message-sent', { status: 'success', response: saved });
        }
        catch (error) {
            client.emit('send-error', { message: error.message });
        }
    }
    async handleSendMediaMessage(data, client) {
        const { from, to, mediaUrl } = data;
        try {
            const response = await this.twilioService.sendMediaMessage(to, from, mediaUrl);
            const saved = await this.messagesService.saveMessage(from, to, {
                messageSid: response.sid,
                sender: from,
                mediaUrl,
                status: response.status,
                timestamp: new Date(),
            });
            this.sendWhatsAppMessageNotification(saved);
            client.emit('media-sent', { status: 'success', response: saved });
        }
        catch (error) {
            client.emit('send-error', { message: error.message });
        }
    }
    async handleGetMessageStatus(data, client) {
        const { messageSid } = data;
        try {
            const status = await this.twilioService.getMessageStatus(messageSid);
            client.emit('message-status', { status });
        }
        catch (error) {
            client.emit('status-error', { message: error.message });
        }
    }
    async handleSendSMSMessage(data, client) {
        const { from, to, message } = data;
        try {
            const normalizedFrom = this.normalizeNumber(from);
            const normalizedTo = this.normalizeNumber(to);
            const response = await this.twilioService.sendSMSMessage(normalizedTo, message);
            const saved = await this.messagesService.saveMessage(normalizedFrom, normalizedTo, {
                messageSid: response.sid,
                sender: normalizedFrom,
                text: message,
                status: response.status,
                timestamp: new Date(),
            });
            this.sendSMSMessageNotification(saved);
            client.emit('sms-message-sent', { status: 'success', response: saved });
        }
        catch (error) {
            client.emit('send-error', { message: error.message });
        }
    }
};
exports.TwillioGateway = TwillioGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], TwillioGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('whatsapp-message'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TwillioGateway.prototype, "sendWhatsAppMessageNotification", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('update-message-status'),
    __param(0, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TwillioGateway.prototype, "handleUpdateMessageStatus", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('send-message'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], TwillioGateway.prototype, "handleSendMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('send-media-message'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], TwillioGateway.prototype, "handleSendMediaMessage", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('get-message-status'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], TwillioGateway.prototype, "handleGetMessageStatus", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('send-sms-message'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], TwillioGateway.prototype, "handleSendSMSMessage", null);
exports.TwillioGateway = TwillioGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({ cors: { origin: '*', methods: ['GET', 'POST'] } }),
    __metadata("design:paramtypes", [twillio_service_1.TwillioService,
        messages_service_1.MessagesService])
], TwillioGateway);
//# sourceMappingURL=twillio.gateway.js.map