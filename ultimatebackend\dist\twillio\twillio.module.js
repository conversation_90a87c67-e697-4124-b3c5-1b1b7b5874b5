"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwillioModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const twillio_service_1 = require("./twillio.service");
const twillio_controller_1 = require("./twillio.controller");
const whatsapp_message_entity_1 = require("./whatsapp-message.entity");
const twillio_gateway_1 = require("./twillio.gateway");
const messages_module_1 = require("../messages/messages.module");
const email_module_1 = require("../email/email.module");
let TwillioModule = class TwillioModule {
};
exports.TwillioModule = TwillioModule;
exports.TwillioModule = TwillioModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([whatsapp_message_entity_1.WhatsAppMessage]), messages_module_1.MessagesModule, email_module_1.EmailModule],
        providers: [twillio_service_1.TwillioService, twillio_gateway_1.TwillioGateway],
        controllers: [twillio_controller_1.TwillioController],
        exports: [typeorm_1.TypeOrmModule, twillio_service_1.TwillioService, twillio_gateway_1.TwillioGateway],
    })
], TwillioModule);
//# sourceMappingURL=twillio.module.js.map