import { Repository } from 'typeorm';
import { WhatsAppMessage } from './whatsapp-message.entity';
import { EmailService } from 'src/email/email.service';
export declare class TwillioService {
    private readonly whatsappMessageRepo;
    private readonly emailService;
    private client;
    private readonly whatsappNumberUK;
    private readonly whatsappNumberUS;
    constructor(whatsappMessageRepo: Repository<WhatsAppMessage>, emailService: EmailService);
    generateAccessToken(identity: string): string;
    sendWhatsAppMessage(to: string, message: string, mediaUrl?: string, region?: string, isTemplate?: boolean, templateData?: Record<string, any>): Promise<any>;
    saveWhatsAppStatusUpdate(messageSid: string, status: string): Promise<WhatsAppMessage>;
    sendMediaMessage(to: string, from: string, mediaUrl: string): Promise<import("twilio/lib/rest/api/v2010/account/message").MessageInstance>;
    getMessageStatus(messageSid: string): Promise<import("twilio/lib/rest/api/v2010/account/message").MessageStatus>;
    getMediaContent(mediaSid: string): Promise<string>;
    saveSentWhatsAppMessage(result: any, to: string, body: string, mediaUrl?: string, mediaContentType?: string): Promise<WhatsAppMessage>;
    saveReceivedWhatsAppMessage(data: {
        messageSid: string;
        from: string;
        to: string;
        body: string;
        mediaUrl?: string;
        mediaContentType?: string;
        status?: string;
    }): Promise<WhatsAppMessage>;
    sendSMSMessage(to: string, message: string): Promise<import("twilio/lib/rest/api/v2010/account/message").MessageInstance>;
    saveMessage(from: string, to: string, messageData: {
        messageSid?: string;
        sender?: string;
        text?: string;
        mediaUrl?: string;
        status?: string;
        timestamp?: Date;
    }): Promise<WhatsAppMessage>;
    saveSentSMSMessage(result: any, to: string, body: string): Promise<WhatsAppMessage>;
    getWhatsAppMessagesByNumber(phoneNumber: string): Promise<WhatsAppMessage[]>;
    sendCombinedWhatsAppAndEmail(payload: any): Promise<{
        results: any[];
        summary: {
            whatsappSent: number;
            whatsappFailed: number;
            emailSent: number;
            emailFailed: number;
        };
    }>;
    private generateSummary;
}
