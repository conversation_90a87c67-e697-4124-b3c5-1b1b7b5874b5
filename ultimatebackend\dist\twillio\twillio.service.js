"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwillioService = void 0;
const common_1 = require("@nestjs/common");
const twilio_1 = require("twilio");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const whatsapp_message_entity_1 = require("./whatsapp-message.entity");
const email_service_1 = require("../email/email.service");
const phone_formatter_util_1 = require("../utils/phone-formatter.util");
let TwillioService = class TwillioService {
    constructor(whatsappMessageRepo, emailService) {
        this.whatsappMessageRepo = whatsappMessageRepo;
        this.emailService = emailService;
        this.client = new twilio_1.Twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        this.whatsappNumberUK = process.env.TWILIO_WHATSAPP_PHONE_NUMBER_UK;
        this.whatsappNumberUS = process.env.TWILIO_WHATSAPP_PHONE_NUMBER_US;
    }
    generateAccessToken(identity) {
        const AccessToken = twilio_1.jwt.AccessToken;
        const VoiceGrant = AccessToken.VoiceGrant;
        const token = new AccessToken(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_API_KEY, process.env.TWILIO_API_SECRET, { identity });
        const voiceGrant = new VoiceGrant({
            outgoingApplicationSid: process.env.TWILIO_TWIML_APP_SID,
            incomingAllow: true,
        });
        token.addGrant(voiceGrant);
        return token.toJwt();
    }
    async sendWhatsAppMessage(to, message, mediaUrl, region = 'UK', isTemplate = false, templateData) {
        let formattedTo = to.trim();
        if (formattedTo.startsWith('whatsapp:')) {
            formattedTo = formattedTo.replace('whatsapp:', '');
        }
        try {
            formattedTo = (0, phone_formatter_util_1.formatToE164)(formattedTo);
            console.log(`📞 TWILIO-SERVICE: Phone number formatted to E.164: ${formattedTo}`);
        }
        catch (formatError) {
            console.error(`📞 TWILIO-SERVICE: Failed to format phone number: ${formatError.message}`);
            throw new Error(`Invalid phone number format: ${formatError.message}`);
        }
        if (!formattedTo.startsWith('whatsapp:')) {
            formattedTo = `whatsapp:${formattedTo}`;
        }
        const from = region === 'UK' ? this.whatsappNumberUK : this.whatsappNumberUS;
        if (!from) {
            throw new Error(`WhatsApp number for region ${region} is not configured in .env`);
        }
        try {
            let response;
            if (isTemplate) {
                if (!templateData) {
                    throw new Error('Template data is required for template messages');
                }
                response = await this.client.messages.create({
                    from: from,
                    to: formattedTo,
                    contentSid: process.env.TWILIO_WHATSAPP_TEMPLATE_SID,
                    contentVariables: JSON.stringify({
                        ...templateData,
                    }),
                });
            }
            else {
                const payload = {
                    from: from,
                    to: formattedTo,
                    body: message,
                };
                if (mediaUrl) {
                    payload.mediaUrl = mediaUrl;
                }
                response = await this.client.messages.create(payload);
            }
            return response;
        }
        catch (error) {
            console.error('Failed to send WhatsApp message:', error);
            throw error;
        }
    }
    async saveWhatsAppStatusUpdate(messageSid, status) {
        if (!messageSid || !status) {
            throw new Error('messageSid and status are required');
        }
        console.log(`Saving WhatsApp status update for message SID ${messageSid}: ${status}`);
        try {
            const message = await this.whatsappMessageRepo.findOne({
                where: { messageSid },
            });
            if (!message) {
                throw new Error(`Message with SID ${messageSid} not found`);
            }
            message.status = status;
            return this.whatsappMessageRepo.save(message);
        }
        catch (error) {
            console.error('Failed to save WhatsApp status update:', error);
            throw error;
        }
    }
    async sendMediaMessage(to, from, mediaUrl) {
        try {
            const response = await this.client.messages.create({
                from: `whatsapp:${from}`,
                to: `whatsapp:${to}`,
                mediaUrl: [mediaUrl],
            });
            return response;
        }
        catch (error) {
            console.error('Failed to send media message:', error);
            throw error;
        }
    }
    async getMessageStatus(messageSid) {
        try {
            const message = await this.client.messages(messageSid).fetch();
            return message.status;
        }
        catch (error) {
            console.error('Failed to fetch message status:', error);
            throw error;
        }
    }
    async getMediaContent(mediaSid) {
        try {
            const mediaList = await this.client.messages(mediaSid).media.list();
            if (!mediaList.length) {
                throw new Error('No media found for this message');
            }
            return mediaList[0].uri;
        }
        catch (error) {
            console.error('Failed to fetch media content:', error);
            throw error;
        }
    }
    async saveSentWhatsAppMessage(result, to, body, mediaUrl, mediaContentType) {
        const msg = this.whatsappMessageRepo.create({
            messageSid: result.sid,
            from: result.from,
            to,
            body,
            mediaUrl,
            mediaContentType,
            status: result.status,
        });
        return this.whatsappMessageRepo.save(msg);
    }
    async saveReceivedWhatsAppMessage(data) {
        const msg = this.whatsappMessageRepo.create({
            messageSid: data.messageSid,
            from: data.from,
            to: data.to,
            body: data.body,
            mediaUrl: data.mediaUrl,
            mediaContentType: data.mediaContentType,
            status: data.status || 'received',
        });
        return this.whatsappMessageRepo.save(msg);
    }
    async sendSMSMessage(to, message) {
        try {
            let formattedTo = to.trim();
            try {
                formattedTo = (0, phone_formatter_util_1.formatToE164)(formattedTo);
                console.log(`📱 TWILIO-SERVICE: SMS phone number formatted to E.164: ${formattedTo}`);
            }
            catch (formatError) {
                console.error(`📱 TWILIO-SERVICE: Failed to format SMS phone number: ${formatError.message}`);
                throw new Error(`Invalid phone number format: ${formatError.message}`);
            }
            const result = await this.client.messages.create({
                body: message,
                from: process.env.TWILIO_PHONE_NUMBER,
                to: formattedTo,
            });
            return result;
        }
        catch (error) {
            console.error('Error sending SMS:', error);
            throw error;
        }
    }
    async saveMessage(from, to, messageData) {
        const message = this.whatsappMessageRepo.create({
            from,
            to,
            messageSid: messageData.messageSid,
            body: messageData.text,
            mediaUrl: messageData.mediaUrl,
            status: messageData.status || 'sent',
            createdAt: messageData.timestamp || new Date(),
        });
        return this.whatsappMessageRepo.save(message);
    }
    async saveSentSMSMessage(result, to, body) {
        const msg = this.whatsappMessageRepo.create({
            messageSid: result.sid,
            from: result.from,
            to,
            body,
            status: result.status,
        });
        return this.whatsappMessageRepo.save(msg);
    }
    async getWhatsAppMessagesByNumber(phoneNumber) {
        const formattedNumber = phoneNumber.startsWith('+')
            ? phoneNumber
            : `+${phoneNumber}`;
        return this.whatsappMessageRepo.find({
            where: [
                { to: (0, typeorm_2.ILike)(`whatsapp:${formattedNumber}`) },
                { to: (0, typeorm_2.ILike)(`%${formattedNumber}`) },
            ],
            order: { createdAt: 'ASC' },
        });
    }
    async sendCombinedWhatsAppAndEmail(payload) {
        const candidates = Array.isArray(payload)
            ? payload
            : Array.isArray(payload.candidates)
                ? payload.candidates
                : [];
        console.log('Sending combined WhatsApp and email messages to candidates:', candidates);
        const results = [];
        await Promise.all(candidates.map(async (candidate) => {
            const { first_name, email, whatsappNumbers, title, location } = candidate;
            const name = first_name;
            const phone = whatsappNumbers;
            const whatsappPromise = this.sendWhatsAppMessage(phone, '', undefined, 'UK', true, { name, title, location }).then(() => ({
                channel: 'whatsapp',
                status: 'sent',
                candidate: name,
            }))
                .catch((err) => ({
                channel: 'whatsapp',
                status: 'failed',
                candidate: name,
                error: err.message,
            }));
            const emailSubject = `${title} – Competitive Salary + Benefits – ${location}`;
            const emailBody = `
        Hi ${name},<br><br>
        Coming across your profile, I thought you are a great match for my client’s <b>${title}</b> in <b>${location}</b>.<br><br>
        The role is paying Competitive Salary (DOE).<br><br>
        Let me know if you think it’s a good fit, I’ll be happy to share your profile across and arrange your interview soon.<br><br>
        Looking forward to your response.
      `;
            const emailPromise = this.emailService.sendEmailWithAttachmentsOld({
                to: [email],
                subject: emailSubject,
                body: emailBody,
                from: process.env.DEFAULT_FROM_EMAIL,
                attachments: [],
            }).then(() => ({
                channel: 'email',
                status: 'sent',
                candidate: name,
            }))
                .catch((err) => ({
                channel: 'email',
                status: 'failed',
                candidate: name,
                error: err.message,
            }));
            const [whatsappResult, emailResult] = await Promise.all([
                whatsappPromise,
                emailPromise,
            ]);
            results.push({
                candidate: name,
                whatsapp: whatsappResult,
                email: emailResult,
            });
        }));
        return { results, summary: this.generateSummary(results) };
    }
    generateSummary(results) {
        const summary = { whatsappSent: 0, whatsappFailed: 0, emailSent: 0, emailFailed: 0 };
        results.forEach((res) => {
            if (res.whatsapp.status === 'sent')
                summary.whatsappSent += 1;
            else
                summary.whatsappFailed += 1;
            if (res.email.status === 'sent')
                summary.emailSent += 1;
            else
                summary.emailFailed += 1;
        });
        return summary;
    }
};
exports.TwillioService = TwillioService;
exports.TwillioService = TwillioService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(whatsapp_message_entity_1.WhatsAppMessage)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        email_service_1.EmailService])
], TwillioService);
//# sourceMappingURL=twillio.service.js.map