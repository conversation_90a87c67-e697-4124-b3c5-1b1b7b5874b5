{"version": 3, "file": "twillio.service.js", "sourceRoot": "", "sources": ["../../src/twillio/twillio.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,mCAAqC;AACrC,6CAAmD;AACnD,qCAA4C;AAC5C,uEAA4D;AAC5D,0DAAuD;AACvD,wEAAqG;AAG9F,IAAM,cAAc,GAApB,MAAM,cAAc;IAKzB,YAEmB,mBAAgD,EAEhD,YAA0B;QAF1B,wBAAmB,GAAnB,mBAAmB,CAA6B;QAEhD,iBAAY,GAAZ,YAAY,CAAc;QAE3C,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CACtB,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAC9B,CAAC;QAGF,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;QACpE,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;IACtE,CAAC;IAED,mBAAmB,CAAC,QAAgB;QAClC,MAAM,WAAW,GAAG,YAAG,CAAC,WAAW,CAAC;QACpC,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QAE1C,MAAM,KAAK,GAAG,IAAI,WAAW,CAC3B,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAC9B,OAAO,CAAC,GAAG,CAAC,cAAc,EAC1B,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAC7B,EAAE,QAAQ,EAAE,CACb,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC;YAChC,sBAAsB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;YACxD,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAE3B,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,EAAU,EACV,OAAe,EACf,QAAiB,EACjB,SAAiB,IAAI,EACrB,UAAU,GAAG,KAAK,EAClB,YAAkC;QAElC,IAAI,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;QAG5B,IAAI,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YACxC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,CAAC;YACH,WAAW,GAAG,IAAA,mCAAY,EAAC,WAAW,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,uDAAuD,WAAW,EAAE,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,WAAW,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,qDAAqD,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1F,MAAM,IAAI,KAAK,CAAC,gCAAgC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;QAGD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,WAAW,GAAG,YAAY,WAAW,EAAE,CAAC;QAC1C,CAAC;QAED,MAAM,IAAI,GACR,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAClE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CACb,8BAA8B,MAAM,4BAA4B,CACjE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,IAAI,QAAa,CAAC;YAClB,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBACrE,CAAC;gBAED,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC3C,IAAI,EAAE,IAAI;oBACV,EAAE,EAAE,WAAW;oBACf,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B;oBACpD,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC;wBAC/B,GAAG,YAAY;qBAChB,CAAC;iBACH,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBAEN,MAAM,OAAO,GAAQ;oBACnB,IAAI,EAAE,IAAI;oBACV,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,OAAO;iBACd,CAAC;gBAEF,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC9B,CAAC;gBAED,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,UAAkB,EAAE,MAAc;QAC/D,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,CAAC,GAAG,CACT,iDAAiD,UAAU,KAAK,MAAM,EAAE,CACzE,CAAC;QACF,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACrD,KAAK,EAAE,EAAE,UAAU,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,oBAAoB,UAAU,YAAY,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU,EAAE,IAAY,EAAE,QAAgB;QAC/D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACjD,IAAI,EAAE,YAAY,IAAI,EAAE;gBACxB,EAAE,EAAE,YAAY,EAAE,EAAE;gBACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;YAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YACpE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,MAAW,EACX,EAAU,EACV,IAAY,EACZ,QAAiB,EACjB,gBAAyB;QAEzB,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC1C,UAAU,EAAE,MAAM,CAAC,GAAG;YACtB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,EAAE;YACF,IAAI;YACJ,QAAQ;YACR,gBAAgB;YAChB,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,IAQjC;QACC,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC1C,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,UAAU;SAClC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,OAAe;QAC9C,IAAI,CAAC;YAEH,IAAI,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,WAAW,GAAG,IAAA,mCAAY,EAAC,WAAW,CAAC,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,2DAA2D,WAAW,EAAE,CAAC,CAAC;YACxF,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,yDAAyD,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9F,MAAM,IAAI,KAAK,CAAC,gCAAgC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC/C,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;gBACrC,EAAE,EAAE,WAAW;aAChB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CACf,IAAY,EACZ,EAAU,EACV,WAOC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC9C,IAAI;YACJ,EAAE;YACF,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,MAAM;YACpC,SAAS,EAAE,WAAW,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;SAC/C,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAW,EAAE,EAAU,EAAE,IAAY;QAC5D,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC1C,UAAU,EAAE,MAAM,CAAC,GAAG;YACtB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,EAAE;YACF,IAAI;YACJ,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,2BAA2B,CAC/B,WAAmB;QAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;YACjD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YACnC,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,YAAY,eAAe,EAAE,CAAC,EAAE;gBAC5C,EAAE,EAAE,EAAE,IAAA,eAAK,EAAC,IAAI,eAAe,EAAE,CAAC,EAAE;aACrC;YACD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;SAC5B,CAAC,CAAC;IACL,CAAC;IAIH,KAAK,CAAC,4BAA4B,CAAC,OAAY;QAE7C,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;YACvC,CAAC,CAAC,OAAO;YACT,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;gBACjC,CAAC,CAAC,OAAO,CAAC,UAAU;gBACpB,CAAC,CAAC,EAAE,CAAC;QAET,OAAO,CAAC,GAAG,CAAC,6DAA6D,EAAE,UAAU,CAAC,CAAC;QAEvF,MAAM,OAAO,GAAG,EAAE,CAAC;QAuBnB,MAAM,OAAO,CAAC,GAAG,CACd,UAA0B,CAAC,GAAG,CAAC,KAAK,EAAE,SAAoB,EAAE,EAAE;YAC7D,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;YAC1E,MAAM,IAAI,GAAG,UAAU,CAAC;YACxB,MAAM,KAAK,GAAG,eAAe,CAAC;YAE9B,MAAM,eAAe,GAA2B,IAAI,CAAC,mBAAmB,CACtE,KAAK,EACL,EAAE,EACF,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,CAC1B,CAAC,IAAI,CAAC,GAAkB,EAAE,CAAC,CAAC;gBAC3B,OAAO,EAAE,UAAmB;gBAC5B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;iBACA,KAAK,CAAC,CAAC,GAAU,EAAiB,EAAE,CAAC,CAAC;gBACrC,OAAO,EAAE,UAAU;gBACnB,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,GAAG,CAAC,OAAO;aACnB,CAAC,CAAC,CAAC;YAEN,MAAM,YAAY,GAAG,GAAG,KAAK,sCAAsC,QAAQ,EAAE,CAAC;YAC9E,MAAM,SAAS,GAAG;aACX,IAAI;yFACwE,KAAK,cAAc,QAAQ;;;;OAI7G,CAAC;YAEF,MAAM,YAAY,GAA2B,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC;gBACzF,EAAE,EAAE,CAAC,KAAK,CAAC;gBACX,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;gBACpC,WAAW,EAAE,EAAE;aAChB,CAAC,CAAC,IAAI,CAAC,GAAkB,EAAE,CAAC,CAAC;gBAC5B,OAAO,EAAE,OAAgB;gBACzB,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAI;aAChB,CAAC,CAAC;iBACA,KAAK,CAAC,CAAC,GAAU,EAAiB,EAAE,CAAC,CAAC;gBACrC,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI;gBACf,KAAK,EAAE,GAAG,CAAC,OAAO;aACnB,CAAC,CAAC,CAAC;YAEN,MAAM,CAAC,cAAc,EAAE,WAAW,CAAC,GAAmC,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtF,eAAe;gBACf,YAAY;aACb,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,WAAW;aACD,CAAC,CAAC;QACvB,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;IAC7D,CAAC;IAGS,eAAe,CAAC,OAAc;QACpC,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;QAErF,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACtB,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,MAAM;gBAAE,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;;gBACzD,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC;YAEjC,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM;gBAAE,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;;gBACnD,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;CAEF,CAAA;AA3ZY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCACI,oBAAU;QAEjB,4BAAY;GATlC,cAAc,CA2Z1B"}