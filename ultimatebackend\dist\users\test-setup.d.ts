import 'reflect-metadata';
declare global {
    namespace jest {
        interface Matchers<R> {
            toBeValidEmail(): R;
            toBeValidUUID(): R;
            toBeValidJWT(): R;
        }
    }
    var testUtils: {
        createMockUser: () => any;
        createMockCreateUserDto: () => any;
        createMockLoginDto: () => any;
        createMockVerifyDto: () => any;
        createMockUpdateUserDto: () => any;
    };
}
