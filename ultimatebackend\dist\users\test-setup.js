"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
beforeAll(() => {
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.JWT_EXPIRATION = '1h';
    process.env.DEFAULT_FROM_EMAIL = '<EMAIL>';
});
afterAll(() => {
});
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
global.testUtils = {
    createMockUser: () => ({
        id: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        full_name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        password_hash: '$2b$10$hashedpassword',
        password_salt: 'salt123',
        role: 'USER',
        designation: 'RECRUITER',
        status: 'ACTIVE',
        email_verified: false,
        created_at: new Date(),
        updated_at: new Date(),
    }),
    createMockCreateUserDto: () => ({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Jane',
        last_name: 'Smith',
        username: 'janesmith',
        source: 'CRM',
        role: 'USER',
        designation: 'RECRUITER',
        profile_picture: 'https://example.com/profile.jpg',
        status: 'ACTIVE',
    }),
    createMockLoginDto: () => ({
        email: '<EMAIL>',
        password: 'password123',
        source: 'crm',
        designation: 'RECRUITER',
    }),
    createMockVerifyDto: () => ({
        email: '<EMAIL>',
        verification_code: '123456',
    }),
    createMockUpdateUserDto: () => ({
        first_name: 'Updated',
        last_name: 'Name',
        phone_number: '+1234567890',
    }),
};
expect.extend({
    toBeValidEmail(received) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const pass = emailRegex.test(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid email`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid email`,
                pass: false,
            };
        }
    },
    toBeValidUUID(received) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        const pass = uuidRegex.test(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid UUID`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid UUID`,
                pass: false,
            };
        }
    },
    toBeValidJWT(received) {
        const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
        const pass = jwtRegex.test(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid JWT`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${received} to be a valid JWT`,
                pass: false,
            };
        }
    },
});
//# sourceMappingURL=test-setup.js.map