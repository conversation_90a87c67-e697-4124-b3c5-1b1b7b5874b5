"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatToE164 = formatToE164;
exports.isValidE164 = isValidE164;
exports.formatForWhatsApp = formatForWhatsApp;
exports.extractFromWhatsApp = extractFromWhatsApp;
exports.normalizePhoneNumber = normalizePhoneNumber;
exports.getCountryCode = getCountryCode;
exports.formatForDisplay = formatForDisplay;
function formatToE164(phoneNumber, defaultCountryCode = '+44') {
    if (!phoneNumber) {
        throw new Error('Phone number is required');
    }
    let cleaned = phoneNumber.replace(/[^\d+]/g, '');
    if (cleaned.startsWith('+')) {
        if (cleaned.length < 10) {
            throw new Error('Phone number too short');
        }
        return cleaned;
    }
    if (cleaned.startsWith('00')) {
        const formatted = '+' + cleaned.substring(2);
        if (formatted.length < 10) {
            throw new Error('Phone number too short');
        }
        return formatted;
    }
    if (cleaned.startsWith('0')) {
        const formatted = '+44' + cleaned.substring(1);
        if (formatted.length < 12) {
            throw new Error('UK phone number too short');
        }
        return formatted;
    }
    if (/^\d+$/.test(cleaned)) {
        const knownCountryCodes = [
            { code: '1', length: [11, 12] },
            { code: '7', length: [11] },
            { code: '20', length: [11, 12] },
            { code: '27', length: [11] },
            { code: '30', length: [12] },
            { code: '31', length: [11] },
            { code: '32', length: [11] },
            { code: '33', length: [11] },
            { code: '34', length: [11] },
            { code: '36', length: [11] },
            { code: '39', length: [11, 12] },
            { code: '40', length: [11] },
            { code: '41', length: [11] },
            { code: '43', length: [11, 12] },
            { code: '44', length: [12, 13] },
            { code: '45', length: [10] },
            { code: '46', length: [11] },
            { code: '47', length: [10] },
            { code: '48', length: [11] },
            { code: '49', length: [11, 12] },
            { code: '51', length: [11] },
            { code: '52', length: [12, 13] },
            { code: '53', length: [10] },
            { code: '54', length: [12, 13] },
            { code: '55', length: [12, 13] },
            { code: '56', length: [11] },
            { code: '57', length: [12] },
            { code: '58', length: [12] },
            { code: '60', length: [11, 12] },
            { code: '61', length: [11] },
            { code: '62', length: [11, 12, 13] },
            { code: '63', length: [12] },
            { code: '64', length: [10, 11] },
            { code: '65', length: [10] },
            { code: '66', length: [11] },
            { code: '81', length: [12] },
            { code: '82', length: [11, 12] },
            { code: '84', length: [11, 12] },
            { code: '86', length: [13] },
            { code: '90', length: [12] },
            { code: '91', length: [12, 13] },
            { code: '92', length: [12, 13] },
            { code: '93', length: [11] },
            { code: '94', length: [11] },
            { code: '95', length: [10, 11] },
            { code: '98', length: [12] },
        ];
        for (const country of knownCountryCodes.sort((a, b) => b.code.length - a.code.length)) {
            if (cleaned.startsWith(country.code)) {
                const formatted = '+' + cleaned;
                if (country.length.includes(formatted.length)) {
                    console.log(`📞 FORMATTER: Detected country code ${country.code} for number ${cleaned} → ${formatted}`);
                    return formatted;
                }
            }
        }
        if (cleaned.startsWith('7') && cleaned.length === 11) {
            return '+44' + cleaned;
        }
        const formatted = defaultCountryCode + cleaned;
        if (formatted.length < 10) {
            throw new Error('Phone number too short');
        }
        return formatted;
    }
    const formatted = defaultCountryCode + cleaned;
    if (formatted.length < 10) {
        throw new Error('Invalid phone number format');
    }
    return formatted;
}
function isValidE164(phoneNumber) {
    if (!phoneNumber)
        return false;
    const e164Regex = /^\+[1-9]\d{6,14}$/;
    return e164Regex.test(phoneNumber);
}
function formatForWhatsApp(phoneNumber, defaultCountryCode = '+44') {
    let cleaned = phoneNumber.replace(/^whatsapp:/, '');
    const e164Number = formatToE164(cleaned, defaultCountryCode);
    return `whatsapp:${e164Number}`;
}
function extractFromWhatsApp(whatsappNumber) {
    return whatsappNumber.replace(/^whatsapp:/, '');
}
function normalizePhoneNumber(phoneNumber) {
    if (!phoneNumber)
        return '';
    return phoneNumber
        .replace(/^whatsapp:/, '')
        .replace(/^sms:/, '')
        .trim();
}
function getCountryCode(phoneNumber) {
    if (!phoneNumber.startsWith('+')) {
        throw new Error('Phone number must be in E.164 format');
    }
    const countryCodes = ['+1', '+7', '+20', '+27', '+30', '+31', '+32', '+33', '+34', '+36', '+39', '+40', '+41', '+43', '+44', '+45', '+46', '+47', '+48', '+49', '+51', '+52', '+53', '+54', '+55', '+56', '+57', '+58', '+60', '+61', '+62', '+63', '+64', '+65', '+66', '+81', '+82', '+84', '+86', '+90', '+91', '+92', '+93', '+94', '+95', '+98', '+212', '+213', '+216', '+218', '+220', '+221', '+222', '+223', '+224', '+225', '+226', '+227', '+228', '+229', '+230', '+231', '+232', '+233', '+234', '+235', '+236', '+237', '+238', '+239', '+240', '+241', '+242', '+243', '+244', '+245', '+246', '+248', '+249', '+250', '+251', '+252', '+253', '+254', '+255', '+256', '+257', '+258', '+260', '+261', '+262', '+263', '+264', '+265', '+266', '+267', '+268', '+269', '+290', '+291', '+297', '+298', '+299', '+350', '+351', '+352', '+353', '+354', '+355', '+356', '+357', '+358', '+359', '+370', '+371', '+372', '+373', '+374', '+375', '+376', '+377', '+378', '+380', '+381', '+382', '+383', '+385', '+386', '+387', '+389', '+420', '+421', '+423', '+500', '+501', '+502', '+503', '+504', '+505', '+506', '+507', '+508', '+509', '+590', '+591', '+592', '+593', '+594', '+595', '+596', '+597', '+598', '+599', '+670', '+672', '+673', '+674', '+675', '+676', '+677', '+678', '+679', '+680', '+681', '+682', '+683', '+684', '+685', '+686', '+687', '+688', '+689', '+690', '+691', '+692', '+850', '+852', '+853', '+855', '+856', '+880', '+886', '+960', '+961', '+962', '+963', '+964', '+965', '+966', '+967', '+968', '+970', '+971', '+972', '+973', '+974', '+975', '+976', '+977', '+992', '+993', '+994', '+995', '+996', '+998'];
    for (const code of countryCodes.sort((a, b) => b.length - a.length)) {
        if (phoneNumber.startsWith(code)) {
            return code;
        }
    }
    return phoneNumber.substring(0, 2);
}
function formatForDisplay(phoneNumber) {
    if (!phoneNumber.startsWith('+')) {
        return phoneNumber;
    }
    const countryCode = getCountryCode(phoneNumber);
    const number = phoneNumber.substring(countryCode.length);
    if (countryCode === '+44') {
        if (number.length === 10) {
            return `${countryCode} ${number.substring(0, 4)} ${number.substring(4, 7)} ${number.substring(7)}`;
        }
    }
    else if (countryCode === '+1') {
        if (number.length === 10) {
            return `${countryCode} (${number.substring(0, 3)}) ${number.substring(3, 6)}-${number.substring(6)}`;
        }
    }
    if (number.length >= 8) {
        const firstPart = number.substring(0, 4);
        const secondPart = number.substring(4);
        return `${countryCode} ${firstPart} ${secondPart}`;
    }
    return phoneNumber;
}
//# sourceMappingURL=phone-formatter.util.js.map