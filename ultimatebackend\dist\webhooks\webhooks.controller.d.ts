import { WebhooksService } from './webhooks.service';
export interface EmailWebhookPayload {
    candidateId: number;
    stepId: number;
    event: 'delivered' | 'opened' | 'clicked' | 'replied' | 'bounced' | 'spam';
    timestamp: string;
    messageId?: string;
    recipientEmail?: string;
    responseData?: any;
}
export interface WhatsAppWebhookPayload {
    candidateId: number;
    stepId: number;
    event: 'delivered' | 'read' | 'replied';
    timestamp: string;
    messageId?: string;
    recipientPhone?: string;
    responseData?: any;
}
export interface SmsWebhookPayload {
    candidateId: number;
    stepId: number;
    event: 'delivered' | 'replied' | 'failed';
    timestamp: string;
    messageId?: string;
    recipientPhone?: string;
    responseData?: any;
}
export interface CallWebhookPayload {
    candidateId: number;
    stepId: number;
    event: 'answered' | 'no_answer' | 'busy' | 'failed';
    timestamp: string;
    callId?: string;
    recipientPhone?: string;
    callDuration?: number;
    responseData?: any;
}
export interface LinkedInWebhookPayload {
    candidateId: number;
    stepId: number;
    event: 'delivered' | 'viewed' | 'replied' | 'connection_accepted';
    timestamp: string;
    messageId?: string;
    recipientProfile?: string;
    responseData?: any;
}
export declare class WebhooksController {
    private readonly webhooksService;
    private readonly logger;
    constructor(webhooksService: WebhooksService);
    handleEmailWebhook(payload: EmailWebhookPayload): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    handleWhatsAppWebhook(payload: WhatsAppWebhookPayload): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    handleSmsWebhook(payload: SmsWebhookPayload): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    handleCallWebhook(payload: CallWebhookPayload): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    handleLinkedInWebhook(payload: LinkedInWebhookPayload): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    testWebhook(candidateId: number, stepId: number, payload: {
        event: string;
        medium: string;
    }): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
}
