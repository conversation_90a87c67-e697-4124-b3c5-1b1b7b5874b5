"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WebhooksController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhooksController = void 0;
const common_1 = require("@nestjs/common");
const webhooks_service_1 = require("./webhooks.service");
let WebhooksController = WebhooksController_1 = class WebhooksController {
    constructor(webhooksService) {
        this.webhooksService = webhooksService;
        this.logger = new common_1.Logger(WebhooksController_1.name);
    }
    async handleEmailWebhook(payload) {
        this.logger.log(`Received email webhook: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processEmailWebhook(payload);
            return { success: true, message: 'Email webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process email webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async handleWhatsAppWebhook(payload) {
        this.logger.log(`Received WhatsApp webhook: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processWhatsAppWebhook(payload);
            return { success: true, message: 'WhatsApp webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process WhatsApp webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async handleSmsWebhook(payload) {
        this.logger.log(`Received SMS webhook: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processSmsWebhook(payload);
            return { success: true, message: 'SMS webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process SMS webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async handleCallWebhook(payload) {
        this.logger.log(`Received call webhook: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processCallWebhook(payload);
            return { success: true, message: 'Call webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process call webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async handleLinkedInWebhook(payload) {
        this.logger.log(`Received LinkedIn webhook: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processLinkedInWebhook(payload);
            return { success: true, message: 'LinkedIn webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process LinkedIn webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    async testWebhook(candidateId, stepId, payload) {
        this.logger.log(`Test webhook for candidate ${candidateId}, step ${stepId}: ${JSON.stringify(payload)}`);
        try {
            await this.webhooksService.processTestWebhook(candidateId, stepId, payload.event, payload.medium);
            return { success: true, message: 'Test webhook processed successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to process test webhook: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
};
exports.WebhooksController = WebhooksController;
__decorate([
    (0, common_1.Post)('email'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleEmailWebhook", null);
__decorate([
    (0, common_1.Post)('whatsapp'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleWhatsAppWebhook", null);
__decorate([
    (0, common_1.Post)('sms'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleSmsWebhook", null);
__decorate([
    (0, common_1.Post)('call'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleCallWebhook", null);
__decorate([
    (0, common_1.Post)('linkedin'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "handleLinkedInWebhook", null);
__decorate([
    (0, common_1.Post)('test/:candidateId/:stepId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('candidateId')),
    __param(1, (0, common_1.Param)('stepId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], WebhooksController.prototype, "testWebhook", null);
exports.WebhooksController = WebhooksController = WebhooksController_1 = __decorate([
    (0, common_1.Controller)('webhooks'),
    __metadata("design:paramtypes", [webhooks_service_1.WebhooksService])
], WebhooksController);
//# sourceMappingURL=webhooks.controller.js.map