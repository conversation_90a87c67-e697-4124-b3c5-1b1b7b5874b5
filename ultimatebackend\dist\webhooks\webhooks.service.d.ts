import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceService } from 'src/sequence/sequence.service';
import { EmailWebhookPayload, WhatsAppWebhookPayload, SmsWebhookPayload, CallWebhookPayload, LinkedInWebhookPayload } from './webhooks.controller';
export declare class WebhooksService {
    private readonly candidateSequenceStatusService;
    private readonly sequenceService;
    private readonly logger;
    constructor(candidateSequenceStatusService: CandidateSequenceStatusService, sequenceService: SequenceService);
    processEmailWebhook(payload: EmailWebhookPayload): Promise<void>;
    processWhatsAppWebhook(payload: WhatsAppWebhookPayload): Promise<void>;
    processSmsWebhook(payload: SmsWebhookPayload): Promise<void>;
    processCallWebhook(payload: CallWebhookPayload): Promise<void>;
    processLinkedInWebhook(payload: LinkedInWebhookPayload): Promise<void>;
    processTestWebhook(candidateId: number, stepId: number, event: string, medium: string): Promise<void>;
}
