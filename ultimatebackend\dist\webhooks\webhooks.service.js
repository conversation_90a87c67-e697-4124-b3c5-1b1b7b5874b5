"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WebhooksService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhooksService = void 0;
const common_1 = require("@nestjs/common");
const candidate_sequence_status_service_1 = require("../candidate-sequence-status/candidate-sequence-status.service");
const sequence_service_1 = require("../sequence/sequence.service");
const candidate_sequence_status_entity_1 = require("../candidate-sequence-status/candidate-sequence-status.entity");
let WebhooksService = WebhooksService_1 = class WebhooksService {
    constructor(candidateSequenceStatusService, sequenceService) {
        this.candidateSequenceStatusService = candidateSequenceStatusService;
        this.sequenceService = sequenceService;
        this.logger = new common_1.Logger(WebhooksService_1.name);
    }
    async processEmailWebhook(payload) {
        const { candidateId, stepId, event, responseData } = payload;
        try {
            const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, 0);
            const status = statuses.find(s => s.stepId === stepId);
            if (!status) {
                throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
            }
            let newStatus;
            switch (event) {
                case 'delivered':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED;
                    break;
                case 'opened':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.OPENED;
                    break;
                case 'clicked':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.CLICKED;
                    break;
                case 'replied':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.REPLIED;
                    await this.candidateSequenceStatusService.markStepCompleted(candidateId, stepId, JSON.stringify(responseData));
                    await this.sequenceService.processNextSteps(candidateId, stepId);
                    return;
                case 'bounced':
                case 'spam':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.FAILED;
                    break;
                default:
                    this.logger.warn(`Unknown email event: ${event}`);
                    return;
            }
            await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
                webhookEvent: event,
                webhookTimestamp: payload.timestamp,
                webhookData: responseData,
            });
            this.logger.log(`Email webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
        }
        catch (error) {
            this.logger.error(`Failed to process email webhook: ${error.message}`);
            throw error;
        }
    }
    async processWhatsAppWebhook(payload) {
        const { candidateId, stepId, event, responseData } = payload;
        try {
            const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, 0);
            const status = statuses.find(s => s.stepId === stepId);
            if (!status) {
                throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
            }
            let newStatus;
            switch (event) {
                case 'delivered':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED;
                    break;
                case 'read':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.OPENED;
                    break;
                case 'replied':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.REPLIED;
                    await this.candidateSequenceStatusService.markStepCompleted(candidateId, stepId, JSON.stringify(responseData));
                    await this.sequenceService.processNextSteps(candidateId, stepId);
                    return;
                default:
                    this.logger.warn(`Unknown WhatsApp event: ${event}`);
                    return;
            }
            await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
                webhookEvent: event,
                webhookTimestamp: payload.timestamp,
                webhookData: responseData,
            });
            this.logger.log(`WhatsApp webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
        }
        catch (error) {
            this.logger.error(`Failed to process WhatsApp webhook: ${error.message}`);
            throw error;
        }
    }
    async processSmsWebhook(payload) {
        const { candidateId, stepId, event, responseData } = payload;
        try {
            const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, 0);
            const status = statuses.find(s => s.stepId === stepId);
            if (!status) {
                throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
            }
            let newStatus;
            switch (event) {
                case 'delivered':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED;
                    break;
                case 'replied':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.REPLIED;
                    await this.candidateSequenceStatusService.markStepCompleted(candidateId, stepId, JSON.stringify(responseData));
                    await this.sequenceService.processNextSteps(candidateId, stepId);
                    return;
                case 'failed':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.FAILED;
                    break;
                default:
                    this.logger.warn(`Unknown SMS event: ${event}`);
                    return;
            }
            await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
                webhookEvent: event,
                webhookTimestamp: payload.timestamp,
                webhookData: responseData,
            });
            this.logger.log(`SMS webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
        }
        catch (error) {
            this.logger.error(`Failed to process SMS webhook: ${error.message}`);
            throw error;
        }
    }
    async processCallWebhook(payload) {
        const { candidateId, stepId, event, responseData } = payload;
        try {
            const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, 0);
            const status = statuses.find(s => s.stepId === stepId);
            if (!status) {
                throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
            }
            let newStatus;
            switch (event) {
                case 'answered':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.COMPLETED;
                    await this.candidateSequenceStatusService.markStepCompleted(candidateId, stepId, JSON.stringify(responseData));
                    await this.sequenceService.processNextSteps(candidateId, stepId);
                    return;
                case 'no_answer':
                case 'busy':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED;
                    break;
                case 'failed':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.FAILED;
                    break;
                default:
                    this.logger.warn(`Unknown call event: ${event}`);
                    return;
            }
            await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
                webhookEvent: event,
                webhookTimestamp: payload.timestamp,
                webhookData: responseData,
            });
            this.logger.log(`Call webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
        }
        catch (error) {
            this.logger.error(`Failed to process call webhook: ${error.message}`);
            throw error;
        }
    }
    async processLinkedInWebhook(payload) {
        const { candidateId, stepId, event, responseData } = payload;
        try {
            const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(candidateId, 0);
            const status = statuses.find(s => s.stepId === stepId);
            if (!status) {
                throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
            }
            let newStatus;
            switch (event) {
                case 'delivered':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.DELIVERED;
                    break;
                case 'viewed':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.OPENED;
                    break;
                case 'replied':
                case 'connection_accepted':
                    newStatus = candidate_sequence_status_entity_1.SequenceStepStatus.REPLIED;
                    await this.candidateSequenceStatusService.markStepCompleted(candidateId, stepId, JSON.stringify(responseData));
                    await this.sequenceService.processNextSteps(candidateId, stepId);
                    return;
                default:
                    this.logger.warn(`Unknown LinkedIn event: ${event}`);
                    return;
            }
            await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
                webhookEvent: event,
                webhookTimestamp: payload.timestamp,
                webhookData: responseData,
            });
            this.logger.log(`LinkedIn webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
        }
        catch (error) {
            this.logger.error(`Failed to process LinkedIn webhook: ${error.message}`);
            throw error;
        }
    }
    async processTestWebhook(candidateId, stepId, event, medium) {
        this.logger.log(`Processing test webhook: candidate ${candidateId}, step ${stepId}, event ${event}, medium ${medium}`);
        const testPayload = {
            candidateId,
            stepId,
            event,
            timestamp: new Date().toISOString(),
            responseData: { test: true, medium, event },
        };
        switch (medium.toLowerCase()) {
            case 'email':
                await this.processEmailWebhook(testPayload);
                break;
            case 'whatsapp':
                await this.processWhatsAppWebhook(testPayload);
                break;
            case 'sms':
                await this.processSmsWebhook(testPayload);
                break;
            case 'call':
                await this.processCallWebhook(testPayload);
                break;
            case 'linkedin':
                await this.processLinkedInWebhook(testPayload);
                break;
            default:
                throw new Error(`Unsupported medium for test webhook: ${medium}`);
        }
    }
};
exports.WebhooksService = WebhooksService;
exports.WebhooksService = WebhooksService = WebhooksService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [candidate_sequence_status_service_1.CandidateSequenceStatusService,
        sequence_service_1.SequenceService])
], WebhooksService);
//# sourceMappingURL=webhooks.service.js.map