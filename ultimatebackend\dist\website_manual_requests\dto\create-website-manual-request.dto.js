"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateWebsiteManualRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const website_manual_requests_entity_1 = require("../website_manual_requests.entity");
class CreateWebsiteManualRequestDto {
}
exports.CreateWebsiteManualRequestDto = CreateWebsiteManualRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'John',
        description: 'First name of the person making the request',
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "first_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Doe',
        description: 'Last name of the person making the request',
        maxLength: 100,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "last_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Software Engineer',
        description: 'Job title of the person',
        maxLength: 200,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Tech Corp Ltd',
        description: 'Company name',
        maxLength: 200,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "company", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '<EMAIL>',
        description: 'Email address of the person',
    }),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'BD Manual',
        description: 'Type of manual interest',
        enum: website_manual_requests_entity_1.ManualInterestType,
    }),
    (0, class_validator_1.IsEnum)(website_manual_requests_entity_1.ManualInterestType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "manual_interest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'https://bucket.s3.amazonaws.com/path/to/file.pdf',
        description: 'S3 bucket URL for the file',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "s3_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'https://example.com/file.pdf',
        description: 'Direct file URL for download',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateWebsiteManualRequestDto.prototype, "file_url", void 0);
//# sourceMappingURL=create-website-manual-request.dto.js.map