"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteManualRequestResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const website_manual_requests_entity_1 = require("../website_manual_requests.entity");
class WebsiteManualRequestResponseDto {
}
exports.WebsiteManualRequestResponseDto = WebsiteManualRequestResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'uuid-string',
        description: 'Unique identifier for the manual request',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'John',
        description: 'First name of the person',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "first_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Doe',
        description: 'Last name of the person',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "last_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Software Engineer',
        description: 'Job title of the person',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Tech Corp Ltd',
        description: 'Company name',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "company", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '<EMAIL>',
        description: 'Email address of the person',
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'BD Manual',
        description: 'Type of manual interest',
        enum: website_manual_requests_entity_1.ManualInterestType,
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "manual_interest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'https://bucket.s3.amazonaws.com/path/to/file.pdf',
        description: 'S3 bucket URL for the file',
        required: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "s3_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'https://example.com/file.pdf',
        description: 'Direct file URL for download',
        required: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "file_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: 'Whether the email has been sent',
    }),
    __metadata("design:type", Boolean)
], WebsiteManualRequestResponseDto.prototype, "email_sent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2024-01-01T00:00:00Z',
        description: 'When the email was sent',
        required: false,
    }),
    __metadata("design:type", Date)
], WebsiteManualRequestResponseDto.prototype, "email_sent_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Email sending failed: Invalid recipient',
        description: 'Error message if email sending failed',
        required: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequestResponseDto.prototype, "email_error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2024-01-01T00:00:00Z',
        description: 'When the request was created',
    }),
    __metadata("design:type", Date)
], WebsiteManualRequestResponseDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2024-01-01T00:00:00Z',
        description: 'When the request was last updated',
    }),
    __metadata("design:type", Date)
], WebsiteManualRequestResponseDto.prototype, "updated_at", void 0);
//# sourceMappingURL=website-manual-request-response.dto.js.map