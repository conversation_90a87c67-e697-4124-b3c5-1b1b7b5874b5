import { WebsiteManualRequestsService } from './website_manual_requests.service';
import { CreateWebsiteManualRequestDto } from './dto/create-website-manual-request.dto';
import { WebsiteManualRequest } from './website_manual_requests.entity';
export declare class WebsiteManualRequestsController {
    private readonly websiteManualRequestsService;
    constructor(websiteManualRequestsService: WebsiteManualRequestsService);
    createManualRequest(createDto: CreateWebsiteManualRequestDto): Promise<WebsiteManualRequest>;
    getAllManualRequests(): Promise<WebsiteManualRequest[]>;
    getManualRequestById(id: string): Promise<WebsiteManualRequest>;
    resendEmail(id: string): Promise<WebsiteManualRequest>;
    getFailedEmailRequests(): Promise<WebsiteManualRequest[]>;
}
