"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteManualRequestsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const website_manual_requests_service_1 = require("./website_manual_requests.service");
const create_website_manual_request_dto_1 = require("./dto/create-website-manual-request.dto");
const website_manual_request_response_dto_1 = require("./dto/website-manual-request-response.dto");
let WebsiteManualRequestsController = class WebsiteManualRequestsController {
    constructor(websiteManualRequestsService) {
        this.websiteManualRequestsService = websiteManualRequestsService;
    }
    async createManualRequest(createDto) {
        try {
            return await this.websiteManualRequestsService.createManualRequest(createDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to create manual request', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllManualRequests() {
        try {
            return await this.websiteManualRequestsService.getAllManualRequests();
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch manual requests', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getManualRequestById(id) {
        try {
            return await this.websiteManualRequestsService.getManualRequestById(id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch manual request', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async resendEmail(id) {
        try {
            return await this.websiteManualRequestsService.resendEmail(id);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to resend email', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getFailedEmailRequests() {
        try {
            return await this.websiteManualRequestsService.getFailedEmailRequests();
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Failed to fetch failed email requests', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.WebsiteManualRequestsController = WebsiteManualRequestsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new website manual request',
        description: 'Creates a new manual request and automatically sends an email with file attachments if URLs are provided'
    }),
    (0, swagger_1.ApiBody)({ type: create_website_manual_request_dto_1.CreateWebsiteManualRequestDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Manual request created successfully',
        type: website_manual_request_response_dto_1.WebsiteManualRequestResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid input data',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_website_manual_request_dto_1.CreateWebsiteManualRequestDto]),
    __metadata("design:returntype", Promise)
], WebsiteManualRequestsController.prototype, "createManualRequest", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all website manual requests',
        description: 'Retrieves all manual requests ordered by creation date (newest first)'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Manual requests retrieved successfully',
        type: [website_manual_request_response_dto_1.WebsiteManualRequestResponseDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WebsiteManualRequestsController.prototype, "getAllManualRequests", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a website manual request by ID',
        description: 'Retrieves a specific manual request by its UUID'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'UUID of the manual request',
        type: 'string',
        format: 'uuid',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Manual request retrieved successfully',
        type: website_manual_request_response_dto_1.WebsiteManualRequestResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Manual request not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WebsiteManualRequestsController.prototype, "getManualRequestById", null);
__decorate([
    (0, common_1.Patch)(':id/resend-email'),
    (0, swagger_1.ApiOperation)({
        summary: 'Resend email for a manual request',
        description: 'Resends the email with file attachments for a specific manual request'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'UUID of the manual request',
        type: 'string',
        format: 'uuid',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email resent successfully',
        type: website_manual_request_response_dto_1.WebsiteManualRequestResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Manual request not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Failed to resend email',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WebsiteManualRequestsController.prototype, "resendEmail", null);
__decorate([
    (0, common_1.Get)('failed/emails'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get manual requests with failed emails',
        description: 'Retrieves all manual requests where email sending failed'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Failed email requests retrieved successfully',
        type: [website_manual_request_response_dto_1.WebsiteManualRequestResponseDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WebsiteManualRequestsController.prototype, "getFailedEmailRequests", null);
exports.WebsiteManualRequestsController = WebsiteManualRequestsController = __decorate([
    (0, swagger_1.ApiTags)('Website Manual Requests'),
    (0, common_1.Controller)('website-manual-requests'),
    __metadata("design:paramtypes", [website_manual_requests_service_1.WebsiteManualRequestsService])
], WebsiteManualRequestsController);
//# sourceMappingURL=website_manual_requests.controller.js.map