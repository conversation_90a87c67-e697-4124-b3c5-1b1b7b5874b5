"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteManualRequest = exports.ManualInterestType = void 0;
const typeorm_1 = require("typeorm");
var ManualInterestType;
(function (ManualInterestType) {
    ManualInterestType["BD_MANUAL"] = "BD Manual";
    ManualInterestType["RECRUITMENT_MANUAL"] = "Recruitment Manual";
})(ManualInterestType || (exports.ManualInterestType = ManualInterestType = {}));
let WebsiteManualRequest = class WebsiteManualRequest {
};
exports.WebsiteManualRequest = WebsiteManualRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "first_name", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "last_name", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 200,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "job_title", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 200,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ManualInterestType,
        nullable: false,
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "manual_interest", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'S3 bucket URL for the file',
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "s3_url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'Direct file URL for download',
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "file_url", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'boolean',
        default: false,
        comment: 'Whether the email has been sent',
    }),
    __metadata("design:type", Boolean)
], WebsiteManualRequest.prototype, "email_sent", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        comment: 'When the email was sent',
    }),
    __metadata("design:type", Date)
], WebsiteManualRequest.prototype, "email_sent_at", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'Error message if email sending failed',
    }),
    __metadata("design:type", String)
], WebsiteManualRequest.prototype, "email_error", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], WebsiteManualRequest.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], WebsiteManualRequest.prototype, "updated_at", void 0);
exports.WebsiteManualRequest = WebsiteManualRequest = __decorate([
    (0, typeorm_1.Entity)('website_manual_requests')
], WebsiteManualRequest);
//# sourceMappingURL=website_manual_requests.entity.js.map