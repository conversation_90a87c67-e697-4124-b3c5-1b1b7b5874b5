"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteManualRequestsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const website_manual_requests_service_1 = require("./website_manual_requests.service");
const website_manual_requests_controller_1 = require("./website_manual_requests.controller");
const website_manual_requests_entity_1 = require("./website_manual_requests.entity");
const email_module_1 = require("../email/email.module");
const s3bucket_module_1 = require("../s3bucket/s3bucket.module");
let WebsiteManualRequestsModule = class WebsiteManualRequestsModule {
};
exports.WebsiteManualRequestsModule = WebsiteManualRequestsModule;
exports.WebsiteManualRequestsModule = WebsiteManualRequestsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([website_manual_requests_entity_1.WebsiteManualRequest]),
            email_module_1.EmailModule,
            s3bucket_module_1.S3bucketModule,
        ],
        providers: [website_manual_requests_service_1.WebsiteManualRequestsService],
        controllers: [website_manual_requests_controller_1.WebsiteManualRequestsController],
        exports: [website_manual_requests_service_1.WebsiteManualRequestsService],
    })
], WebsiteManualRequestsModule);
//# sourceMappingURL=website_manual_requests.module.js.map