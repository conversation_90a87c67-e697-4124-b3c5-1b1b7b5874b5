import { Repository } from 'typeorm';
import { WebsiteManualRequest } from './website_manual_requests.entity';
import { CreateWebsiteManualRequestDto } from './dto/create-website-manual-request.dto';
import { EmailService } from '../email/email.service';
import { S3bucketService } from '../s3bucket/s3bucket.service';
export declare class WebsiteManualRequestsService {
    private readonly websiteManualRequestRepository;
    private readonly emailService;
    private readonly s3bucketService;
    constructor(websiteManualRequestRepository: Repository<WebsiteManualRequest>, emailService: EmailService, s3bucketService: S3bucketService);
    createManualRequest(createDto: CreateWebsiteManualRequestDto): Promise<WebsiteManualRequest>;
    getAllManualRequests(): Promise<WebsiteManualRequest[]>;
    getManualRequestById(id: string): Promise<WebsiteManualRequest>;
    private sendManualRequestEmail;
    resendEmail(id: string): Promise<WebsiteManualRequest>;
    getFailedEmailRequests(): Promise<WebsiteManualRequest[]>;
}
