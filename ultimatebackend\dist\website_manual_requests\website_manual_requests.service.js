"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebsiteManualRequestsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const website_manual_requests_entity_1 = require("./website_manual_requests.entity");
const email_service_1 = require("../email/email.service");
const s3bucket_service_1 = require("../s3bucket/s3bucket.service");
let WebsiteManualRequestsService = class WebsiteManualRequestsService {
    constructor(websiteManualRequestRepository, emailService, s3bucketService) {
        this.websiteManualRequestRepository = websiteManualRequestRepository;
        this.emailService = emailService;
        this.s3bucketService = s3bucketService;
    }
    async createManualRequest(createDto) {
        try {
            const manualRequest = this.websiteManualRequestRepository.create(createDto);
            const savedRequest = await this.websiteManualRequestRepository.save(manualRequest);
            if (createDto.s3_url || createDto.file_url) {
                await this.sendManualRequestEmail(savedRequest);
            }
            return savedRequest;
        }
        catch (error) {
            console.error('Error creating manual request:', error);
            throw new common_1.InternalServerErrorException('Failed to create manual request');
        }
    }
    async getAllManualRequests() {
        try {
            return await this.websiteManualRequestRepository.find({
                order: { created_at: 'DESC' },
            });
        }
        catch (error) {
            console.error('Error fetching manual requests:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch manual requests');
        }
    }
    async getManualRequestById(id) {
        try {
            const request = await this.websiteManualRequestRepository.findOne({
                where: { id },
            });
            if (!request) {
                throw new common_1.NotFoundException(`Manual request with ID ${id} not found`);
            }
            return request;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            console.error('Error fetching manual request by ID:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch manual request');
        }
    }
    async sendManualRequestEmail(request) {
        try {
            const subject = `Manual Request: ${request.manual_interest} - ${request.first_name} ${request.last_name}`;
            const emailBody = `
        <h2>New Manual Request Received</h2>
        <p>A new manual request has been submitted with the following details:</p>

        <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Name:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.first_name} ${request.last_name}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Job Title:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.job_title}</td>
          </tr>
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Company:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.company}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Email:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.email}</td>
          </tr>
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Manual Interest:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.manual_interest}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Request Date:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.created_at.toLocaleDateString()}</td>
          </tr>
        </table>

        ${request.s3_url ? `<p><strong>S3 File URL:</strong> ${request.s3_url}</p>` : ''}
        ${request.file_url ? `<p><strong>File URL:</strong> ${request.file_url}</p>` : ''}

        <p>Please find the requested file attached to this email.</p>

        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          This email was automatically generated from the Website Manual Request system.
        </p>
      `;
            const fileUrls = [];
            if (request.s3_url) {
                fileUrls.push(request.s3_url);
            }
            if (request.file_url && request.file_url !== request.s3_url) {
                fileUrls.push(request.file_url);
            }
            await this.emailService.sendEmailWithAttachments({
                to: [request.email],
                subject,
                body: emailBody,
                fileUrls,
            });
            await this.websiteManualRequestRepository.update(request.id, {
                email_sent: true,
                email_sent_at: new Date(),
                email_error: null,
            });
            console.log(`Manual request email sent successfully to: ${request.email}`);
        }
        catch (error) {
            console.error('Error sending manual request email:', error);
            await this.websiteManualRequestRepository.update(request.id, {
                email_sent: false,
                email_error: error.message || 'Unknown error occurred while sending email',
            });
        }
    }
    async resendEmail(id) {
        try {
            const request = await this.getManualRequestById(id);
            if (!request.s3_url && !request.file_url) {
                throw new common_1.InternalServerErrorException('No file URLs available for this request');
            }
            await this.sendManualRequestEmail(request);
            return await this.getManualRequestById(id);
        }
        catch (error) {
            console.error('Error resending email:', error);
            throw new common_1.InternalServerErrorException('Failed to resend email');
        }
    }
    async getFailedEmailRequests() {
        try {
            return await this.websiteManualRequestRepository.find({
                where: { email_sent: false },
                order: { created_at: 'DESC' },
            });
        }
        catch (error) {
            console.error('Error fetching failed email requests:', error);
            throw new common_1.InternalServerErrorException('Failed to fetch failed email requests');
        }
    }
};
exports.WebsiteManualRequestsService = WebsiteManualRequestsService;
exports.WebsiteManualRequestsService = WebsiteManualRequestsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(website_manual_requests_entity_1.WebsiteManualRequest)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        email_service_1.EmailService,
        s3bucket_service_1.S3bucketService])
], WebsiteManualRequestsService);
//# sourceMappingURL=website_manual_requests.service.js.map