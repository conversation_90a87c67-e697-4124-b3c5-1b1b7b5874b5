import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import rateLimit from 'express-rate-limit';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import { ValidationPipe } from '@nestjs/common';
const port = process.env.PORT || 5000;

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Set global prefix first
  app.setGlobalPrefix('api');

  // Swagger UI setup
  const config = new DocumentBuilder()
    .setTitle('Ultimate Outsourcing LTD APIs')
    .setDescription('Ultimate Outsourcing LTD APIs')
    .setVersion('1.0')
    .addTag('Ultimate Outsourcing LTD')
    .addServer('http://localhost:5001/api', 'Development server')
    .addServer('https://your-production-domain.com/api', 'Production server')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      defaultModelsExpandDepth: 0, // Hides schemas/models
      defaultExpandedDepth: 0, // Keeps all controllers closed
      docExpansion: 'none',
    },
  });

  // Trust the first proxy
  app.set('trust proxy', 1);

  app.useGlobalPipes(new ValidationPipe(
    {
      whitelist: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    },
  ))
  

  // app.use(
  //   rateLimit({
  //     windowMs: 15 * 60 * 1000, // 15 minutes
  //     max: 1000, // limit each IP to 1000 requests per windowMs
  //   }),
  // );

  // Increase payload size limits
  app.use(bodyParser.json({ limit: '100mb' }));
  app.use(bodyParser.urlencoded({ limit: '100mb', extended: false }));
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  console.log("server started on port", port);

  await app.listen(port, '0.0.0.0');
}
bootstrap();
